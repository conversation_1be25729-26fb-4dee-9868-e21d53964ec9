# Athena SQL

## ROW_NUMBER() function to Rank rows and then pick from the ranked rows 

account_dse table in S3 maintains change history of the rows in the account_dse table. The ROW_NUMBER() function with OVER clause is used to assign row_numbers to records based on partitioned set of columns.

After the row_number has been assigned, the select is used to limit the rows to only the first row in the partition
```
with account_dse_ranked as (
  SELECT
    *,
    ROW_NUMBER() OVER(PARTITION BY accountid ORDER BY cast(updatedatyear as int) desc, cast(updatedatmonth as int) desc, cast(updatedatday as int) desc ) AS row_number
    
  FROM <customer>_prod.account_dse
)
SELECT * from account_dse_ranked WHERE row_number = 1;

```

## Projection Partitioning in Athena

Projection Partitioning in Glue/Athena is simpler and cost effective than the Glue Crawler partitioning approach that we are using now in archiver/snowflake load process.

We should switch the Archive External Tables to Projection Partitioning and Turn off the Crawler that runs as part of the Vision teams archiver/snowflake load process.

We can make sure Athena only reads as much data as it needs for a particular query by partitioning our data. We do this by storing the data files in a Hive folder structure that represents the patitions we’ll use in our queries.

```
s3://mybucket/data/year=2021/month=06/day=27/file1.json
s3://mybucket/data/year=2021/month=06/day=27/file2.json
s3://mybucket/data/year=2021/month=06/day=28/file1.json
```

We can then create a table partitioned by the keys used in the folder structure.

```
CREATE EXTERNAL TABLE example (
    foo string,
    bar string,
    baz string
)
PARTITIONED BY (year int, month int, day int)
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
LOCATION 's3://mybucket/data/'
```

We then need to tell Athena about the partitions. We can either do this with ALTER TABLE example ADD PARTITION (year='2021, month=06, day=27);, or by running MSCK REPAIR TABLE example;, which will crawl the folder structure and add any partitions it finds. Once the partitions are loaded we can query the data, restricting the query to just the required partitions:

```
SELECT * FROM example
WHERE year=2021 AND month=6 AND day=27
```

The problem with this is that we either need to know every about every partition before we can query the data, or repair the table to make sure our partitions are up to date - a process that will take longer and longer to run as our table grows.

There is a better way! By using partition projection we can tell Athena where to look for partitions. At query time, if the partition doesn’t exist, the query will just return no rows for that partition. Queries should also be faster when there are a lot of partitions, since Athena doesn’t need to query the metadata store to find them.

```
CREATE EXTERNAL TABLE example (
    foo string,
    bar string,
    baz string
)
PARTITIONED BY (year int, month int, day int)
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
LOCATION 's3://mybucket/data/'
TABLEPROPERTIES (
    'projection.enabled' = 'true',
    'projection.year.type' = 'integer',
    'projection.year.range' = '2020,2021',
    'projection.month.type' = 'integer',
    'projection.month.range' = '1-12',
    'projection.day.type' = 'integer',
    'projection.day.range' = '1-31',
    'storage.location.template' = 's3://mybucket/data/${year}/${month}/${day}/'
)
```
We can query this table immediately, without needing to run ADD PARTITION or REPAIR TABLE, since Athena now knows what partitions can exist. Since we need to provide Athena with the range of expected values for each key, the year partition range will eventually need to be updated to keep up with new data.

Another option is to project an actual date partition. This time we treat the date path in S3 (yyyy/MM/dd) as a single partition key, which Athena will read and convert to a date field. We call this partition date_created as date is a reserved keyword.

```
CREATE EXTERNAL TABLE example (
    foo string,
    bar string,
    baz string
)
PARTITIONED BY (date_created string)
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
LOCATION 's3://mybucket/data/'
TABLEPROPERTIES (
    'projection.enabled' = true
    'projection.date_created.type' = 'date',
    'projection.date_created.format' = 'yyyy/MM/dd',
    'projection.date_created.interval' = '1',
    'projection.date_created.interval.unit' = 'DAYS',
    'projection.date_created.range' = '2021/01/01,NOW',
    'storage.location.template' = 's3://mybucket/data/${date_created}/'
)
```

With a date partition we no longer need to update the partition ranges. Using NOW for the upper boundary allows new data to automatically become queryable at the appropriate UTC time. We can also now use the date() function in queries and Athena will still find the required partitions to limit the amount of data read.


```
SELECT * FROM example
WHERE date_created >= date('2021-06-27')
```

For more examples on projection partitioning - see external table definitions in
```
genzymeus_prod_current.ext_sales_snsob_ad_core_wk;
genzymeus_prod_current.ext_sales_snsob_as_core_wk;

sanofius_prod_current.ext_sales_akt_nbrx_data;
sanofius_prod_current.ext_sales_akt_trxnrx_data;
```
Also see [this ticket](https://aktana.atlassian.net/browse/DO-696)


## Maintaining External Table Definitions in DBT

dbt is a transformation workflow that helps you get more work done while producing higher quality results. 

DBT core framework supports creating SQL transforms (either views or create table as) from source tables to target tables leveraging data warehousing tools such as Athena, Snowflake, Redshift etc.

There is an package called [dbt-external-tables](https://github.com/dbt-labs/dbt-external-tables) that helps build and external source tables which is used as starting point in the transforms. The released package does not support Athena. However there is a pull-request to add [this feature](https://github.com/dbt-labs/dbt-external-tables/pull/133).

To support this we have defined a local external package in the kpi-reporting-dbt repo under dbt_sub_projects/dbt_external_tables with the pull request applied.

For examples for the external table definitions, please see sources.yml under models/staging/.. folders in kpi-reporting-dbt repo

To create the external tables, the following syntax can be used

```
dbt run-operation stage_external_sources --vars='{"customer":"genzymeus","ext_full_refresh": true}' 

```


## Making JSON files and text queryable in Athena

Athena supports extracting and querying JSON files as if they are flattened extracts.

Given JSON file(s) stored in a S3 bucket, we can define an external table as shown below to to read the JSON object.


### Example
Each measurement scenario is represented by a JSON file as defined below. The JSON is pretty formatted for explanatory purposes. Athena requires that this JSON is represented as a single line without line breaks.

```
{
    "uid": "default",
    "name": "Default Scenario",
    "type": "pilot",
    "period_definitions": [
        {
            "uid": "Pilot Period",
            "name": "Pilot Period",
            "yearquarter_begin": "2022-Q2",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2022-04-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Before Pilot",
            "name": "Before Pilot",
            "yearquarter_begin": "2021-Q4",
            "yearquarter_end": "2022-Q1",
            "dt_begin": "2021-10-01",
            "dt_end": "2022-03-31"
        },
        {
            "uid": "LY Pilot Period",
            "name": "LY Pilot Period",
            "yearquarter_begin": "2021-Q2",
            "yearquarter_end": "2021-Q3",
            "dt_begin": "2021-04-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Analysis Period",
            "name": "Analysis Period",
            "yearquarter_begin": "2021-Q1",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2021-01-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Conversion Period",
            "name": "Conversion Period",
            "yearquarter_begin": "2022-Q2",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2021-04-15",
            "dt_end": "2022-09-30"
        }
    ],
    "repaccountassignment_begindate": "2022-01-01",
    "repaccountassignment_asofdate": "2022-09-22",
    "interaction_events": [
        "KPISEND_ANY-COMPLETED",
        "KPIVISIT-COMPLETED"
    ],
    "usecase_level_interactions": [
        "KPISEND_ANY-COMPLETED",
        "KPIVISIT-COMPLETED"
    ],
    "topic_level_interactions": [],
    "conversion_events": [
        "KPISTATECHANGE-TIER"
    ],
    "analyze_driven_events": true,
    "analyze_aligned_events": true,
    "factorusecasename_map_override": [
        {
            "factor_name": "304: Change in Initiations--Biologic NBRx",
            "usecase_name": "Market Data Response"
        },
        {
            "factor_name": "311: Change in Initiations--Steroid NBRx",
            "usecase_name": "Market Data Response"
        }
    ],
    "remove_same_state_transitions": true,
    "pilot_rep_predicate": "usr.is_aktana_user and usr.suggestion_active_month_count > 5",
    "control_rep_predicate": " NOT usr.is_aktana_user",
    "excluded_rep_predicate": "usr.is_aktana_user and usr.suggestion_count < 100 ",
    "sales_filedate": "20221129",
    "sales_interaction_offset_months": -2,
    "sales_filedate_offset_weeks": -6
}

```
Assuming the above JSON (folded into a single line) is stored in a file default_genzymeus.json, this json file can be copied over into an s3 bucket as shown below:

```
aws s3 cp default_genzymeus.json s3://aktana-bdp-genzymeus/prod/tempdata/MSRScenario/default.json
```

More scenarios can be stored in a new line in the same file or can be copied over into the s3 bucket location with different filename.

Once the files have been created, an external Athena table can be defined as shown below pointing to the s3 bucket 

```
   create external table genzymeus_prod_current.param_msrscenario (
        uid string,
        name string,
        type string,
        period_definitions array<struct<uid:string,name:string,yearquarter_begin:string,yearquarter_end:string,dt_begin:string,dt_end:string>>,
        repaccountassignment_begindate string,
        repaccountassignment_asofdate string,
        interaction_events array<string>,
        usecase_level_interactions array<string>,
        topic_level_interactions array<string>,
        conversion_events array<string>,
        analyze_driven_events boolean,
        analyze_aligned_events boolean,
        factorusecasename_map_override array<struct<factor_name:string,usecase_name:string>>,
        remove_same_state_transitions boolean,
        pilot_rep_predicate string,
        control_rep_predicate string,
        excluded_rep_predicate string,
        sales_filedate string,
        sales_filedate_offset_weeks integer,
        sales_interaction_offset_months integer
    )
    row format SERDE 'org.openx.data.jsonserde.JsonSerDe'
    location 's3://aktana-bdp-genzymeus/prod/tempdata/MSRScenario'
```

With the above table defined the table can queried and joined with other Athena tables.

## Exploding JSON Arrays into Rows

Once the JSON is represented as a column in a table, a common requirement is to explode a JSON Array inside the JSON column into separate rows. Assuming a table as shown above, the following view explodes the period_definitions into separate rows.

Note the cross join unnest (period_definitions) t(prd) that enables exploding JSON arrays into separate rows and access the elements using prd. syntax.

```
create or replace view
    impact_genzymeus_satya.param_msrscenario_period_definition_v
  as
select uid as msrscenariouid, name msrscenarioname, prd.uid period_type, prd.name period_name, prd.yearquarter_begin, prd.yearquarter_end,
date_parse(prd.dt_begin, '%Y-%m-%d') as dt_begin, date_parse(prd.dt_end, '%Y-%m-%d') as dt_end
from genzymeus_prod_current.param_msrscenario
cross join unnest (period_definitions) t(prd);

```

## Creating complex objects such as array and struct using SQL

To create array from SQL select use array_agg
To create a struct from SQL select use Row(...) and cast it to a Row(...) data type

```
create or replace view
    impact_genzymeus_prod.imp_event_monthly_interactions
  as
select accountuid, i.productuid as productuid, date_trunc('month', eventdatetimeutc) as event_date,
       array_agg(
            cast(
                    Row(eventtypename, coalesce(usecasename,'UNKNOWN'), suggestiondriver) 
                    as 
                    Row(eventtypename varchar, usecasename varchar, suggestiondriver varchar)
                )
        )  as interaction_usecases,
       count(distinct externalid) event_count
       from
       impact_genzymeus_prod.kpi_event_interaction CROSS JOIN UNNEST(product_details) AS t(i)
       where eventtypename like '%-COMPLETED'
       group by accountuid, i.productuid, date_trunc('month', eventdatetimeutc)
```








