Welcome to your new dbt project!

### Using the starter project

To run the pipeline:

#### Pre-requisties: 
- Initialize the data: Use the rundeck job for the pipeline at <> to load the additional data from RDS that is not part of archive_rds

#### Steps to run: 
- Set the following env variables (replace values for CUSTOMER, REGION, SCHEMA_SUFFIX and AWS_REGION as needed).  Usually, it is not required to change SCHEMA_PREFIX and ENVIRONMENT variables

    - export CUSTOMER=biogeneu2
    - export REGION=useks
    - export AWS_REGION=us-east-1
    - export SCHEMA_SUFFIX=dev
    - export SCHEMA_PREFIX=impact
    - export ENVIRONMENT=prod
    - Note: SCHEMA_SUFFIX allows separate copies of the output even while reading inputs from prod (referred by ENVIRONMENT variable).  For dev purposes, export SCHEMA_SUFFIX=sankar

- From the repo top-level folder (which has profiles.yml),  try running the following commands:

    - To setup the external sources:
        - dbt seed --vars='{"customer":"'$CUSTOMER'","region":"'$REGION'","is_multicountry":"false","ext_full_refresh": true}' 
        - dbt run-operation stage_external_sources --vars='{"customer":"'$CUSTOMER'","region":"'$REGION'","is_multicountry":"false","ext_full_refresh": true}' 
            - Optionally, use the args parameter with select filter to stage a subset of sources: e.g. --args "select: manually_maintained.param_msrscenario"

    - To run the pipeline:
        - dbt run --vars='{"customer":"'$CUSTOMER'","region":"'$REGION'","is_multicountry":"false","ext_full_refresh": true}' 
            - Optionally, add the select param to execute a subset of models: e.g. --select msrscenario_strategic_coverage_v

#### Dev

- dbt run-operation stage_external_sources --vars='{"customer":"biogeneu2","ext_full_refresh": true}' --target=biogeneu2dev

- dbt run-operation stage_external_sources --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=genzymeusdev
- dbt seed --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false"}' --target=genzymeusdev
- dbt run --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false"}' --target=genzymeusdev
- dbt test --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false"}' --target=genzymeusdev

- dbt run-operation stage_external_sources --vars='{"customer":"genzymeus","ext_full_refresh": true}' --target=genzymeusdev --args "select: staging.external_data.salesdata"

dbt run-operation stage_external_sources --vars='{"customer":"abbottus","ext_full_refresh": true, "customized_models":"abbottus_external_sales_weekly_indicationaccountproduct,abbottus_sales_weekly_indicationaccountproduct"}' --target=abbottusdev --args "select: staging.external_data.salesdata"

dbt run --vars='{"customer":"abbottus","ext_full_refresh": true, "customized_models":"abbottus_external_sales_weekly_indicationaccountproduct,abbottus_sales_weekly_indicationaccountproduct"}' --target=abbottusdev --args "select: staging.external_data.salesdata"

dbt run --vars='{"customer":"'$CUSTOMER'","region":"'$REGION'","is_multicountry":"false","ext_full_refresh": true,"customized_models":"external_sales_weekly_indicationaccountproduct,sales_weekly_indicationaccountproduct"}' --target=abbottusdev --select abbottus_external_sales_weekly_indicationaccountproduct abbottus_sales_weekly_indicationaccountproduct external_sales_weekly_indicationaccountproduct sales_weekly_indicationaccountproduct sales_monthly_indicationaccountproduct +kpi_event_sales_data

- dbt run-operation stage_external_sources --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=sanofiusdev
- dbt seed --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev
- dbt run --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev
- dbt test --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev

- dbt run-operation stage_external_sources --vars='{"customer":"pfizerus","ext_full_refresh": true}' --target=pfizerusdev --args "select: staging.external_data.salesdata"

- dbt seed --vars='{"customer":"pfizerus"}' --target=pfizerusdev
- dbt run --vars='{"customer":"pfizerus"}' --target=pfizerusdev

- dbt run-operation stage_external_sources --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=sanofiusdev


dbt run --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true"}' --target=biogeneu2dev -m sparkdserun_v sparkdserunaccount_v sparkdserunaccountrep_v sparkdserunconfigfactor_v sparkdserunconfigfactortag_v sparkdserunrepdate_v sparkdserunrepdatesuggestion_v sparkdserunrepdatesuggestiondetail_v sparkdserunrepdatesuggestionreason_v

dbt run --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true"}' --target=biogeneu2dev -m imp_call_monthly_interactions

dbt docs generate --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true"}' --target=biogeneu2dev 


dbt run --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false"}' --target=genzymeusdev -m cta_messages_recently_used_v
 


dbt run --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusprod -m msrscenario_sales_indacctprdrep

dbt run --vars='{"region":"useks"}' --target=usdev
dbt run-operation stage_external_sources --vars='{"region":"useks","ext_full_refresh": true}' --target=usdev

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m staging.manually_maintained.asof

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m staging.manually_maintained.msrscenario.param_msrscenario_period_definition_v

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m staging.archive_data.repaccountassignment_cdc_v+

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m staging.manually_maintained.repengagement.rpt_rep_engagement_segmentation_v+

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m marts.impact.msrscenario_repaccountassignment_util_v+

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m marts.impact.imp_all_monthly_interactions_dtl+

dbt run --vars='{"customer":"genzymeus"}' --target=genzymeusprod -m staging.archive_data.repaccountassignment_cdc_v+

dbt compile --vars='{"region":"useks"}' --target=usdev -m accountcount_analysis.sql


dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod -m interactions_call_daily_v

dbt run --vars='{"customer":"sanofius"}' --target=sanofiusdev -m interactions_call_daily_v


dbt run --vars='{"customer":"biogeneu2"}' --target=biogeneu2dev -m eventtype_v



dbt run --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev -m +marts.impact.msrscenario_all_monthly_interactions_dtl


dbt run --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true"}' --target=biogeneu2dev -m eventtype_v


dbt run --vars='{"customer":"devgenzymeus","region":"devuseks","is_multicountry":"false","src_env":"dev"}' --target=devgenzymeus 

dbt run --vars='{"customer":"devnovartisbr","region":"devuseks","is_multicountry":"false","src_env":"prod"}' --target=devnovartisbrprod -m +kpi_data_calculation_v +strategy_performance +kpi_summary_calculation

dbt seed --vars='{"customer":"devnovartisbr","region":"devuseks","is_multicountry":"false","src_env":"prod"}' --target=devnovartisbrprod -m +strategy_performance 


dbt run --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev -m msrscenario_activity_acctprdrep


dbt seed --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev 


dbt seed --vars='{"customer":"devgenentechca","region":"devuseks","is_multicountry":"false","src_env":"dev"}' --target=devgenentechcaprod --select strategy_audience_base_v


dbt seed --vars='{"customer":"devbiogenna","region":"devuseks","is_multicountry":"false","src_env":"dev"}' --target=devbiogennadev 


dbt run --vars='{"customer":"sanofius","region":"useks","is_multicountry":"false"}' --target=sanofiusdev -m intermediate.suggestion_delv_factor.suggestion_delv_monthly_count


dbt run --vars='{"region":"us"}' --target=usdev -m suggestiontypecount_by_month


dbt seed --vars='{"customer":"novartisde","region":"eueks","is_multicountry":"false","ext_full_refresh": true}' --target=novartiseudev

dbt seed --vars='{"customer":"novartisbr","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisbrdev


dbt run-operation stage_external_sources --vars='{"customer":"leoeu","region":"eueks","is_multicountry":"true","ext_full_refresh": true}' --target=leoeudev

dbt seed --vars='{"customer":"leoeu","region":"eueks","is_multicountry":"true","ext_full_refresh": true}' --target=leoeuprod


dbt run-operation stage_external_sources --vars='{"customer":"pfizerus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=pfizerusdev

param_msrscenario_driven_conditions_v

dbt run-operation stage_external_sources --vars='{"customer":"novartisjp","region":"jpeks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisjpdev
-
dbt seed --vars='{"customer":"novartisde","region":"eueks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisdedev

dbt run-operation stage_external_sources --vars='{"customer":"novartisus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisusdev


dbt run --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=genzymeusdev -m product_v --target=genzymeusdev

dbt run --vars='{"customer":"novartisus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisusdev -m suggestion_candidates_v+

dbt run-operation stage_external_sources --vars='{"customer":"novartisuk","region":"eueks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisukdev


dbt run-operation stage_external_sources --vars='{"customer":"novartiscn","region":"jpeks","is_multicountry":"false","ext_full_refresh": true}' --target=novartiscndev


dbt run --vars='{"customer":"novartisjp","region":"jpeks","is_multicountry":"false","ext_full_refresh": true}' --target=novartisjpdev -m msrscenario_sales_indacctprdrep

dbt run --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true","ext_full_refresh": true}' --target=biogeneu2dev -m interactions_call_daily_v

dbt run --vars='{"customer":"biogeneu2","region":"useks","is_multicountry":"true","ext_full_refresh": true}' --target=biogeneu2dev -m cpa_repaccount_monthly


dbt run-operation stage_external_sources --vars='{"customer":"genzymeus","region":"useks","is_multicountry":"false","ext_full_refresh": true}' --target=genzymeusdev



#### Production

- dbt run-operation stage_external_sources --vars='{"customer":"genzymeus","ext_full_refresh": true}' --target=genzymeusprod
- dbt seed --vars='{"customer":"genzymeus"}' --target=genzymeusprod
- dbt run --vars='{"customer":"genzymeus"}' --target=genzymeusprod
- dbt test --vars='{"customer":"genzymeus"}' --target=genzymeusprod

- dbt run-operation stage_external_sources --vars='{"customer":"sanofius","ext_full_refresh": true}' --target=sanofiusprod
- dbt seed --vars='{"customer":"sanofius"}' --target=sanofiusprod
- dbt run --vars='{"customer":"sanofius"}' --target=sanofiusprod
- dbt test --vars='{"customer":"sanofius"}' --target=sanofiusprod


### Organization of Models

#### Source Categories
- archive_data
    - Data from rds archived by the Archiver job
- manually_maintained
    - Data that is not currently archived, but needs to be in S3
- engine_generated
    - Suggestion Candidates generated by Candidate Generator
- external_data
    - Raw data from customer archived into S3 (e.g salesdata)

#### Layers
- staging 
    - Only layer allowed to access Sources directly
    - No Joins
    - Only filtering, column renaming and cast
- intermediate
    - Standardized data applicable for many data marts
- marts
    - Vertical models supporting one usecases such as impact, attribution etc.


### Customers Added

- genzymeus
- sanofius

### New Customer Steps

- Sales Data Standardization
    - Files Re-Organized in S3
    - Create transformations in staging/external_data/salesdata
    - Create transformations in intermediate/normalized_salesdata

- Map custom fields to standard reporting fields in macro 
    - build_custom_fields.sql
        - User object
        - RepAccountAssignment Object

- Manually Maintained Data into s3
    - AKT_Accounts copy from stage schema in RDS
    - AKT_ProductCatalog copy from stage schema RDS
    - AKT_ProductMap for sales product id to detail product id mapping
    - AKT_MessageToMessageTopicMap to map message ids to message topic
    - Rep Engagement Data from stage schema

- Identify State Changes for Conversion Event Generation
    - Attribute or Metric Field to track conversion overtime
    - Update models/intermediate/statechange_detection/accountproduct_tier_interpolated.sql

- Populate measurement scenario json
    - msrscenariouid
    - period definitions
    - interaction events
        - usecase_level_interactions
        - topic_level_interactions
    - conversion events
    - analyze_driven_events (true/false)
    - analyze_aligned_events (true/false)
    - remove_same_state_transitions (true/false)
    - repaccountassignment_asofdate (date in YYYY-MM-DD)

- Identify Control/Pilot Reps and Control/Pilot HCPs by Scenario

### Metrics Available

  - Metrics Available By Rep
    - first date of suggestion
    - last date of suggestion
    - active month count
    - nbrdays active
    - suggestion count
    - config_count
    - seconfigname
    - repteamid
  - Metrics Available For All Users
    - pilot_call_activity_count
    - before_pilot_call_activity_count
    - lastyear_pilot_call_activity_count
    - pilot_email_activity_count
    - before_pilot_email_activity_count
    - lastyear_pilot_email_activity_count
    - analysis_call_activity_count
    - analysis_email_activity_count
    - pilot_period_territory_count

  - Normalized Monthly Sales Data with
    Current Aligned Reps
    Historically Aligned Reps
    Call Interactions
    Email Interactions
    Interaction Events 

  - By Measurement Scenarios
  - Impact
    - Use Case Mix
    - Suggestion Adoptions
    - Action Counts by Pilot/Control
    - Action Overlay with Sales Metrics (offsetted)

  - Interaction Journey with Conversions

  - Attribution of Interactions to Conversions
    first_interaction
    last_interaction
    first_suggestion
    last_suggestion
    markov_chain

# Multi-Touch Attribution

Customers buy Aktana solution to generate suggestions to actors to execute sales/marketing actions.
A key Aktana hypothesis is that these suggestions when delivered with enough context and timeliness would be adopted by the actor and the activity (action) will be executed. Another, key Aktana hypothesis is that an activity that is executed effectively would lead Aktana Customers achieving their sales/marketing objectives. Each objective attainment can be measured using one or more KPIs. Although, Aktana can effectively measure the adoption of suggestions (engagement) and can measure the effectiveness of a particular activity (HCP Engagement), measuring the impact of the collection of actions on the KPIs is a challenge. In other words, it is difficult to know how to assign credit where the credit is due. Since customers ultimately measure the effectiveness of the Aktana solution by looking at the KPIs related to their sales/marketing objectives, Aktana has to show the impact of the actions on the sales/marketing KPIs to actually demonstrate the value of Aktana solution to the customer. With multi-touch attribution, credit can be assigned in a variety of ways, but at a high-level, it's typically done using one of two methods: heuristic or data-driven.

- Broadly speaking, heuristic methods are rule-based and consist of both single-touch and multi-touch approaches. Single-touch methods, such as first-touch and last-touch , assign credit to the first channel, or the last channel, associated with a conversion. Multi-touch methods, such as linear and time-decay , assign credit to multiple channels associated with a conversion. In the case of linear, credit is assigned uniformly across all channels, whereas for time-decay, an increasing amount of credit is assigned to the channels that appear closer in time to the conversion event.

- In contrast to heuristic methods, data-driven methods determine assignment using probabilites and statistics. Examples of data-driven methods include Markov Chains and SHAP .

## Data standardization for data-driven multi-touch attribution

The suggestions are generated based on a Strategy Configuration that enumerates the actions, audiences and the corresponding trigger conditions that are tagged with the ultimate objective. Example of such tags are Market Response, Digitical Coordination, Data Collection etc.. Such tags (also called usecases) can be used to categorize the suggestions and the actions (in case the action was generated based on the suggestion). Actions that cannot be attributed to a suggestion can broadly be assigned to Other bucket. Messages used during the action may also be used to categorize the action if such data (Message to Topic mapping) is collected. To standardize the process of data-driven measurement, all actions will be represented as Events with an event type and associated dimensions and date/time of the activity.

The ultimate KPI to measure the impact is the reported sales as measured by TRX and NBRX values reported. However, there could be other KPIs such as count of journey stage conversions or count of segment changes that could be used to measure the objectives. To standardize the process of data-driven measurement, we will convert any of these KPIs into a Conversion Event with the associated dimensions and date/time of the conversion. Currently, a conversion can be measured as any change in an Account Attribute or Account Product Metric from one month to another.

The interaction events and conversion events can be transformed and normalized from the archives. The example of such events is shown below. This standardization is standardized to execute against any customer archive data

<table>
<tr>
<th>
accountuid
</th>
<th>
productuid
</th>
<th>
actoruid
</th>
<th>
messagetopic
</th>
<th>
eventtypename
</th>
<th>
usecasename
</th>
<th>
suggestiondriver
</th>
<th>
eventdatetime
</th>
<th>
isconversionevent
</th>
<th>
product_details
</th>
</tr>
</table>

## Data preparation for data-driven multi-touch attribution

To isolate the time period for the analysis and enable running the attribution at different levels of granularity of the interaction events and conversion events, we have introduced the notion of a measurement scenario. Measurement scenario defines the scope of a multi-touch attribution analysis. The following attributes define a measurement scenario:

- scenario uid
- scenario name
- scenario type
- period_definitions
  - uid
  - name
  - yearquarter_begin
  - yearquarter_end
  - dt_begin
  - dt_end
- interaction_events
- conversion_events
- usecase_level_interactions
- topic_level_interactions
- factorname to usecasename mapping overrides

```
### Sample Measurement Scenario Definition
{
    "uid": "default",
    "name": "Default Scenario",
    "type": "pilot",
    "period_definitions": [
        {
            "uid": "Pilot Period",
            "name": "Pilot Period",
            "yearquarter_begin": "2022-Q2",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2022-04-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Before Pilot",
            "name": "Before Pilot",
            "yearquarter_begin": "2021-Q4",
            "yearquarter_end": "2022-Q1",
            "dt_begin": "2021-10-01",
            "dt_end": "2022-03-31"
        },
        {
            "uid": "LY Pilot Period",
            "name": "LY Pilot Period",
            "yearquarter_begin": "2021-Q2",
            "yearquarter_end": "2021-Q3",
            "dt_begin": "2021-04-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Analysis Period",
            "name": "Analysis Period",
            "yearquarter_begin": "2021-Q1",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2021-01-01",
            "dt_end": "2022-09-30"
        },
        {
            "uid": "Conversion Period",
            "name": "Conversion Period",
            "yearquarter_begin": "2022-Q2",
            "yearquarter_end": "2022-Q3",
            "dt_begin": "2021-04-15",
            "dt_end": "2022-09-30"
        }
    ],
    "repaccountassignment_asofdate": "2022-09-22",
    "interaction_events": [
        "KPISEND_ANY-COMPLETED",
        "KPIVISIT-COMPLETED"
    ],
    "usecase_level_interactions": [
        "KPISEND_ANY-COMPLETED",
        "KPIVISIT-COMPLETED"
    ],
    "topic_level_interactions": [],
    "conversion_events": [
        "KPISTATECHANGE-TIER"
    ],
    "analyze_driven_events":true,
    "analyze_aligned_events":true,
    "factorusecasename_map_override":[
      {"factor_name":"304: Change in Initiations--Biologic NBRx", "usecase_name":"Market Data Response"},
      {"factor_name":"311: Change in Initiations--Steroid NBRx", "usecase_name":"Market Data Response"}
    ],
    "remove_same_state_transitions":true
}
```

Using the scenario definition and the event definition table, transforms are executed to transform the event data into chronological sequence of tokens for each account product and a marker to identify if such sequence of actions led to a conversion. The result of this event sequence transformation is a table as shown below. 

<table>
<tr>
<th>
msrscenariouid
</th>
<th>
accountuid
</th>
<th>
productuid
</th>
<th>
path
</th>
<th>
first_interaction
</th>
<th>
last_interaction
</th>
<th>
conversion
</th>
<th>
event_order
</th>
<th>
interaction_count
</th>
<th>
suggestion_count
</th>
</tr>
</table>

There are four steps to take when using Markov Chains to calculate attribution:

- Step 1: Construct all transistions from the paths above
- Step 2: Construct a transition probablity matrix
- Step 3: Calculate the total conversion probability
- Step 4: Use the removal effect to calculate attribution

Transitions are every unique state change from one action event to another event (action or conversion). 
As the name suggests, a transition probability matrix is a matrix that contains the probabilities associated with moving from one state to another state. This is calculated using the data from all available paths. With this matrix in place, we can then easily calculate the total conversion probability, which represents, on average, the likelihood that a given HCP/Product will experience a conversion event. Lastly, we use the total conversion probability as an input for calculating the removal effect for each channel. The way that the removal effect is calculated is best illustrated with an example as shown below.

<img src="file:///Users/<USER>/dbt/impact_measurement/example_step1.jpeg"/>

<img src="file:///Users/<USER>/dbt/impact_measurement/example_step2.jpeg"/>

### Step 1 - Identify Transitions
Identify the number of transitions that have happened in the path from one channel to another channel, by ignoring any transistions to the same channel to create a table as shown below:

TODO: Any eventypename that appears repetitively in a path will be folded into a single action


<table>
<tr>
<th>
msrscenariouid
</th>
<th>
accountuid
</th>
<th>
productuid
</th>
<th>
idx
</th>
<th>
first_interaction
</th>
<th>
last_interaction
</th>
<th>
conversion
</th>
<th>
interaction_count
</th>
<th>
suggestion_count
</th>
<th>
transition
</th>
<th>
cnt
</th>
</tr>
</table>



### Step 2 - Calculate Transition Probability 

Finally create a transition matrix to calculate the transition probability associated with transition from start_state to the end_state as follows. Note that this table contains all the state transitions from one interaction state to another state. One of the transitioned states could end up as a Conversion or in other cases may not result in a Conversion.

<table>
<tr>
<th>
msrscenariouid
</th>
<th>
start_state
</th>
<th>
end_state
</th>
<th>
transition_probability
</th>
</tr>
</table>

Using this transition probability matrix table

### Step 3 - Calculate Total Conversion Probability

Calculate the total conversion probability which represents, on average, the likelihood that a given HCP/Product will experience a conversion event.

### Step 4 - Calculate Removal Effect to Calculate Attribution

Such actions are valued by the Aktana engine at the time suggestions are generated using the expected value function which ideally would include components that are linked to the sales/marketing KPI's. However such KPI's are driven by multiple actions over a period of time.

With many suggestions getting delivered that in turn leading to actions, measuring the effectiveness of the suggestions is a challenge.

### Resources:

- Learn about Athena SQL [tips and tricks](athena_sql.md)
- Learn about [grouping sets, rollup and cube] [https://www.ibm.com/docs/en/db2/11.5?topic=clause-examples-grouping-sets-cube-rollup]
- Cheatsheet [in the docs] [https://datacaffee.com/dbt-data-built-tool-commands-cheat-sheet/]
- Learn more about dbt [in the docs](https://docs.getdbt.com/docs/introduction)
- Check out [Discourse](https://discourse.getdbt.com/) for commonly asked questions and answers
- Join the [chat](https://community.getdbt.com/) on Slack for live discussions and support
- Find [dbt events](https://events.getdbt.com) near you
- Check out [the blog](https://blog.getdbt.com/) for the latest news on dbt's development and best practices



select date_trunc('quarter', now()) , date_trunc('year', now())  , quarter(now()), year(now())

11800
11800
 5900
 5900
 5900
 5900