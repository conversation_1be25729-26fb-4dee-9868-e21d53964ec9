#!/bin/bash
set -x

cd "$(dirname "$0")"
pwd

echo "Copying mapping file..."
./utils/eventtype_mapping/cp_eventtypemapping.sh $CUSTOMER

CUSTOM_MODELS=$(python ./generate_variables.py $CUSTOMER 2>&1)

if [ -z "$SCHEMA_SUFFIX" ]; then
    echo "SCHEMA_SUFFIX not defined, using ENVIRONMENT..."
    export SCHEMA_SUFFIX="$ENVIRONMENT"
fi

rc=0
if [ "$1" = "DBT_RUN_OPERATION" ]; then
    param_file_exists=`aws s3 ls s3://aktana-bdp-$CUSTOMER/$ENVIRONMENT/tempdata/MSRScenario/default.json | wc -l | sed 's/ //g'`
    if [ $param_file_exists != "1" ]; then
        echo "Copying default parameter json file to s3://aktana-bdp-$CUSTOMER/$ENVIRONMENT/tempdata/MSRScenario/default.json"
        aws s3 cp ./utils/msrscenarios/default_default.json s3://aktana-bdp-$CUSTOMER/$ENVIRONMENT/tempdata/MSRScenario/default.json
    fi
    dbt deps
    dbt seed --full-refresh
    shift
    dbt run-operation "$@"
    rc=$?

elif [ "$1" = "DBT_REFRESH_TABLES" ]; then
    dbt deps
    shift
    SOURCES_TO_REFRESH=$1
    shift
    if [ "$SOURCES_TO_REFRESH" = "sources=" ]; then
        dbt run-operation "$@"
    else
        source_tables_to_refresh=${SOURCES_TO_REFRESH:8}
        dbt run-operation "$@" --args={"select: $source_tables_to_refresh"}
    fi
    rc=$?

elif [ "$1" = "DBT_RUN" ]; then
    dbt deps
    shift
    dbt-fal flow "$@"
    rc=$?

elif [ "$1" = "DBT_RUN_MULTIPLE" ]; then
    dbt deps
    shift
    IFS=';' read -ra CMD_ARGS <<< "$@"
    fal_models1="${CMD_ARGS[0]}"
    run_models="${CMD_ARGS[1]}"
    fal_models2="${CMD_ARGS[2]}"
    rest="${CMD_ARGS[3]/:custom_models_string:/"$CUSTOM_MODELS"}"

    echo "Fal models1:" $fal_models1
    echo "Run  models:" $run_models
    echo "Fal models2:" $fal_models2
    echo "Remaining  args:" $rest

    rc=0
    if [ ! -z "$fal_models1" ]; then
        echo "Building first set of fal models using:" "$rest" $fal_models1
        dbt-fal flow run "$rest" $fal_models1
        rc=$?
    fi

    if [ $rc -eq 0 ] && [ ! -z "$run_models" ]; then
        echo "Building dbt run models using:"  "$rest" $run_models
        dbt run "$rest" $run_models
        rc=$?
    fi

    if [ $rc -eq 0 ] && [ ! -z "$fal_models2" ]; then
        echo "Building last set of fal models using:"  "$rest" $fal_models2
        dbt-fal flow run "$rest" $fal_models2
        rc=$?
    fi

elif [ "$1" = "DBT_FAL" ]; then
    dbt deps
    shift
    dbt-fal flow "$@"
    rc=$?

else
    "$@"
    rc=1
fi

exit $rc