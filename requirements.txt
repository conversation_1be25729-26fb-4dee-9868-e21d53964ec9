# Spark
pyspark==3.3.1
delta-spark==2.2.0
findspark==1.4.2
pyarrow==10.0.1
# AWS
s3fs==2023.4.0
fsspec==2023.4.0
boto3~=1.26.76
botocore==1.29.76
aiobotocore==2.5.0
# dbt
dbt-core==1.5.1
dbt-spark==1.5.1
dbt-snowflake==1.5.1
dbt-athena-community==1.5.1
awswrangler==3.3.0
py4j==0.10.9.5
markupsafe==2.1.1
Werkzeug==2.3.6
dbt-fal[athena]==1.5.7
pyathena==3.0.6
# top2vec
top2vec==1.0.29
top2vec[sentence_encoders]
openai==0.27.8
beautifulsoup4==4.12.2
# utils
prometheus-client==0.13.1
statsd==3.3.0
numpy==1.23.4
pandas==1.3.5
# db connectors
mysql-connector-python==8.0.21
PyMySQL==1.0.3
snowflake-connector-python==3.0.2
snowflake-sqlalchemy==1.4.7
SQLAlchemy==1.4.49
sshtunnel==0.4.0
pyOpenSSL==23.1.0
paramiko==3.1.0
urllib3==1.26.15
geopy==2.2.0
joblib~=1.1.0
# learning 
xgboost==1.7.3
holidays==0.11.1
scikit-learn==1.2.0
qgrid==1.3.1
sqldf==0.4.2
tableauserverclient==0.17.0
tabulate==0.9.0
shap==0.41.0
lightgbm==3.3.5
sklearn2pmml==0.85.0
sklearn-pmml-model==1.0.1
tensorflow~=2.13
keras~=2.13
requests-oauthlib==1.3.0
requests~=2.28.1
scipy==1.10.0
seaborn==0.11.0
regex==2022.10.31
numpydoc==1.1.0
pandleau==0.4.1
pandocfilters==1.5.0
matplotlib==3.7.1
graphviz==0.20.1
cryptography==40.0.1
imbalanced-learn==0.11.0
# Incompatible
#delta-lake-reader==0.2.13
#dbt-athena-adapter==1.0.1
#pandas==1.5.3 #incompatible with awswrangler 2.20

