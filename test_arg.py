import argparse


parser = argparse.ArgumentParser()
parser.add_argument("--target", required = True)
parser.add_argument("--schema", required = True)

args, unknown = parser.parse_known_args()

arg_dict = {}
for arg in vars(args):
    if getattr(args, arg):
        arg_dict[arg] = getattr(args, arg)

if unknown:
    for i in range(0, len(unknown), 2):
        key, value = unknown[i][2:], unknown[i+1]
        arg_dict[key] = value

print(arg_dict)

# args = parser.parse_args()
# print(args)

# arg_dict = {}
# if args.arg:
#     for i in range(0, len(args.arg), 2):
#         key, value = args.arg[i], args.arg[i+1]
#         arg_dict[key] = value

# print(arg_dict)