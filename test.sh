#!/bin/bash
set -x

# Initialize variables
target=""
directory=""
env=""
profile=""

# Process command line arguments
while [[ $# -gt 0 ]]
do
key="$1"

case $key in
    --target)
    value="$2"
    target="$value"
    shift 
    ;;
    --directory)
    value="$2"
    directory="$value"
    shift 
    ;;
    --env)
    value="$2"
    env="$value"
    shift 
    ;;
    *)
    echo "Error: Unknown argument $key"
    exit 1
    ;;
esac
shift 
done

# Check if necessary arguments exist
if [[ -z "$target" || -z "$directory" || -z "$env" ]]; then
    echo "Error: Missing necessary arguments"
    exit 1
fi

# Print results
echo "target=$target"
echo "directory=$directory"
echo "env=$env"


sed -e "s/{{target}}/$target/g" -e  > profiles.yml


run --vars='{"customer":"sanofius","region":"us"}' --select account_cs_v
-e CUSTOMER=$CUSTOMER -e REGION=$REGION -e ENVIRONMENT=$ENVIRONMENT -e SCHEMA_PREFIX='impact'