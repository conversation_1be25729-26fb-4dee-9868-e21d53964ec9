# Setup

conda env list
conda create --name python38 python=3.8
conda activate python38
conda install -c anaconda ephem
conda install -c conda-forge pystan
conda install -c conda-forge fbprophet
pip3 install --upgrade plotly
pip3 install seaborn
pip3 install openpyxl


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from fbprophet import Prophet
from fbprophet.plot import add_changepoints_to_plot
data = pd.read_excel('/Users/<USER>/Downloads/DDDD.xlsx')
#data = pd.read_csv('https://raw.githubusercontent.com/facebook/prophet/main/examples/example_wp_log_peyton_manning.csv')


dfProphet=data.drop(columns=[ 'Customer','SalesItem','SalesTransactionID','SalesValue'])
dfProphet['SalesDate'] = pd.to_datetime(dfProphet['SalesDate'])
dfProphet = dfProphet.set_index('SalesDate')
dfProphet.head()
dfMonthly = dfProphet.resample('MS').sum()
#M stands for end of month, MS start of month, D daily and w weekly 
dfMonthly.head()
dfMonthlyProph = dfMonthly.reset_index().dropna()
dfMonthlyProph.columns = ['ds', 'y']
dfMonthlyProph.head()
fig = plt.figure(facecolor='w', figsize=(20, 6))
plt.plot(dfMonthlyProph.ds, dfMonthlyProph.y)

m = Prophet(seasonality_mode='additive',interval_width=0.95).fit(dfMonthlyProph) 
#additive vs multiplicative seasonality, 95% confidence interval
future = m.make_future_dataframe(periods=12, freq='MS')
# vs just periods=365 
fcst = m.predict(future)
fig = m.plot(fcst)

#from fbprophet.plot import plot_plotly
#import plotly.offline as py
#py.init_notebook_mode()
#fig = plot_plotly(m,fcst)
#fig.show(renderer="colab")

fig2 = m.plot_components(fcst)
fig3 = m.plot(fcst)
change_points = add_changepoints_to_plot(fig3.gca(), m, fcst)

change_points = add_changepoints_to_plot(fig3.gca(), m, forecast)
df.loc[df["ds"].isin(m.changepoints)]


import logging.config
import time
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
#from pyathena import connect
import pandas as pd
import awswrangler as wr
import getopt, sys, os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from fbprophet import Prophet
from fbprophet.plot import add_changepoints_to_plot


df = wr.s3.read_parquet(path="s3://aktana-bdp-genzymeus/prod/dbt/impact/download/sales_weekly/")

import pandas as pd
from fbprophet import Prophet

data = pd.read_excel('/Users/<USER>/Downloads/DDDD.xlsx')
gData = data.groupby(['SalesItem', 'SalesDate'])['SalesAmount'].sum()
itemlist = gData.SalesItem.unique()

gData = df.groupby(['indication', 'accountuid', 'productuid', 'sale_date'])['sales_value_trx'].sum()
gDim = gData.groupby(['indication', 'accountuid', 'productuid']).count();

#new_df = pd.merge(gData, gDim,  how='left', left_on=['indication','accountuid','productuid'], right_on = ['indication','accountuid','productuid'])

gDim = gDim.reset_index()
gDim_filtered = gDim.query('sales_value_trx > 60')
gData = gData.reset_index();


m = Prophet()
fcst_all = pd.DataFrame()  # store all forecasts here
for index, row in gDim_filtered.head(1).iterrows():
    print(row['indication'], row['accountuid'], row['productuid'])
    temp = gData.query('indication == ' + "'" + row['indication'] + "' and " + 'accountuid == ' + "'" + row['accountuid'] + "' and " + 'productuid == ' + "'" + row['productuid'] + "'")
    temp = temp.drop(columns=[ 'indication', 'accountuid', 'productuid' ])
    temp['sale_date'] = pd.to_datetime(temp['sale_date'])
    temp = temp.set_index('sale_date')
    d_df = temp.resample('MS').sum()
    d_df = d_df.reset_index().dropna()
    d_df.columns = ['ds', 'y']
    try:
        p = Prophet(seasonality_mode='additive',yearly_seasonality=True,weekly_seasonality=False,interval_width=0.95,changepoint_range=0.95);
        #p.add_seasonality(name='monthly', period=30.5, fourier_order=5)        
        m = p.fit(d_df)
        future = m.make_future_dataframe(periods=3, freq='MS')
    except ValueError:
        pass
    fcst = m.predict(future)     
    fprefix = "plot_" + row['indication'] + "_" + row['accountuid'] + "_" + row['productuid']        
    fig1 = m.plot(fcst)    
    fig2 = m.plot_components(fcst)    
    fig3 = m.plot(fcst)    
    change_points = add_changepoints_to_plot(fig3.gca(), m, fcst)    
    fig1.savefig(fprefix + "_fcst.png")
    fig2.savefig(fprefix + "_comp.png")
    fig3.savefig(fprefix + "_chg.png")
    fcst['indication'] = row['indication']
    fcst['accountuid'] = row['accountuid']
    fcst['productuid'] = row['productuid']
    fcst['Fact'] = d_df['y'].reset_index(drop = True)    
    fcst_all = pd.concat((fcst_all, fcst))



m = Prophet()
fcst_all = pd.DataFrame()  # store all forecasts here
for x in itemlist:
    temp = gData[gData.SalesItem == x]
    temp = temp.drop(columns=[ 'SalesItem'])
    temp['SalesDate'] = pd.to_datetime(temp['SalesDate'])
    temp = temp.set_index('SalesDate')
    d_df = temp.resample('MS').sum()
    d_df = d_df.reset_index().dropna()
    d_df.columns = ['ds', 'y']
    try:
        m = Prophet(seasonality_mode='additive',interval_width=0.95).fit(d_df)
        future = m.make_future_dataframe(periods=12, freq='MS')
    except ValueError:
        pass
    fcst = m.predict(future)
    fcst['SalesItem'] = x
    fcst['Fact'] = d_df['y'].reset_index(drop = True)
    fcst_all = pd.concat((fcst_all, fcst))
    print(x)

fcst_all.to_excel('ProphetFc.xlsx', index=False)    