with monthly_count as (
    select msrscenariouid, date_trunc('month', eventdatetimeutc) yearmonth,   
    count(*) as monthly_interaction_count
    from {{ref('msrscenario_events')}} where msrscenariouid =  'default'  and
    eventdatetimeutc >= date '2022-07-01'
    group by msrscenariouid, date_trunc('month', eventdatetimeutc)
),
match_count as (
select msrscenariouid, date_trunc('month', eventdatetimeutc) yearmonth,   
       case when is_candidate_usecase = 1 then 'candidate_matched' when is_candidate_usecase = 0 and internalsuggestionreferenceid is not null then 'suggestion_matched' else 'nomatch' end match_type, count(*) as interaction_count
from {{ref('msrscenario_events')}} 
where msrscenariouid = 'default' and eventdatetimeutc >= date '2022-07-01'
group by msrscenariouid, date_trunc('month', eventdatetimeutc), case when is_candidate_usecase = 1 then 'candidate_matched' when is_candidate_usecase = 0 and internalsuggestionreferenceid is not null then 'suggestion_matched' else 'nomatch' end
)
select '{{var('customer')}}' customer, s.msrscenariouid, s.yearmonth, s.match_type, s.interaction_count, m.monthly_interaction_count,
round((s.interaction_count*1.0/m.monthly_interaction_count*1.0)*100.0, 2) interaction_pct
from match_count s inner join monthly_count m on s.msrscenariouid =  m.msrscenariouid and s.yearmonth  = m.yearmonth
order by s.msrscenariouid, s.yearmonth, s.match_type 
-- 
