
# HEY! This file is used in the dbt-external-tables integrations tests with CircleCI.
# You should __NEVER__ check credentials into version control. Thanks for reading :)

config:
    send_anonymous_usage_stats: False
    use_colors: True

integration_tests:
  target: postgres
  outputs:

    redshift:
      type: redshift
      host: "{{ env_var('REDSHIFT_TEST_HOST') }}"
      user: "{{ env_var('REDSHIFT_TEST_USER') }}"
      pass: "{{ env_var('REDSHIFT_TEST_PASS') }}"
      dbname: "{{ env_var('REDSHIFT_TEST_DBNAME') }}"
      port: "{{ env_var('REDSHIFT_TEST_PORT') | as_number }}"
      schema: dbt_external_tables_integration_tests_redshift
      threads: 1
      
    snowflake:
      type: snowflake
      account: "{{ env_var('SNOWFLAKE_TEST_ACCOUNT') }}"
      user: "{{ env_var('SNOWFLAKE_TEST_USER') }}"
      password: "{{ env_var('SNOWFLAKE_TEST_PASSWORD') }}"
      role: "{{ env_var('SNOWFLAKE_TEST_ROLE') }}"
      database: "{{ env_var('SNOWFLAKE_TEST_DATABASE') }}"
      warehouse: "{{ env_var('SNOWFLAKE_TEST_WAREHOUSE') }}"
      schema: dbt_external_tables_integration_tests_snowflake
      threads: 1

    bigquery:
      type: bigquery
      method: service-account
      keyfile: "{{ env_var('BIGQUERY_SERVICE_KEY_PATH') }}"
      project: "{{ env_var('BIGQUERY_TEST_DATABASE') }}"
      schema: dbt_external_tables_integration_tests_bigquery
      threads: 1

    databricks:
      type: spark
      method: odbc
      port: 443
      driver: "{{ env_var('ODBC_DRIVER') }}"
      host: "{{ env_var('DBT_DATABRICKS_HOST_NAME') }}"
      endpoint: "{{ env_var('DBT_DATABRICKS_ENDPOINT') }}"
      token: "{{ env_var('DBT_DATABRICKS_TOKEN') }}"
      schema: dbt_external_tables_integration_tests_databricks

    synapse:
      type: synapse
      driver: "ODBC Driver 17 for SQL Server"
      port: 1433
      host: "{{ env_var('DBT_SYNAPSE_SERVER') }}.sql.azuresynapse.net"
      database: "{{ env_var('DBT_SYNAPSE_DB') }}"
      authentication: CLI
      schema: dbt_external_tables_integration_tests_synapse
      threads: 1

    azuresql:
      type: sqlserver
      driver: "ODBC Driver 17 for SQL Server"
      port: 1433
      host: "{{ env_var('DBT_AZURESQL_SERVER') }}"
      database: "{{ env_var('DBT_AZURESQL_DB') }}"
      authentication: CLI
      schema: dbt_external_tables_integration_tests_azuresql
      threads: 1
