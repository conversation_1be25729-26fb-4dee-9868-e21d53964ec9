
name: 'dbt_external_tables_integration_tests'
version: '1.0'

profile: 'integration_tests'

config-version: 2

model-paths: ["models"]
analysis-paths: ["analysis"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]

target-path: "target"
clean-targets:
  - "target"
  - "dbt_packages"

dispatch:
  - macro_namespace: dbt_external_tables
    search_order: ['dbt_external_tables_integration_tests', 'dbt_external_tables']

seeds:
  +quote_columns: false

sources:
  dbt_external_tables_integration_tests:
    plugins:
      redshift:
        +enabled: "{{ target.type == 'redshift' }}"
      snowflake:
        +enabled: "{{ target.type == 'snowflake' }}"
      bigquery:
        +enabled: "{{ target.type == 'bigquery' }}"
      spark:
        +enabled: "{{ target.type == 'spark' }}"
      synapse:
        +enabled: "{{ target.type == 'synapse' }}"
      azuresql:
        +enabled: "{{ target.type == 'sqlserver' }}"

tests:
  dbt_external_tables_integration_tests:
    plugins:
      redshift:
        +enabled: "{{ target.type == 'redshift' }}"
      snowflake:
        +enabled: "{{ target.type == 'snowflake' }}"
      bigquery:
        +enabled: "{{ target.type == 'bigquery' }}"
      spark:
        +enabled: "{{ target.type == 'spark' }}"
      synapse:
        +enabled: "{{ target.type == 'synapse' }}"
      azuresql:
        +enabled: "{{ target.type == 'sqlserver' }}"
