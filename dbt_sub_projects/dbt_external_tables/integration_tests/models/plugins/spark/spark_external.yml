version: 2

sources:
  - name: spark_external
    schema: "{{ target.schema }}"
    loader: S3

    tables:
      
      - name: people_csv_unpartitioned_using
        external: &csv-people-using
          location: "s3://dbt-external-tables-testing/csv/"
          using: csv
          options: &csv-people-options
            sep: ','
            header: 'true'
        columns: &cols-of-the-people
          - name: id
            data_type: int
          - name: first_name
            data_type: string
          - name: last_name
            data_type: string
          - name: email
            data_type: string
        tests: &equal-to-the-people
          - dbt_utils.equality:
              compare_model: ref('people')
              compare_columns:
                - id
                - first_name
                - last_name
                - email

      - name: people_csv_partitioned_using
        external:
          <<: *csv-people-using
          partitions: &parts-of-the-people
            - name: section
              data_type: string
        columns: *cols-of-the-people
        tests: *equal-to-the-people

# ----- TODO: hive format
        
#      - name: people_csv_unpartitioned_hive_format
#        external: &csv-people-hive
#          location: "s3://dbt-external-tables-testing/csv/"
#          row_format: delimited fields terminated by ','
#          file_format: textfile
#          tbl_properties: "('skip.header.line.count': 1)"
#        columns: *cols-of-the-people
#        
#      - name: people_csv_partitioned_hive_format
#        external:
#          <<: *csv-people-hive
#          partitions: *parts-of-the-people
#        columns: *cols-of-the-people

# ----- TODO: json

#      - name: people_json_unpartitioned_using
#        external: &json-people-using
#          location: "s3://dbt-external-tables-testing/json/"
#          using: json
#        columns: *cols-of-the-people
#        tests: *equal-to-the-people
#        
#      - name: people_json_partitioned_using
#        external:
#          <<: *json-people-using
#          partitions: *parts-of-the-people
#        columns: *cols-of-the-people
#        tests: *equal-to-the-people
