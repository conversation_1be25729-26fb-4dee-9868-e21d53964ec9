#--------------------------------------------------------
#Target (change this to target tests need to run against)
#--------------------------------------------------------
dbt_target=postgres

#--------------------------------------------------------
#PostgreSQL (dbt_target: postgres)
#--------------------------------------------------------
POSTGRES_TEST_HOST=localhost
POSTGRES_TEST_USER=root
POSTGRES_TEST_PASSWORD=password
POSTGRES_TEST_PORT=5432
POSTGRES_TEST_DB=dbt

#--------------------------------------------------------
#Redshift (dbt_target: redshift)
#--------------------------------------------------------
REDSHIFT_TEST_HOST=
REDSHIFT_TEST_USER=
REDSHIFT_TEST_PASS=
REDSHIFT_TEST_DBNAME=
REDSHIFT_TEST_PORT=

#--------------------------------------------------------
#Snowflake (dbt_target: snowflake)
#--------------------------------------------------------
SNOWFLAKE_TEST_ACCOUNT=
SNOWFLAKE_TEST_USER=
SNOWFLAKE_TEST_PASSWORD=
SNOWFLAKE_TEST_ROLE=
SNOWFLAKE_TEST_DATABASE=
SNOWFLAKE_TEST_WAREHOUSE=

#--------------------------------------------------------
#BigQuery (dbt_target: bigquery)
#--------------------------------------------------------
BIGQUERY_TEST_PROJECT=
BIGQUERY_SERVICE_KEY_PATH=