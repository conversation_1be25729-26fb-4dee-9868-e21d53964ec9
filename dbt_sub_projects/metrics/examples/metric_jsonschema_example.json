{"title": "dbt_metric_file", "type": "object", "required": ["name", "label", "timestamp", "time_grains", "calculation_method", "expression"], "additionalProperties": false, "properties": {"name": {"type": "string"}, "label": {"type": "string"}, "timestamp": {"type": "string"}, "time_grains": {"type": "array", "items": {"type": "string"}}, "calculation_method": {"type": "string"}, "expression": {"type": "string"}, "dimensions": {"type": "array", "items": {"type": "string"}}}}