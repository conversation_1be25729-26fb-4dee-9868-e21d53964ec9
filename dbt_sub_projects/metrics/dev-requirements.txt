pytest
pytest-dotenv

# Bleeding edge
# git+https://github.com/dbt-labs/dbt-core.git@main#egg=dbt-tests-adapter&subdirectory=tests/adapter
# git+https://github.com/dbt-labs/dbt-core.git@main#egg=dbt-core&subdirectory=core
# git+https://github.com/dbt-labs/dbt-core.git@main#egg=dbt-postgres&subdirectory=plugins/postgres
# git+https://github.com/dbt-labs/dbt-redshift.git
# git+https://github.com/dbt-labs/dbt-snowflake.git
# git+https://github.com/dbt-labs/dbt-bigquery.git
# git+https://github.com/databricks/dbt-databricks.git

# Most recent stable release
# dbt-tests-adapter
# dbt-core
# dbt-redshift
# dbt-snowflake
# dbt-bigquery
# dbt-databricks

# Most recent release candidates
dbt-tests-adapter==1.5.0-rc1
dbt-core==1.5.0-rc1
dbt-redshift==1.5.0-rc1
dbt-snowflake==1.5.0-rc1
dbt-bigquery==1.5.0-rc1
# dbt-databricks==1.5.0-rc1
