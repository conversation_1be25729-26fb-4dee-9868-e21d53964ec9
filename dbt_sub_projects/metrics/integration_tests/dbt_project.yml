# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: "dbt_metrics_integration_tests"
version: "1.0.0"
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: "dbt_metrics_integration_tests"

model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"
clean-targets:
  - "target"
  - "dbt_packages"
  - "logs"

models:

  dbt_metrics_integration_tests:

    metric_testing_models:
      +materialized: table

    materialized_models:
      +materialized: table

vars:
  dbt_metrics_calendar_model: custom_calendar
  custom_calendar_dimension_list: ["is_weekend"]
