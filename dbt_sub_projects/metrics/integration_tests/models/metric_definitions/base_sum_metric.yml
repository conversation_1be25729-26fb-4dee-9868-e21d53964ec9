version: 2 
metrics:
  - name: base_sum_metric
    model: ref('fact_orders')
    label: Order Total ($)
    timestamp: order_date
    time_grains: [day, week, month]
    calculation_method: sum
    expression: order_total
    dimensions:
      - had_discount
      - order_country
    # config:
    #   restrict_no_time_grain: True

  - name: base_sum_metric_duplicate
    model: ref('fact_orders_duplicate')
    label: Order Total ($)
    timestamp: order_date
    time_grains: [day, week, month]
    calculation_method: sum
    expression: order_total
    dimensions:
      - had_discount
      - order_country

  - name: base_sum_metric__14_day_window
    model: ref('fact_orders')
    label: Order Total ($)
    timestamp: order_date
    time_grains: [day, week, month]
    calculation_method: sum
    expression: order_total
    window:
      count: 14
      period: month
    dimensions:
      - had_discount
      - order_country

  - name: base_test_metric
    model: ref('fact_orders')
    label: Order Total ($)
    timestamp: order_date
    time_grains: [day, week, month] 
    calculation_method: sum
    expression: order_total
    dimensions:
      - had_discount
      - order_country
  
  - name: base_sum_metric__no_timestamp
    model: ref('fact_orders')
    label: Order Total ($)
    calculation_method: sum
    expression: order_total
    dimensions:
      - had_discount
      - order_country