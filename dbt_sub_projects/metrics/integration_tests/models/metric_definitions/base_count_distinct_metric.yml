version: 2 

metrics:
  - name: base_count_distinct_metric
    model: ref('fact_orders')
    label: Count Distinct
    timestamp: order_date
    time_grains: [day, week, month]
    calculation_method: count_distinct
    expression: customer_id
    dimensions:
      - had_discount
      - order_country
    window:
      count: 14
      period: month
    filters:
      - field: had_discount
        operator: '='
        value: 'true'
      - field: order_country
        operator: '='
        value: "'CA'"