-- depends_on: {{ ref('strategy_performance') }}
-- depends_on: {{ ref('fiscalperiod_country_normalized') }}

{{
    config(
        materialized='incremental',
        unique_key =['strategyid','fiscalperiodid','countrycode'],
        is_external=false,
        s3_data_naming='table_unique',
        format='parquet',
        write_compression='GZIP',
        table_type='iceberg',
        incremental_strategy = 'merge',
        on_schema_change = 'sync_all_columns'
    )
}}


{% set query %}
        SELECT frequency,countrycode FROM {{ref('fiscalperioddefinition_country_normalized')}} v where isDefault = true;
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}

with 
{% for row in results.rows %}
periods_{{row[1]}} as (
  select fiscalperiodid,countrycode from  {{ ref('fiscalperiod_country_normalized') }}  v 
where v.startdate <= CURRENT_DATE
  and v.frequency = '{{ row[0] }}'
  and v.countrycode = '{{ row[1] }}'
order by v.startdate desc limit 2
),
 {% endfor %}
periods as (
   {% for row in results.rows %}
select * from periods_{{row[1]}}
{% if not loop.last %} UNION ALL {% endif %}
{% endfor %}
)
select sp.strategyid,
       sp.fiscalperiodid,
       sp.countrycode,
       sp.audiencesize,
       sp.audiencereach,
       sp.audiencereachpercent,
       sp.suggestioncount,
       sp.interactioncount,
       sp.nbaadoptionpercent,
       sp.suggestion_accepted_count,
       sp.suggestion_dismissed_count, 
       sp.suggestion_inferred_accepted_count, 
       sp.suggestion_direct_accepted_count, 
       sp.suggestion_marked_completed_count,
       sp.suggestion_engagement_percent,
       cast(now() as timestamp(6)) as updatedat
    from {{ref ('strategy_performance') }} sp
{% if is_incremental() %}     
, periods cp
where sp.fiscalperiodid = cp.fiscalperiodid
  and sp.countrycode = cp.countrycode
{% endif %}

{% endif %}