-- depends_on: {{ ref('rep_data_calculation_v') }}
-- depends_on: {{ ref('fiscalperiod_country_normalized') }}
{{
    config(
        materialized='incremental',
        unique_key =['kpitypeid','fiscalperiodid','productuid', 'repuid', 'repteamuid', 'recordtype', 'datevalue', 'countrycode'],
        is_external=false,
        s3_data_naming='table_unique',
        format='parquet',
        write_compression='GZIP',
        table_type='iceberg',
        incremental_strategy = 'merge',
        on_schema_change = 'sync_all_columns'
    )
}}

{% set query %}
    SELECT frequency,countrycode FROM {{ref('fiscalperioddefinition_country_normalized')}} v where isDefault = true;
{% endset %}

{% set results = run_query(query) %}

{% if execute %}

    with 
    {% for row in results.rows %}
    periods_{{row[1]}} as (
    select fiscalperiodid,countrycode from  {{ ref('fiscalperiod_country_normalized') }}  v 
    where v.startdate <= CURRENT_DATE
    and v.frequency = '{{ row[0] }}'
    and v.countrycode = '{{ row[1] }}'
    order by v.startdate desc limit 2
    ),
    {% endfor %}
    periods as (
    {% for row in results.rows %}
    select * from periods_{{row[1]}}
    {% if not loop.last %} UNION ALL {% endif %}
    {% endfor %}
    )
    select sc.repuid,
        sc.recordtype,
        sc.datevalue,
        sc.fiscalperiodid,
        sc.countrycode,
        sc.periodname,
        sc.repteamname,
        sc.repteamuid,
        sc.territoryname,
        sc.districtname,
        sc.regionname,
        sc.productuid,
        sc.kpivalue,
        sc.kpitypeid,
        cast(now() as timestamp(6)) as updatedat
    from {{ref ('rep_data_calculation_v') }} sc
    {% if is_incremental() %}     
    , periods cp
    where sc.fiscalperiodid = cp.fiscalperiodid 
    and sc.countrycode = cp.countrycode
    {% endif %}
{% endif %}

