models:
  - name: kpi_summary_final_v
    description: kpi summary final model
    meta:
      fal:
        post-hook:
          - path: utils/kpi/kpi_table_move.py
            with:
              source_table_name: "kpi_summary_final_v"
              target_table_name: "KPISummary"
              post_load_operations: ["CREATE UNIQUE INDEX kpiSummary_unique1 ON KPISummary(fiscalPeriodId, repTeamUID, productUID, kpiTypeId, additionalSetting, strategyId,kpiId, countryCode)",
                                     "CREATE INDEX kpiSummary_idx01 ON KPISummary(fiscalPeriodId, repTeamUID, productUID, kpiTypeId, additionalSetting, strategyId, countryCode)"]
  - name: kpi_result_final_v
    description: kpi result final model
    meta:
      fal:
        post-hook:
          - path: utils/kpi/kpi_table_move.py
            with:
              source_table_name: "kpi_result_final_v"
              target_table_name: "KPIResult"
              post_load_operations: ["CREATE UNIQUE INDEX kpiResult_unique1 ON KPIResult(kpiid, kpitypeid, strategyid, fiscalperiodid, productuid, repteamuid, repuid, additionalsetting, countrycode)"]
  - name: strategy_performance_final_v
    description: strategy performance final model
    meta:
      fal:
        post-hook:
          - path: utils/kpi/kpi_table_move.py
            with:
              source_table_name: "strategy_performance_final_v"
              target_table_name: "StrategyPerformance"
              post_load_operations: ["CREATE UNIQUE INDEX strategyPerformance_unique1 ON StrategyPerformance(strategyid, fiscalperiodid)"]
  - name: rep_data_calculation_final_v
    description: rep data calculation final model
    meta:
      fal:
        post-hook:
          - path: utils/kpi/kpi_table_move.py
            with:
              source_table_name: "rep_data_calculation_final_v"
              target_table_name: "KPIRepDataCalculation"
              post_load_operations: ["CREATE UNIQUE INDEX kpirepdatacalculation_idx1 ON KPIRepDataCalculation(kpitypeid,repuid,repteamuid,productuid,recordtype,datevalue,countrycode)"]