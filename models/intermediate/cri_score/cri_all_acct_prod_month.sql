{{ config(materialized='view') }}

with cri_months_arr as (
select
sequence(date_add('month', -coalesce(params.cri_look_back_months, 24) + coalesce(params.cri_offset_months, -1), current_date), 
date_add('month', coalesce(params.cri_offset_months, -1), current_date), 
interval '1' month) months
from  {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),

all_months as (
select date_format(x, '%Y-%m') yearMonth from cri_months_arr 
cross join unnest(months) t(x)
)


select accountid, productid, yearMonth
from
(
    select distinct accountid, productid
    from {{ ref('interaction_account_product_history_v') }}

    union all

    select distinct accountid, productid
    from {{ ref('interaction_account_parent_product_history_v') }} 
) ap

cross join
(
    select yearMonth from all_months
)
