/*
Cadence score:
Find avg & stddev of days between interactions
Define cadence score as (avg + stddev) / 2
Flip the score as 1 - cadence score
use sigmoid func to scale
*/
{{ config(materialized='view') }}

with raw_interactions as (
select accountid, productid, startDateLocal, yearMonth,
date_diff('day', lag(startDateLocal) over (partition by accountid, productid order by startDateLocal), startDateLocal) days_btw_visits
from 
(select * from {{ ref('cri_interactions_filter') }}
where currentChannel = 'VISIT_CHANNEL') t
),

raw_interactions_dedup as (
select accountid, productid, startDateLocal, yearMonth,
max(days_btw_visits) days_btw_visits
from raw_interactions
group by accountid, productid, startDateLocal, yearMonth
),

raw_cadence as (
select t.*
from 
(select ri.*,
avg(days_btw_visits) over (partition by accountid, productid order by startDateLocal range between unbounded preceding and current row) visit_avg,
stddev(days_btw_visits) over (partition by accountid, productid order by startDateLocal range between unbounded preceding and current row) visit_stddev,
(avg(days_btw_visits) over (partition by accountid, productid order by startDateLocal range between unbounded preceding and current row) + stddev(days_btw_visits) over (partition by accountid, productid order by startDateLocal range between unbounded preceding and current row) ) / 2.0 cadence,
row_number() over (partition by accountid, productid, yearMonth order by startDateLocal desc) rn
from raw_interactions_dedup ri
) t
where rn = 1
),

lower_upper_bound as (
select least(2 * stddev(cadence) + avg(cadence), max(cadence)) as upper_bound,
greatest(avg(cadence) - 2 * stddev(cadence), min(cadence)) as lower_bound
from raw_cadence
)

select r.*,
case when cadence >= b.upper_bound then 0.0
when cadence <= b.lower_bound then 1.0
else (b.upper_bound - cadence) / (b.upper_bound - b.lower_bound) end as cadence_score
from raw_cadence r, lower_upper_bound b