/*
Tenure score:
Find the date of first interaction, note that if there is a gap > 365 days, first date will be reset
Then compute the date difference between first and latest (for each month) interaction 
*/
{{ config(materialized='view') }}

with raw_dates as (
select startDateLocal, accountId, productid, yearMonth,
case
    when lag(startDateLocal) over (partition by accountId, productid order by startDateLocal) is null
        or date_diff('day', lag(startDateLocal) over (partition by accountId, productid order by startDateLocal), startDateLocal) > 365
    then startDateLocal
    else min(startDateLocal) over (partition by accountId, productid)
    end as first_date
from {{ ref('cri_interactions_filter') }}
),

raw_tenure as (
select accountid, productid, yearMonth,
case when tenure > 365 then 365 else tenure end as tenure
from (
select accountid, productid, yearMonth, max(date_diff('day', first_date, startDateLocal)) tenure
from raw_dates r
group by accountid, productid, yearMonth
having max(date_diff('day', first_date, startDateLocal)) > 0   
)
),

lower_upper_bound as (
select least(3 * stddev(tenure) + avg(tenure), max(tenure)) as upper_bound,
greatest(avg(tenure) - 3 * stddev(tenure), min(tenure)) as lower_bound
from raw_tenure
)

select r.*,
case when tenure >= b.upper_bound then 1.0
when tenure <= b.lower_bound then 0.0
else (tenure - b.lower_bound) / (b.upper_bound - b.lower_bound) end as tenure_score
from raw_tenure r, lower_upper_bound b
