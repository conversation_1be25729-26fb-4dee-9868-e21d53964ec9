/*
Email open score:
First filter sent_email with last 2 years data
Then get monthly open and send count
Finally compute open score as (cumulative open count) / (cumulative send count) 
*/
{{ config(materialized='view') }}

with cri_email_range as (
select 
date_add('month', -coalesce(params.cri_look_back_months, 24) + coalesce(params.cri_offset_months, -1), current_date) start_date,
date_add('month', coalesce(params.cri_offset_months, -1), current_date) end_date
from {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),

raw_emails as (
select a.accountId accountid, a.productId productid, date_format(s.email_sent_date_vod__c, '%Y-%m') yearMonth, s.id as email_id, s.opened_vod__c
from {{ ref('interaction_account_product_history_v') }} a
join {{ ref('sent_email_vod__c_v') }} s
on a.interactionuid = s.id

union all 

select a.accountId accountid, a.productId productid, date_format(s.email_sent_date_vod__c, '%Y-%m') yearMonth, s.id as email_id, s.opened_vod__c
from {{ ref('interaction_account_parent_product_history_v') }} a
join {{ ref('sent_email_vod__c_v') }} s
on a.interactionuid = s.id


),

emails_filter as (
select accountid, productid, yearMonth, email_id, opened_vod__c
from raw_emails e, cri_email_range r
where e.yearMonth between date_format(r.start_date, '%Y-%m') and date_format(r.end_date, '%Y-%m')
),

email_month as (
select accountid, productid, yearMonth,
count(distinct email_id) sent_yearMonth, sum(opened_vod__c) open_yearMonth
from emails_filter

group by accountId, productId, yearMonth
),

raw_email_scores as (
select e.*, 
sum(sent_yearMonth) over (partition by accountId, productId order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) sent_total,
sum(open_yearMonth) over (partition by accountId, productId order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) open_total,
case when sum(sent_yearMonth) over (partition by accountId, productId order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) > 0 then
sum(open_yearMonth) over (partition by accountId, productId order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) /
cast(sum(sent_yearMonth) over (partition by accountId, productId order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) as double) 
else null
end as open_rate

from email_month e
),

lower_upper_bound as (
select least(3 * stddev(open_rate) + avg(open_rate), max(open_rate)) as upper_bound,
greatest(avg(open_rate) - 3 * stddev(open_rate), min(open_rate)) as lower_bound
from raw_email_scores
)

select r.*,
case when open_rate >= b.upper_bound then 1.0
when open_rate <= b.lower_bound then 0.0
else (open_rate - b.lower_bound) / (b.upper_bound - b.lower_bound) end as email_score
from raw_email_scores r, lower_upper_bound b