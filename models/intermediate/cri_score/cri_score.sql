{{ config(materialized='table') }}

with cri_time_range as (
select 
date_format(date_add('month', -11 + coalesce(params.cri_offset_months, -1), current_date), '%Y-%m') start_month,
date_format(date_add('month', coalesce(params.cri_offset_months, -1), current_date), '%Y-%m') end_month
from {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),

combined_scores as (
select accountid, productid, yearMonth, email_score, tenure_score, visit_score, cadence_score
from
{{ ref('cri_email_open_score') }} e

full outer join

{{ ref('cri_tenure_score') }} t
using (accountid, productid, yearMonth)

full outer join

{{ ref('cri_visit_score') }} v
using (accountid, productid, yearMonth)

full outer join 

{{ ref('cri_cadence_score') }} c
using (accountid, productid, yearMonth)

right join

{{ ref('cri_all_acct_prod_month') }}
using (accountid, productid, yearMonth)
),

cri_fill_na as (
select accountid, productid, yearMonth,
coalesce(email_score, lag(email_score) IGNORE NULLS over (partition by accountid, productid order by yearMonth)) email_score,
coalesce(tenure_score, lag(tenure_score) IGNORE NULLS over (partition by accountid, productid order by yearMonth)) tenure_score,
coalesce(visit_score, lag(visit_score) IGNORE NULLS over (partition by accountid, productid order by yearMonth)) visit_score,
coalesce(cadence_score, lag(cadence_score) IGNORE NULLS over (partition by accountid, productid order by yearMonth)) cadence_score
from combined_scores
),

all_scores as (
select accountid, productid, yearMonth,
email_score, tenure_score, visit_score, cadence_score,
cast(coalesce(email_score, 0) + coalesce(tenure_score, 0) + coalesce(visit_score, 0) + coalesce(cadence_score, 0) as double) as score_sum,
(case when email_score is null then 0 else 1 end + 
case when tenure_score is null then 0 else 1 end + 
case when visit_score is null then 0 else 1 end + 
case when cadence_score is null then 0 else 1 end)
as score_count
from cri_fill_na c
)

select c.accountid, c.productid, c.yearMonth,
c.email_score, c.tenure_score, c.visit_score, c.cadence_score,
case when score_count > 0 then score_sum / score_count else 0 end as index_score
from all_scores c, cri_time_range r
where yearMonth >= start_month and yearMonth <= end_month
