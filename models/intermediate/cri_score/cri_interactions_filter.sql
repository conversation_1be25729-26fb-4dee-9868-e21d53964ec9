{{ config(materialized='view') }}

with cri_interaction_range as (
select 
date_add('month', -coalesce(params.cri_look_back_months, 24) + coalesce(params.cri_offset_months, -1), current_date) start_date,
date_add('month', coalesce(params.cri_offset_months, -1), current_date) end_date 
from {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),

raw_interactions as (
select interactionid, accountid, productid, activityDate,  date_format(activityDate, '%Y-%m') yearMonth, currentChannel 
from 
{{ ref('interaction_account_product_history_v') }}

union all 

select interactionid, accountid, productid, activityDate,  date_format(activityDate, '%Y-%m') yearMonth, currentChannel 
from 
{{ ref('interaction_account_parent_product_history_v') }}

)

select interactionid, accountid, productid, activityDate startDateLocal, yearMonth, currentChannel, date_format(r.end_date, '%Y-%m') end_month
from raw_interactions i, cri_interaction_range r
where i.yearMonth between date_format(r.start_date, '%Y-%m') and date_format(r.end_date, '%Y-%m')