/*
Visit score:
First find visit count for each month
Compute cumulative avg of monthly visit count as visit score
*/
{{ config(materialized='view') }}

with visit_count as (
select accountid, productid, yearMonth,
count(1) visit_cnt,
max(end_month) end_month
from 
(select * from {{ ref('cri_interactions_filter') }}
where currentChannel = 'VISIT_CHANNEL') t
group by accountid, productid, yearMonth
),

min_max_month as (
select accountid, productid, 
min(yearMonth) first_month, 
max(end_month) last_month
from visit_count
group by accountid, productid
),

acct_prod_month as (
select accountid, productid, date_format(x, '%Y-%m') yearMonth
from min_max_month
cross join unnest(sequence(date_parse(first_month, '%Y-%m'), date_parse(last_month, '%Y-%m'), interval '1' month)) t(x)
),

visit_count_fill as (
select accountid, productid, yearMonth, coalesce(visit_cnt, 0) visit_cnt
from visit_count v
right join acct_prod_month c
using (accountid, productid, yearMonth)
),

raw_score as (
select v.*, 
avg(visit_cnt) over (partition by accountid, productid order by yearMonth range between UNBOUNDED PRECEDING AND CURRENT ROW) raw_visit_score
from visit_count_fill v
),

lower_upper_bound as (
select least(3 * stddev(raw_visit_score) + avg(raw_visit_score), max(raw_visit_score)) as upper_bound,
greatest(avg(raw_visit_score) - 3 * stddev(raw_visit_score), min(raw_visit_score)) as lower_bound
from raw_score
)

select r.*,
case when raw_visit_score >= b.upper_bound then 1.0
when raw_visit_score <= b.lower_bound then 0.0
else (raw_visit_score - b.lower_bound) / (b.upper_bound - b.lower_bound) end as visit_score
from raw_score r, lower_upper_bound b
