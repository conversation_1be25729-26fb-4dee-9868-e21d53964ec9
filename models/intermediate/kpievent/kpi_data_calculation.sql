-- depends_on: {{ ref('aktana_metrics_calendar') }}
-- depends_on: {{ ref('kpi_event_interaction_explode') }}
-- depends_on: {{ ref('kpi_event_interaction_factorlevel_v') }}
-- depends_on: {{ ref('kpi_event_sales_data') }}
-- depends_on: {{ ref('kpi_event_apmetric_data') }}

{{ config(materialized='table') }}

{% set kpitypes_query %}
select concat("kpitypelabel" ,':',cast ("kpitypeid" as varchar), ':', COALESCE(aggregationlevel, 'Brand')) kpitype  from {{ ref('kpitype_v')}} 
where upper(modelType) = 'EVENT' and isDeleted = false
{% endset %}

{% set default_timegrain_query %}
select lower(frequency) from {{ ref('fiscalperioddefinition_default_v') }} where isdefault = true
{% endset %}

{% set all_timegrain = ["month", "quarter", "trimester", "semester", "year"] %}
{% set startDate = build_rolling_two_years_date(1) %}

{% set results = run_query(kpitypes_query) %}
{% if execute %}
{% set kpitypes = results.columns[0].values() %}
{% else %}
{% set kpitypes = [] %}
{% endif %}

{% set results = run_query(default_timegrain_query) %}
{% if execute %}
{% set default_timegrain = results.columns[0].values() %}
{% else %}
{% set default_timegrain = [] %}
{% endif %}

with
{% for kpitypewithid in kpitypes %}
    {% set kpitype, kpitypeidval, kpitype_aggregationlevel = kpitypewithid.split(':') %}
    {% if kpitype == 'journey_progression' %}
        {% set cust_dimensions=['facilityid','facilityuid','facilityname','configcountrycode','accountid','accountuid', 'accountname', 'product_id', 'product_uid','product_name','repid','repuid','repname', 'eventlabel'] %}
    {% elif kpitype == 'calls_completed' or kpitype_aggregationlevel != "Brand" %}    
        {% set cust_dimensions=['facilityid','facilityuid','facilityname','configcountrycode','accountid','accountuid', 'accountname', 'repid','repuid','repname'] %}
    {% else %}
        {% set cust_dimensions=['facilityid','facilityuid','facilityname','configcountrycode','accountid','accountuid', 'accountname', 'product_id', 'product_uid','product_name','repid','repuid','repname'] %}
    {% endif %}

    -- we will use the default timegrain for all kpis
    {% set timegrain = default_timegrain %}


    {% for c in timegrain %}
        {{kpitype}}_{{c}} as (
            select *
            from {{ metrics.calculate(
            metric(kpitype),
            dimensions=cust_dimensions,       
            grain = (c),
            start_date=startDate
            )
        }}
        )
        {% if not loop.last %},{% endif %}
    {% endfor %}
    {% if not loop.last %},{% endif %}
{% endfor %}
select facilityid,facilityuid,facilityname,configcountrycode,
accountid,accountuid, accountname, product_id, product_uid,
product_name,repid,repuid,repname,eventlabel,messagetopic,recordtype,datevalue,kpivalue,kpitypeid from (
{% for kpitypewithid in kpitypes %}  
    {% set kpitype, kpitypeidval, kpitype_aggregationlevel = kpitypewithid.split(':') %}
    -- we will use the default timegrain for all kpis
    {% set timegrain = default_timegrain %}
    {% for c in timegrain %}
        select 
        facilityid,facilityuid,facilityname,configcountrycode,accountid,accountuid,accountname,
        {% if kpitype == "calls_completed" or kpitype_aggregationlevel != "Brand" %}
            cast(null as integer) as product_id,
            cast(null as varchar) as product_uid,
            cast(null as varchar) as product_name,
        {% else %} 
            product_id,
            product_uid,
            product_name,
        {% endif %}
        repid,repuid,repname,
        {% if kpitype == "journey_progression" %}
            eventlabel,
        {% else %} 
            cast(null as varchar) as eventlabel,  
        {% endif %}
            cast(null as varchar) as messagetopic,
        upper('{{c}}') as recordtype, date_{{c}} as datevalue,{{kpitype}} as kpivalue, 
        {{kpitypeidval}} as kpitypeid
        from {{kpitype}}_{{c}}
        {% if not loop.last %} UNION {% endif %}
    {% endfor %}
    {% if not loop.last %} UNION {% endif %}
{% endfor %}
) tt
union all
select
    facilityid,
    facilityuid,
    facilityname,
    configcountrycode,
    accountid,
    accountuid,
    accountname,
    product_id,
    product_uid,
    product_name,
    repid,
    repuid,
    repname,
    eventlabel,
    messagetopic,
    recordtype,
    datevalue,
    kpivalue, 
    kpitypeid   
from {{ ref('kpi_data_calculation_message_kpis') }}