-- depends_on: {{ ref('aktana_metrics_calendar') }}
-- depends_on: {{ ref('kpi_event_interaction_message_explode') }}

{{ config(materialized='table') }}

{% set kpitypes_query %}
select concat("kpitypelabel" ,':',cast ("kpitypeid" as varchar)) kpitype from {{ ref('kpitype_v')}} 
where upper(modelType) = 'MESSAGE_TOPIC'
{% endset %}

{% set timegrain = ["month", "quarter", "trimester", "semester", "year"] %}
{% set startDate = build_rolling_two_years_date(1) %}

{% set results = run_query(kpitypes_query) %}
{% if execute %}
{% set kpitypes = results.columns[0].values() %}
{% else %}
{% set kpitypes = [] %}
{% endif %}

with calendar as (
				select *
				from {{ ref('aktana_metrics_calendar') }}
				where date_day >= cast('{{startDate}}' as date)
			)
select
    facilityid,
    facilityuid,
    facilityname,
    configcountrycode,
    accountid,
    accountuid,
    accountname, 
    product_id,
    product_uid,
    product_name,
    repid,
    repuid,
    repname,
    eventlabel,
    messagetopic,
    recordtype,
    datevalue,
    kpivalue, 
    kpitypeid
from (
{% for kpitypewithid in kpitypes %}  
    {% set kpitype, kpitypeidval = kpitypewithid.split(':') %}
    {% for c in timegrain %}
        select
            facilityid,
            facilityuid,
            facilityname,
            configcountrycode,
            cast(null as integer) as accountid,
            cast(null as varchar) as accountuid,
            cast(null as varchar) as accountname, 
            product_id,
            product_uid,
            product_name,
            repid,
            repuid,
            repname,
            eventlabel,
            messagetopic,
            upper('{{c}}') as recordtype,
            datevalue as datevalue,
            sum(case
                    when num_rn = 1
                        {% if kpitype in ('message_topic_clm_coverage', 'message_topic_clm_explosure') %}
                     and message_doc_type = 'CLM'
                        {% elif kpitype in ('message_topic_rte_coverage', 'message_topic_rte_explosure') %} 
                     and message_doc_type = 'RTE'    
                        {% endif %}
                    then 1
                    else 0
                end)/
                {% if kpitype in ('message_topic_coverage', 'message_topic_clm_coverage', 'message_topic_rte_coverage') %} 
                nullif(sum(case when denum_rn = 1 then 1 else 0 end), 0)
                {% elif kpitype in ('message_topic_explosure', 'message_topic_clm_explosure', 'message_topic_rte_explosure') %}
                1
                {% endif %} as kpivalue, 
            {{kpitypeidval}} as kpitypeid
        from(select
                facilityid,
                facilityuid,
                facilityname,
                configcountrycode,
                product_id,
                product_uid,
                product_name,
                repid,
                repuid,
                repname,
                eventlabel,
                {% if c == "month" %}
                calendar.date_month
                {% elif c == "quarter" %}
                calendar.date_quarter
                {% elif c == "trimester" %}
                calendar.date_trimester
                {% elif c == "semester" %}
                calendar.date_semester
                {% elif c == "year" %}
                calendar.date_year
                {% endif %} as datevalue, 
                messagetopic,
                message_doc_type,
                {% if kpitype in ('message_topic_coverage',
                                  'message_topic_clm_coverage',
                                  'message_topic_rte_coverage') %}
                accountid,
                {% elif kpitype in ('message_topic_explosure',
                                    'message_topic_clm_explosure',
                                    'message_topic_rte_explosure') %}
                 interactionuid,
                {% endif %}
                row_number() over(partition by
                                                            facilityid,
                                                            facilityuid,
                                                            facilityname,
                                                            configcountrycode,
                                                            product_id,
                                                            product_uid,
                                                            product_name,
                                                            repid,
                                                            repuid,
                                                            repname,
                                                            eventlabel,
                                                            messagetopic,
                                                            {% if kpitype in ('message_topic_coverage',
                                                                              'message_topic_clm_coverage',
                                                                              'message_topic_rte_coverage') %}
                                                            accountid,
                                                            {% elif kpitype in ('message_topic_explosure',
                                                                                'message_topic_clm_explosure',
                                                                                'message_topic_rte_explosure') %}
                                                            interactionuid,
                                                            {% endif %}
                                                            {% if c == "month" %}
                                                            calendar.date_month
                                                            {% elif c == "quarter" %}
                                                            calendar.date_quarter
                                                            {% elif c == "trimester" %}
                                                            calendar.date_trimester
                                                            {% elif c == "semester" %}
                                                            calendar.date_semester
                                                            {% elif c == "year" %}
                                                            calendar.date_year
                                                            {% endif %}
                                                            order by 
                                                            {% if kpitype in ('message_topic_coverage',
                                                                              'message_topic_clm_coverage',
                                                                              'message_topic_rte_coverage') %}
                                                            accountid
                                                            {% elif kpitype in ('message_topic_explosure',
                                                                                'message_topic_clm_explosure',
                                                                                'message_topic_rte_explosure') %}
                                                            interactionuid
                                                            {% endif %}) as num_rn,
                row_number() over(partition by
                                                            facilityid,
                                                            facilityuid,
                                                            facilityname,
                                                            configcountrycode,
                                                            product_id,
                                                            product_uid,
                                                            product_name,
                                                            repid,
                                                            repuid,
                                                            repname,
                                                            eventlabel,
                                                            {% if kpitype in ('message_topic_coverage',
                                                                              'message_topic_clm_coverage',
                                                                              'message_topic_rte_coverage') %}
                                                            accountid,
                                                            {% elif kpitype in ('message_topic_explosure',
                                                                                'message_topic_clm_explosure',
                                                                                'message_topic_rte_explosure') %}
                                                            interactionuid,
                                                            {% endif %}
                                                            {% if c == "month" %}
                                                            calendar.date_month
                                                            {% elif c == "quarter" %}
                                                            calendar.date_quarter
                                                            {% elif c == "trimester" %}
                                                            calendar.date_trimester
                                                            {% elif c == "semester" %}
                                                            calendar.date_semester
                                                            {% elif c == "year" %}
                                                            calendar.date_year
                                                            {% endif %}) as denum_rn
             from {{ref('kpi_event_interaction_message_explode')}} base_model
             join calendar on cast(base_model.eventdate as date) = calendar.date_day
             where messagetopic is not null
            )
        group by
            facilityid,
            facilityuid,
            facilityname,
            configcountrycode,
            product_id,
            product_uid,
            product_name,
            repid,
            repuid,
            repname,
            eventlabel,
            messagetopic,
            datevalue
        {% if not loop.last %} UNION {% endif %}
    {% endfor %}
    {% if not loop.last %} UNION {% endif %}
{% endfor %}
) tt