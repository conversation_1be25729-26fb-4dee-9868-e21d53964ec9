{{ config(materialized='view') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with kpieventinteractionfactor  as
(
 select kpi.*,
 f.facilityid,
 f.externalid as facilityuid,
 f.facilityname,
{%- if var_multi_country == 'false' %}
    '{{ build_country() }}' as configcountrycode
{%- else %}
      COALESCE(r.configcountrycode, a.configcountrycode) as configcountrycode
{%- endif %}
  from {{ ref('kpi_event_interaction') }} kpi
  inner join {{ ref('actor_v') }} r  on kpi.repid = r.repid
  inner join {{ ref('account_dse_v') }} a on kpi.accountid = a.accountid
  inner join {{ ref('facility_v') }} f on f.facilityid = a.facilityid
  where kpi.recordType = 'Interaction-factor level'
)
  select * from kpieventinteractionfactor;