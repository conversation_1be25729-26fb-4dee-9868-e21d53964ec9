{{ config(materialized='table') }}

with interaction_key_message_delivery_event as (
     select
          t.externalid,      
          array_agg(
          CAST(ROW(
               t.actionorder,
               t.repactiontypename,
               t.productid,
               t.productuid,
               t.productname,
               t.messageid,
               t.messageuid,
               t.messagename,
               t.messagetopic,
               t.messagereaction,
               CAST(null as VA<PERSON>HA<PERSON>),
               t.duration)
          AS ROW(
               actionorder integer,
               repactiontypename <PERSON><PERSON><PERSON><PERSON>,
               productid integer,
               productuid VARCHAR,
               productname <PERSON><PERSON><PERSON><PERSON>,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               messagereaction VARCHAR,
               message_doc_type VARCHAR,
               duration integer)) order by t.actionorder) AS product_details
     from {{ ref('interaction_key_message_delivery_v') }} t
     group by
          t.externalid
),
interaction_detail_event as
(
     select 
          t.externalid,      
          array_agg(
          CAST(ROW(
               t.actionorder,
               t.repactiontypename,
               t.productid,
               t.productuid,
               t.productname,
               t.messageid,
               t.messageuid,
               t.messagename,
               t.messagetopic,
               t.messagereaction,
               CAST(null as VARCHAR),
               t.duration)
          AS ROW(
               actionorder integer,
               repactiontypename <PERSON><PERSON><PERSON><PERSON>,
               productid integer,
               productuid VARCHAR,
               productname <PERSON><PERSON><PERSON><PERSON>,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               messagereaction VARCHAR,
               message_doc_type VARCHAR,
               duration integer)) order by t.actionorder) AS product_details
     from {{ ref('interaction_detail_v') }} t
     group by
          t.externalid
),
interaction_event as
(
     select
          t.accountid,
          coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
          t.repid,
          et.eventtypeid, 
          t.accountname,
          t.repname,
          et.eventtypename,
          t.accountuid,
          t.repuid,
          t.repActionTypeId,
          t.corellationid,
          t.corellationid as interactionuid,
          t.duration, 
          t.eventdate, 
          t.eventdatetimeutc, 
          t.actiondatetime, 
          t.eventlabel, 
          t.externalid, 
          et.isconversionevent,
          case
               when kev.product_details is not null then kev.product_details
               else ev.product_details
          end as product_details ,
          ictv.call_type,
          ictv.call_channel_raw,
          ictv.call_channel_category
     from {{ ref('interaction_event_base_info_v') }} t
     inner join {{ ref('kpi_eventtype') }} et
          on concat('KPI',t.repactiontypeName,'-COMPLETED') = et.eventtypename
     left join interaction_detail_event ev
          on concat(t.corellationid, t.accountuid) = ev.externalid
     left join interaction_key_message_delivery_event kev
          on concat(t.corellationid, t.accountuid) = kev.externalid
     left join {{ ref('akt_account_segment_mapping_v') }} aasm 
          on aasm.accountId = t.accountid 
     left join {{ ref('interaction_call_type_v') }} ictv 
          on ictv.interactionuid = t.interactionuid
), 
suggestion_event_dtl as 
(
     select
          suggestionreferenceid, 
          array_agg(
          CAST(ROW(
               coalesce(actionorder, 1),
               detailrepactionname,
               productid,
               productuid,
               productname,
               messageid,
               messageuid,
               messagename,
               null,
               detailfactoruid,
               factorname,
               strategyid,
               strategyname)
          AS ROW(
               actionorder integer,
               detailrepactionname VARCHAR,
               productid integer,
               productuid VARCHAR,
               productname VARCHAR,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               factoruid VARCHAR,
               factorname VARCHAR,
               strategyid integer,
               strategyname VARCHAR)) order by coalesce(actionorder, 1)) as product_details
     from {{ ref('suggestion_factor_dtl_v') }} 
     group by
          suggestionreferenceid
),
suggestion_event_reason as 
(
     select
          suggestionreferenceid, 
          runuid,
          factoruid,
          factorname,
          min(strategyid) strategyid,
          min(strategyname) strategyname,
          min(tagname) tagname,
          array_agg(
          CAST(ROW(
               reasonrank,
               reasontext,
               crmfieldname,
               reasonsourcesystemname,
               factoruid,
               factorname,
               strategyid,
               strategyname)
          AS ROW(
               reasonrank integer,
               reasontext VARCHAR,
               crmfieldname VARCHAR,
               reasonsourcesystemname VARCHAR,
               factoruid VARCHAR,
               factorname VARCHAR,
               strategyid integer,
               strategyname VARCHAR)) order by reasonrank) as suggestion_reasons,
          grouping(factoruid) as is_factor_grouping
     from {{ ref('suggestion_factor_reason_v') }} 
     group by
          grouping sets(
               (suggestionreferenceid),
               (suggestionreferenceid, runuid, factoruid, factorname))
),
suggestion_event_dtl_factor as 
(
     select 
          t.accountid, 
          t.repid,
          t.eventtypeid,
          t.accountuid,
          t.repuid, 
          t.accountname,
          t.segmentname,
          t.repname,
          t.eventtypename,
          t.corellationid,
          t.linkedinteractionuid,
          t.suggestiondriver,
          t.suggestionreferenceid,
          t.internalsuggestionreferenceid,
          t.detailrepactionname,
          t.eventdate,
          t.eventdatetimeutc,
          t.actiondatetime, 
          t.eventlabel,
          t.externalid,
          t.isconversionevent, 
          coalesce(e.tagname, 'Channel Execution') as usecasename,
          t.actiontaken,
          t.issuggestioncompleteddirect, 
          t.issuggestioncompletedinfer, 
          e.factoruid,
          coalesce(e.factorname, 'Channel Execution') as factorname,
          e.strategyid,
          coalesce(e.strategyname, 'Undefined Strategy') as strategyname,
          t.seconfigid, 
          t.seconfigname,
          d.product_details,
          e.suggestion_reasons,
          t.detailrepactiontypeid,
          t.productid,
          t.productuid,
          t.productname,
          t.iscompleted,
          t.reportedinteractionuid
     from {{ ref('suggestion_event_base_info_v') }} t
     inner join suggestion_event_reason e
          on t.suggestionreferenceid = e.suggestionreferenceid
         and e.is_factor_grouping = 0
     inner join suggestion_event_dtl d
          on t.suggestionreferenceid = d.suggestionreferenceid
),
interaction_suggestion_event_factor_lvl as
(
     select
          a.accountid,
          a.repid,
          a.eventtypeid,
          a.accountuid,
          a.repuid,
          a.accountname,
          a.segmentname,
          a.repname,
          a.eventtypename,
          a.interactionuid,
          a.duration,
          a.eventdate,
          a.eventdatetimeutc,
          a.actiondatetime, 
          a.eventlabel,
          a.externalid as mergeid,
          a.isconversionevent,
          a.product_details,
          b.suggestionreferenceid,
          b.internalsuggestionreferenceid,
          b.linkedinteractionuid,
          b.suggestiondriver,
          b.detailrepactionname as suggesteddetailrepactionname,
          b.actiondatetime as suggestedactiondatetime,
          b.usecasename,
          b.actiontaken, 
          b.issuggestioncompleteddirect,
          b.issuggestioncompletedinfer,
          b.factoruid,
          b.factorname,
          b.strategyid,
          b.strategyname,
          b.seconfigid,
          b.seconfigname,
          b.product_details as suggestion_product_details,
          b.suggestion_reasons,
          a.repActionTypeId,
          b.detailrepactiontypeid,
          a.call_type,
          a.call_channel_raw,
          a.call_channel_category,
          b.iscompleted,
          b.reportedinteractionuid 
     from interaction_event a
     left join suggestion_event_dtl_factor b
          on a.externalid = b.corellationid
),
suggestion_interaction_event_factor_lvl as
(
     select
          a.accountid,
          a.repid,
          a.eventtypeid,
          a.accountuid,
          a.repuid,
          a.accountname,
          a.segmentname,
          a.repname,
          a.eventtypename,
          b.interactionuid,
          b.duration,
          a.eventdate,
          a.eventdatetimeutc,
          a.actiondatetime, 
          a.eventlabel,
          b.externalid as mergeid,
          a.isconversionevent,
          b.product_details, 
          a.suggestionreferenceid,
          a.internalsuggestionreferenceid,
          a.linkedinteractionuid,
          a.suggestiondriver,
          a.detailrepactionname as suggesteddetailrepactionname,
          a.actiondatetime as suggestedactiondatetime,
          a.usecasename,
          a.actiontaken, 
          a.issuggestioncompleteddirect,
          a.issuggestioncompletedinfer,
          a.factoruid,
          a.factorname,
          a.strategyid,
          a.strategyname,
          a.seconfigid,
          a.seconfigname,
          a.product_details as suggestion_product_details,
          a.suggestion_reasons,
          b.repActionTypeId,
          a.detailrepactiontypeid,
          a.productid as suggestion_productid, -- Remove
          a.productuid as suggestion_productuid, -- Remove
          a.productname as suggestion_productname, -- Remove
          b.call_type,
          b.call_channel_raw,
          b.call_channel_category,
          a.iscompleted,
          a.reportedinteractionuid
     from suggestion_event_dtl_factor a
     left join interaction_event b
          on a.corellationid = b.externalid
)
select
     'Interaction-factor level' as recordType,
     accountid,
     accountuid,
     accountname,
     cast(null as integer) as productid,
     cast(null as varchar) as productuid,
     cast(null as varchar) as productname,
     segmentname,
     repid,
     repuid,
     repname,
     cast(null as integer) messageid,
     cast(null as varchar) messageuid,
     cast(null as varchar) as messagename,
     cast(null as varchar) as messagetopic,
     eventtypeid,
     eventtypename,
     interactionuid,
     duration,
     repActionTypeId,
     detailRepActiontypeid,
     eventdate,
     eventdatetimeutc,
     actiondatetime,
     eventlabel,
     mergeid,
     isconversionevent,
     cast(null as double) as conversionvalue, 
     product_details,
     suggestionreferenceid,
     internalsuggestionreferenceid,
     linkedinteractionuid,
     suggestiondriver,
     suggesteddetailrepactionname,
     suggestedactiondatetime,
     usecasename, 
     actiontaken,
     issuggestioncompleteddirect,
     issuggestioncompletedinfer,
     factoruid,
     factorname,
     strategyid,
     strategyname,
     seconfigid,
     seconfigname,
     suggestion_product_details,
     suggestion_reasons,
     call_type,
     call_channel_raw,
     call_channel_category,
     iscompleted,
     reportedinteractionuid
from interaction_suggestion_event_factor_lvl
union
select
     'Interaction-factor level' as recordType,
     accountid,
     accountuid,
     accountname,
     cast(null as integer) as productid,
     cast(null as varchar) as productuid,
     cast(null as varchar) as productname,
     segmentname,
     repid,
     repuid,
     repname,
     cast(null as integer) messageid,
     cast(null as varchar) messageuid,
     cast(null as varchar) as messagename,
     cast(null as varchar) as messagetopic,
     eventtypeid,
     eventtypename,
     interactionuid,
     duration,
     repActionTypeId,
     detailRepActiontypeid,
     eventdate,
     eventdatetimeutc,
     actiondatetime,
     eventlabel,
     mergeid,
     isconversionevent,
     cast(null as double) as conversionvalue, 
     product_details,
     suggestionreferenceid,
     internalsuggestionreferenceid,
     linkedinteractionuid,
     suggestiondriver,
     suggesteddetailrepactionname,
     suggestedactiondatetime,
     usecasename, 
     actiontaken,
     issuggestioncompleteddirect,
     issuggestioncompletedinfer,
     factoruid,
     factorname,
     strategyid,
     strategyname,
     seconfigid,
     seconfigname,
     suggestion_product_details,
     suggestion_reasons,
     call_type,
     call_channel_raw,
     call_channel_category,
     iscompleted,
     reportedinteractionuid
from suggestion_interaction_event_factor_lvl