version: 2
metrics:
  - name: journey_progression
    label: journey_progression
    model: ref('kpi_event_interaction_explode')
    description: "The total number of Accounts moved to the stage indicated in the KPI name in the given period. This KPI is by Account and Product."
    calculation_method: count
    expression: distinct accountid, product_uid, eventlabel
    timestamp: eventdate
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - facilityid
      - facilityuid
      - facilityname
      - configcountrycode
      - accountid
      - accountuid 
      - accountname 
      - product_id 
      - product_uid
      - product_name
      - repid 
      - repuid 
      - repname 
      - eventdate
      - eventtypename
      - eventlabel
    filters: 
       - field: eventtypename
         operator: '='
         value: "'KPISTATECHANGE-TIER'"
       - field: countrycode
         operator: '='
         value: configcountrycode
 
 
  - name: rte_completed
    label: rte_completed
    model: ref('kpi_event_interaction_explode')
    description: "The total number of Rep-Triggered-Email sent to Physicians by reps in the given period. This KPI is by Account, Rep, and Product."
    calculation_method: count
    expression: distinct interactionuid, product_uid
    timestamp: eventdate
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - facilityid
      - facilityuid
      - facilityname
      - configcountrycode
      - accountid
      - accountuid 
      - accountname 
      - product_id 
      - product_uid
      - product_name
      - repid 
      - repuid 
      - repname 
      - eventdate
      - eventtypename
    filters: 
       - field: eventtypename
         operator: '='
         value: "'KPISEND_ANY-COMPLETED'"
       - field: countrycode
         operator: '='
         value: configcountrycode


  - name: calls_completed
    label: calls_completed
    model: ref('kpi_event_interaction_factorlevel_v')
    description: "The total number of Visit to Physicians completed by reps in the given period. This KPI is by by Account and Rep."
    calculation_method: count
    expression: distinct interactionuid
    timestamp: eventdate
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - facilityid
      - facilityuid
      - facilityname
      - configcountrycode
      - accountid
      - accountuid 
      - accountname 
      - repid 
      - repuid 
      - repname 
      - eventdate
      - eventtypename
    filters: 
       - field: eventtypename
         operator: '='
         value: "'KPIVISIT-COMPLETED'"
       - field: countrycode
         operator: '='
         value: configcountrycode


  - name: details_completed
    label: details_completed
    model: ref('kpi_event_interaction_explode')
    description: "The total number of Product Details to Physicians completed by reps in the given period. This KPI is by Account, Rep, and Product."
    calculation_method: count
    expression: distinct interactionuid
    timestamp: eventdate
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - facilityid
      - facilityuid
      - facilityname
      - configcountrycode
      - accountid
      - accountuid 
      - accountname 
      - product_id 
      - product_uid
      - product_name
      - repid 
      - repuid 
      - repname 
      - eventdate
      - eventtypename
    filters: 
       - field: eventtypename
         operator: '='
         value: "'KPIVISIT-COMPLETED'"
       - field: countrycode
         operator: '='
         value: configcountrycode