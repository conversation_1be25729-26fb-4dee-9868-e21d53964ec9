{{ config(materialized='view') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}


with kpieventinteractionexplode  as 
(
 select 
 kpi.recordType,
 kpi.accountid,
 kpi.accountuid,
 kpi.accountname,
 kpi.productid,
 kpi.productuid,
 kpi.productname,
 kpi.segmentname,
 kpi.repid,
 kpi.repuid,
 kpi.repname,
 prod_det.messageid,
 prod_det.messageuid,
 prod_det.messagename,
 prod_det.messagetopic,
 prod_det.message_doc_type,
 kpi.eventtypeid,
 kpi.eventtypename,
 kpi.interactionuid,
 kpi.duration,
 kpi.repActionTypeId,
 kpi.detailRepActiontypeid,
 kpi.eventdate,
 kpi.eventdatetimeutc,
 kpi.actiondatetime,
 kpi.eventlabel,
 kpi.mergeid,
 kpi.isconversionevent,
 kpi.conversionvalue, 
 kpi.product_details,
 kpi.suggestionreferenceid,
 kpi.internalsuggestionreferenceid,
 kpi.linkedinteractionuid,
 kpi.suggestiondriver,
 kpi.suggesteddetailrepactionname,
 kpi.suggestedactiondatetime,
 kpi.usecasename, 
 kpi.actiontaken,
 kpi.issuggestioncompleteddirect,
 kpi.issuggestioncompletedinfer,
 kpi.factoruid,
 kpi.factorname,
 kpi.strategyid,
 kpi.strategyname,
 kpi.seconfigid,
 kpi.seconfigname,
 kpi.suggestion_product_details,
 kpi.suggestion_reasons,
 kpi.call_type,
 kpi.call_channel_raw,
 kpi.call_channel_category,
 f.facilityid,
 f.externalid as facilityuid,
 f.facilityname,
{%- if var_multi_country == 'false' %}  
    '{{ build_country() }}' as configcountrycode,
{%- else %}
      COALESCE(r.configcountrycode, a.configcountrycode) as configcountrycode,
{%- endif %}
 kpi.productuid as product_uid,
 kpi.productid as product_id,
 kpi.productname as product_name
  from {{ ref('kpi_event_interaction') }} kpi
  cross join UNNEST(product_details) as p(prod_det)
  inner join {{ ref('actor_v') }} r  on kpi.repid = r.repid
  inner join {{ ref('account_dse_v') }} a on kpi.accountid = a.accountid  
  inner join {{ ref('facility_v') }} f on f.facilityid = a.facilityid
  where kpi.recordType = 'Interaction-factor-product level'
)
select
 t.recordType,
 t.accountid,
 t.accountuid,
 t.accountname,
 t.productid,
 t.productuid,
 t.productname,
 t.segmentname,
 t.repid,
 t.repuid,
 t.repname,
 t.messageid,
 t.messageuid,
 t.messagename,
 t.messagetopic,
 t.message_doc_type,
 t.eventtypeid,
 t.eventtypename,
 t.interactionuid,
 t.duration,
 t.repActionTypeId,
 t.detailRepActiontypeid,
 t.eventdate,
 t.eventdatetimeutc,
 t.actiondatetime,
 t.eventlabel,
 t.mergeid,
 t.isconversionevent,
 t.conversionvalue, 
 t.product_details,
 t.suggestionreferenceid,
 t.internalsuggestionreferenceid,
 t.linkedinteractionuid,
 t.suggestiondriver,
 t.suggesteddetailrepactionname,
 t.suggestedactiondatetime,
 t.usecasename, 
 t.actiontaken,
 t.issuggestioncompleteddirect,
 t.issuggestioncompletedinfer,
 t.factoruid,
 t.factorname,
 t.strategyid,
 t.strategyname,
 t.seconfigid,
 t.seconfigname,
 t.suggestion_product_details,
 t.suggestion_reasons,
 t.call_type,
 t.call_channel_raw,
 t.call_channel_category,
 t.facilityid,
 t.facilityuid,
 t.facilityname,
 t.configcountrycode,
 t.product_uid,
 t.product_id,
 t.product_name
 from kpieventinteractionexplode t
