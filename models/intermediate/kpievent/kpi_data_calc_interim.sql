{{ config(materialized='table') }}

-- depends_on: {{ ref('account_with_time_grain_v') }}
-- depends_on: {{ ref('accountproduct_with_time_grain_v') }}
-- depends_on: {{ ref('repaccount_with_time_grain') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with data_calculation_details as
(
     select a.facilityid,
		a.facilityuid,
		a.facilityname,
		a.configcountrycode,
		a.accountid,
		a.accountuid,
		a.accountname,
		a.product_id,
		a.product_uid,
		a.product_name,
		a.repid,
		a.repuid,
		a.repname,
		a.recordtype,
		a.datevalue,
        a.messagetopic,
		e.fiscalperiodid,
		e.countrycode,
		e.periodname,
        d.repteamname,
        d.repteamuid,
		d.territoryname,
		d.districtname,
		d.regionname,
		{% if build_check_table_exists('Account') == true %}
        	{{ build_get_attribute_names('Account', 'b') }} ,
		{% endif %}
		{% if build_check_table_exists('AccountProduct') == true %}
        	{{ build_get_attribute_names('AccountProduct', 'c') }} ,
		{% endif %}
		{% if build_check_table_exists('RepAccountAssignment') == true %}
        	{{ build_get_attribute_names('RepAccountAssignment', 'f') }} ,
		{% endif %}
		a.eventlabel,
		a.kpivalue,
		a.kpitypeid
	from {{ ref('kpi_data_calculation') }} a
	{% if build_check_table_exists('Account') == true %}
     left join {{ ref('account_with_time_grain_v') }} b
          on a.accountid = b.accountid
          and a.recordtype = b.recordlevel
		  and a.datevalue = b.accdate
	{% endif %}
	{% if build_check_table_exists('AccountProduct') == true %}
     left join {{ ref('accountproduct_with_time_grain_v') }} c
          on a.accountid = c.accountid
		  and a.product_id = c.productid
          and a.recordtype = c.recordlevel
		  and a.datevalue = c.apdate
	{% endif %}	  
	{% if build_check_table_exists('RepAccountAssignment') == true %}
     left join {{ ref('repaccount_with_time_grain') }} f
          on a.accountid = f.accountid
		  and a.repid = f.repid
          and a.recordtype = f.recordlevel
		  and a.datevalue = f.apdate
	{% endif %}	  
	left join {{ ref('rep_with_time_grain_v') }}  d
		on a.repuid = d.externalid
          and a.recordtype = d.recordlevel
		  and a.datevalue = d.repdate
	left join {{ ref('fiscalperiod_country_normalized') }} e
		on a.datevalue = e.enddate	
		and a.recordtype = e.frequency
	{%- if var_multi_country == 'true' %}  
        and e.countrycode = d.countrycode
    {%- endif %}	
)
select
     *
from data_calculation_details