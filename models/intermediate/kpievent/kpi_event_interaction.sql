{{ config(materialized='table') }}

with tier_conversion_event as
(
     select
          c.accountid,
          a.externalid as accountuid,
          a.accountname,
          c.productid,
          p.externalid as productuid,
          p.productname,
          coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
          et.eventtypeid,
          et.eventtypename,
          c.eventdate,
          c.newstate as eventlabel,
          concat(a.externalid, p.externalid, et.eventtypename, TO_CHAR(c.eventdate, 'yyyy-mm-dd')) as externalid,
          c.conversionvalue,
          cast(cast(et.isconversionevent as boolean) as integer) as isconversionevent
     from {{ ref('accountproduct_tier_conversion') }} c
     inner join {{ ref('account_dse_v') }} a
          on c.accountid = a.accountid
     inner join {{ ref('product_v') }} p
          on c.productid = p.productid
     inner join {{ ref('kpi_eventtype') }} et
          on et.eventtypename = 'KPISTATECHANGE-TIER'
     left join {{ ref('akt_account_segment_mapping_v') }} aasm 
          on aasm.accountId = c.accountid
)
select
    recordType,
	accountid,
	accountuid,
	accountname,
	productid,
	productuid,
	productname,
	segmentname,
	repid,
	repuid,
	repname,
	messageid,
    messageuid,
    messagename,
    messagetopic,
	eventtypeid,
	eventtypename,
	interactionuid,
	duration,
	repActionTypeId,
	detailRepActiontypeid,
	eventdate,
	eventdatetimeutc,
	actiondatetime,
	eventlabel,
	mergeid,
	cast(cast(isconversionevent as boolean) as integer) as isconversionevent,
	conversionvalue,
	product_details,
	suggestionreferenceid,
	internalsuggestionreferenceid,
	linkedinteractionuid,
	suggestiondriver,
	suggesteddetailrepactionname,
	suggestedactiondatetime,
	usecasename,
	actiontaken,
	cast(cast(issuggestioncompleteddirect as boolean) as integer) as issuggestioncompleteddirect,
	cast(cast(issuggestioncompletedinfer as boolean) as integer) as issuggestioncompletedinfer,
	factoruid,
	factorname,
	strategyid,
	strategyname,
	seconfigid,
	seconfigname,
	suggestion_product_details,
	suggestion_reasons,
	call_type,
	call_channel_raw,
    call_channel_category,
	cast(cast(iscompleted as boolean) as integer) as iscompleted,
	reportedinteractionuid,
	cast(null as varchar) as eventTypeUid,
    cast(null as varchar) as event_category,
    cast(null as varchar) as event_sub_category,
    cast(null as varchar) as event_parent_category,
    cast(null as varchar) as eventAttributes
from {{ ref('kpi_event_interaction_lvl1_v') }}
union
select
    recordType,
	accountid,
	accountuid,
	accountname,
	productid,
	productuid,
	productname,
	segmentname,
	repid,
	repuid,
	repname,
	messageid,
    messageuid,
    messagename,
    messagetopic,
	eventtypeid,
	eventtypename,
	interactionuid,
	duration,
	repActionTypeId,
	detailRepActiontypeid,
	eventdate,
	eventdatetimeutc,
	actiondatetime,
	eventlabel,
	mergeid,
	cast(cast(isconversionevent as boolean) as integer) as isconversionevent,
	conversionvalue,
	product_details,
	suggestionreferenceid,
	internalsuggestionreferenceid,
	linkedinteractionuid,
	suggestiondriver,
	suggesteddetailrepactionname,
	suggestedactiondatetime,
	usecasename,
	actiontaken,
	cast(cast(issuggestioncompleteddirect as boolean) as integer) as issuggestioncompleteddirect,
	cast(cast(issuggestioncompletedinfer as boolean) as integer) as issuggestioncompletedinfer,
	factoruid,
	factorname,
	strategyid,
	strategyname,
	seconfigid,
	seconfigname,
	suggestion_product_details,
	suggestion_reasons,
	call_type,
	call_channel_raw,
    call_channel_category,
	cast(cast(iscompleted as boolean) as integer) as iscompleted,
	reportedinteractionuid,
	cast(null as varchar) as eventTypeUid,
    cast(null as varchar) as event_category,
    cast(null as varchar) as event_sub_category,
    cast(null as varchar) as event_parent_category,
    cast(null as varchar) as eventAttributes
from {{ ref('kpi_event_interaction_lvl2_v') }}
union
select
    recordType,
	accountid,
	accountuid,
	accountname,
	productid,
	productuid,
	productname,
	segmentname,
	repid,
	repuid,
	repname,
	messageid,
    messageuid,
    messagename,
    messagetopic,
	eventtypeid,
	eventtypename,
	interactionuid,
	duration,
	repActionTypeId,
	detailRepActiontypeid,
	eventdate,
	eventdatetimeutc,
	actiondatetime,
	eventlabel,
	mergeid,
	cast(cast(isconversionevent as boolean) as integer) as isconversionevent,
	conversionvalue,
	product_details,
	suggestionreferenceid,
	internalsuggestionreferenceid,
	linkedinteractionuid,
	suggestiondriver,
	suggesteddetailrepactionname,
	suggestedactiondatetime,
	usecasename,
	actiontaken,
	cast(cast(issuggestioncompleteddirect as boolean) as integer) as issuggestioncompleteddirect,
	cast(cast(issuggestioncompletedinfer as boolean) as integer) as issuggestioncompletedinfer,
	factoruid,
	factorname,
	strategyid,
	strategyname,
	seconfigid,
	seconfigname,
	suggestion_product_details,
	suggestion_reasons,
	call_type,
	call_channel_raw,
    call_channel_category,
	cast(cast(iscompleted as boolean) as integer) as iscompleted,
	reportedinteractionuid,
	cast(null as varchar) as eventTypeUid,
    cast(null as varchar) as event_category,
    cast(null as varchar) as event_sub_category,
    cast(null as varchar) as event_parent_category,
    cast(null as varchar) as eventAttributes
from {{ ref('kpi_event_interaction_lvl3_v') }}
union
select
    recordType,
	accountid,
	accountuid,
	accountname,
	productid,
	productuid,
	productname,
	segmentname,
	repid,
	repuid,
	repname,
	messageid,
    messageuid,
    messagename,
    messagetopic,
	eventtypeid,
	eventtypename,
	interactionuid,
	duration,
	repActionTypeId,
	detailRepActiontypeid,
	eventdate,
	eventdatetimeutc,
	actiondatetime,
	eventlabel,
	mergeid,
	cast(cast(isconversionevent as boolean) as integer) as isconversionevent,
	conversionvalue,
	product_details,
	suggestionreferenceid,
	internalsuggestionreferenceid,
	linkedinteractionuid,
	suggestiondriver,
	suggesteddetailrepactionname,
	suggestedactiondatetime,
	usecasename,
	actiontaken,
	cast(cast(issuggestioncompleteddirect as boolean) as integer) as issuggestioncompleteddirect,
	cast(cast(issuggestioncompletedinfer as boolean) as integer) as issuggestioncompletedinfer,
	factoruid,
	factorname,
	strategyid,
	strategyname,
	seconfigid,
	seconfigname,
	suggestion_product_details,
	suggestion_reasons,
	call_type,
	call_channel_raw,
    call_channel_category,
	cast(null as integer) as iscompleted,
	cast(null as varchar) as reportedinteractionuid,
	eventTypeUid,
    event_category,
    event_sub_category,
    event_parent_category,
    eventAttributes
from {{ ref('kpi_event_interaction_lvl4_v') }}
union
select
	'Tier Conversion level' as recordType,
	accountid,
	accountuid,
	accountname,
	productid,
	productuid,
	productname,
     segmentname,
	cast(null as integer) as repid,
	null as repuid,
	null as repname,
	cast(null as integer) messageid,
	cast(null as varchar) as messageuid,
	cast(null as varchar) as messagename,
	cast(null as varchar) as messagetopic,
	eventtypeid,
	eventtypename,
	cast(null as varchar) as interactionuid,
	cast(null as integer) as duration,
	cast(null as integer) as repActionTypeId,
	cast(null as integer) as detailRepActionTypeId,
	eventdate,
	eventdate,
	eventdate,
	eventlabel,
	externalid as mergeid,
	isconversionevent,
	conversionvalue,
	cast(null as array(ROW(actionorder integer, repactiontypename VARCHAR, productid integer, productuid VARCHAR, productname VARCHAR, messageid integer, messageuid VARCHAR, messagename VARCHAR, messagetopic VARCHAR, messagereaction VARCHAR, message_doc_type VARCHAR, duration integer))),
	null as suggestionreferenceid,
	null as internalsuggestionreferenceid,
	null as linkedinteractionuid,
	null as suggestiondriver,
	null as suggesteddetailrepactionname,
	cast(null as date) suggestedactiondatetime,
	null as usecasename,
	null as actiontaken,
	cast(null as integer) as issuggestioncompleteddirect,
	cast(null as integer) as issuggestioncompletedinfer,
	null as factoruid,
	null as factorname,
	cast(null as integer) as strategyid,
	null as strategyname,
	cast(null as integer) as seconfigid,
	null as seconfigname,
	cast(null as array(ROW(actionorder integer, detailrepactionname VARCHAR, productid integer, productuid VARCHAR, productname VARCHAR, messageid integer, messageuid VARCHAR, messagename VARCHAR, messagetopic VARCHAR, factoruid VARCHAR, factorname VARCHAR, strategyid integer, strategyname VARCHAR))),
	cast(null as array(ROW(reasonrank integer, reasontext VARCHAR, crmfieldname VARCHAR, reasonsourcesystemname VARCHAR, factoruid VARCHAR, factorname VARCHAR, strategyid integer, strategyname VARCHAR))),
	'' as call_type,
	'' as call_channel_raw,
    '' as call_channel_category,
	cast(null as integer) as iscompleted,
	cast(null as varchar) as reportedinteractionuid,
	cast(null as varchar) as eventTypeUid,
    cast(null as varchar) as event_category,
    cast(null as varchar) as event_sub_category,
    cast(null as varchar) as event_parent_category,
    cast(null as varchar) as eventAttributes
from tier_conversion_event