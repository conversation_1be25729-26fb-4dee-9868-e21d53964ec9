{{ config(materialized='table') }}

with interaction_key_message_delivery_product_dtl as
(
     select
          t.externalid,
          t.productid,
          t.productuid,
          t.productname,   
          array_agg(
          CAST(ROW(
               t.actionorder,
               t.repactiontypeName,
               t.productid,
               t.productuid,
               t.productname,
               t.messageid,
               t.messageuid,
               t.messagename,
               t.messagetopic,
               t.messagereaction,
               t.document_type,
               t.duration)
          AS ROW(
               actionorder integer,
               repactiontypename VARCHAR,
               productid integer,
               productuid VARCHAR,
               productname VARCHAR,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               messagereaction VARCHAR,
               message_doc_type VARCHAR,
               duration integer)) order by t.actionorder) AS product_details
     from {{ ref('interaction_key_message_delivery_v') }} t
     group by
          t.externalid,
          t.productid,
          t.productuid,
          t.productname
),
interaction_event_product_dtl as
(
     select
          t.externalid,
          t.productid,
          t.productuid,
          t.productname,   
          array_agg(
          CAST(ROW(
               t.actionorder,
               t.repactiontypeName,
               t.productid,
               t.productuid,
               t.productname,
               t.messageid,
               t.messageuid,
               t.messagename,
               t.messagetopic,
               t.messagereaction,
               t.document_type,
               t.duration)
          AS ROW(
               actionorder integer,
               repactiontypename VARCHAR,
               productid integer,
               productuid VARCHAR,
               productname VARCHAR,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               messagereaction VARCHAR,
               message_doc_type VARCHAR,
               duration integer)) order by t.actionorder) AS product_details
     from {{ ref('interaction_detail_v') }} t
     group by
          t.externalid,
          t.productid,
          t.productuid,
          t.productname
),
interaction_dtl_event as
(
     select
          t.accountid,
          coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
          t.repid,
          et.eventtypeid, 
          t.accountname,
          t.repname,
          et.eventtypename,
          t.accountuid,
          t.repuid,
          t.repActionTypeId,
          t.corellationid,
          t.corellationid interactionuid,
          t.duration, 
          t.eventdate, 
          t.eventdatetimeutc, 
          t.actiondatetime, 
          t.eventlabel, 
          t.externalid, 
          et.isconversionevent,
          case
               when kev.product_details is not null then kev.product_details
               else ev.product_details
          end as product_details,
          case
               when kev.product_details is not null then kev.productid
               else ev.productid
          end as productid,
          case
               when kev.product_details is not null then kev.productuid
               else ev.productuid
          end as productuid,
          case
               when kev.product_details is not null then kev.productname
               else ev.productname
          end as productname,
          ictv.call_type,
          ictv.call_channel_raw,
          ictv.call_channel_category
     from {{ ref('interaction_event_base_info_v') }} t
     inner join {{ ref('kpi_eventtype') }} et
          on concat('KPI',t.repactiontypeName,'-COMPLETED') = et.eventtypename
     left join interaction_event_product_dtl ev
          on concat(t.corellationid, t.accountuid) = ev.externalid
     left join interaction_key_message_delivery_product_dtl kev
          on concat(t.corellationid, t.accountuid) = kev.externalid
         and kev.productid = ev.productid
     left join {{ ref('akt_account_segment_mapping_v') }} aasm 
          on aasm.accountId = t.accountid
     left join {{ ref('interaction_call_type_v') }} ictv 
          on ictv.interactionuid = t.interactionuid
),
suggestion_event_reason as 
(
     select
          suggestionreferenceid, 
          runuid,
          factoruid,
          factorname,
          min(tagname) tagname,
          min(strategyid) strategyid,
          min(strategyname) strategyname,
          array_agg(
          CAST(ROW(
               reasonrank,
               reasontext,
               crmfieldname,
               reasonsourcesystemname,
               factoruid,
               factorname,
               strategyid,
               strategyname)
          AS ROW(
               reasonrank integer,
               reasontext VARCHAR,
               crmfieldname VARCHAR,
               reasonsourcesystemname VARCHAR,
               factoruid VARCHAR,
               factorname VARCHAR,
               strategyid integer,
               strategyname VARCHAR)) order by reasonrank) as suggestion_reasons,
          grouping(factoruid) as is_factor_grouping
     from {{ ref('suggestion_factor_reason_v') }} 
     group by
          grouping sets(
               (suggestionreferenceid),
               (suggestionreferenceid, runuid, factoruid, factorname))
),
suggestion_event_product_dtl as
(
     select
          r.suggestionreferenceid,
          i.productid,
          i.productuid,
          i.productname,
          r.factoruid,
          r.factorname,
          array_agg(
          CAST(ROW(
               coalesce(i.actionorder, 1),
               i.detailrepactionname,
               i.productid,
               i.productuid,
               i.productname,
               i.messageid,
               i.messageuid,
               i.messagename,
               null,
               r.factoruid,
               r.factorname,
               r.strategyid,
               r.strategyname)
          AS ROW(
               actionorder integer,
               detailrepactionname VARCHAR,
               productid integer,
               productuid VARCHAR,
               productname VARCHAR,
               messageid integer,
               messageuid VARCHAR,
               messagename VARCHAR,
               messagetopic VARCHAR,
               factoruid VARCHAR,
               factorname VARCHAR,
               strategyid integer,
               strategyname VARCHAR)
               ) order by coalesce(i.actionorder, 1)) as product_details
     from suggestion_event_reason r
     inner join {{ ref('sparkdserunconfigfactor_v') }} f
          on r.runuid = f.runuid
         and r.factoruid = f.factoruid
     inner join {{ ref('suggestion_factor_dtl_v') }} i
          on r.suggestionreferenceid = i.suggestionreferenceid
         and f.productuid = i.productuid
     where r.is_factor_grouping = 0
     group by
          r.suggestionreferenceid,
          i.productid,
          i.productuid,
          i.productname,
          r.factoruid,
          r.factorname
),
suggestion_event_dtl_product as 
(
     select 
          t.accountid, 
          t.repid,
          t.eventtypeid,
          t.accountuid,
          t.repuid, 
          t.accountname,
          t.segmentname,
          t.repname,
          t.eventtypename,
          t.corellationid,
          t.linkedinteractionuid,
          t.suggestiondriver,
          t.suggestionreferenceid,
          t.internalsuggestionreferenceid,
          t.detailrepactionname,
          t.eventdate,
          t.eventdatetimeutc,
          t.actiondatetime, 
          t.eventlabel,
          t.externalid,
          t.isconversionevent, 
          coalesce(e.tagname, 'Channel Execution') as usecasename,
          t.actiontaken,
          t.issuggestioncompleteddirect, 
          t.issuggestioncompletedinfer, 
          e.factoruid,
          coalesce(e.factorname, 'Channel Execution') as factorname,
          e.strategyid,
          coalesce(e.strategyname, 'Undefined Strategy') as strategyname,
          t.seconfigid, 
          t.seconfigname,
          d.product_details,
          e.suggestion_reasons,
          t.detailrepactiontypeid,
          d.productid,
          d.productuid,
          d.productname,
          t.iscompleted,
          t.reportedinteractionuid
     from {{ ref('suggestion_event_base_info_v') }} t
     inner join suggestion_event_reason e
          on t.suggestionreferenceid = e.suggestionreferenceid
         and e.is_factor_grouping = 0
     inner join suggestion_event_product_dtl d
          on t.suggestionreferenceid = d.suggestionreferenceid
         and e.factoruid = d.factoruid
),
interaction_suggestion_event_product_lvl as
(
     select
          a.accountid,
          a.repid,
          a.eventtypeid,
          a.accountuid,
          a.repuid,
          a.accountname,
          a.segmentname,
          a.repname,
          a.eventtypename,
          a.interactionuid,
          a.duration,
          a.eventdate,
          a.eventdatetimeutc,
          a.actiondatetime, 
          a.eventlabel,
          a.externalid as mergeid,
          a.isconversionevent,
          a.product_details,
          b.suggestionreferenceid, 
          b.internalsuggestionreferenceid,
          b.linkedinteractionuid,
          b.suggestiondriver,
          b.detailrepactionname as suggesteddetailrepactionname,
          b.actiondatetime as suggestedactiondatetime,
          b.usecasename,
          b.actiontaken, 
          b.issuggestioncompleteddirect,
          b.issuggestioncompletedinfer,
          b.factoruid,
          b.factorname,
          b.strategyid,
          b.strategyname,
          b.seconfigid,
          b.seconfigname,
          b.product_details as suggestion_product_details,
          b.suggestion_reasons,
          a.repActionTypeId,
          b.detailrepactiontypeid,
          a.productid,
          a.productuid,
          a.productname,
          a.call_type,
          a.call_channel_raw,
          a.call_channel_category,
          b.iscompleted,
          b.reportedinteractionuid
     from interaction_dtl_event a
     left join suggestion_event_dtl_product b
          on a.externalid = b.corellationid 
         and a.productid = b.productid
),
suggestion_interaction_event_product_lvl as
(
     select
          a.accountid,
          a.repid,
          a.eventtypeid,
          a.accountuid,
          a.repuid,
          a.accountname,
          a.segmentname,
          a.repname,
          a.eventtypename,
          b.interactionuid,
          b.duration,
          a.eventdate,
          a.eventdatetimeutc,
          a.actiondatetime, 
          a.eventlabel,
          b.externalid as mergeid,
          a.isconversionevent,
          b.product_details, 
          a.suggestionreferenceid, 
          a.internalsuggestionreferenceid,
          a.linkedinteractionuid,
          a.suggestiondriver,
          a.detailrepactionname as suggesteddetailrepactionname,
          a.actiondatetime as suggestedactiondatetime,
          a.usecasename,
          a.actiontaken, 
          a.issuggestioncompleteddirect,
          a.issuggestioncompletedinfer,
          a.factoruid,
          a.factorname,
          a.strategyid,
          a.strategyname,
          a.seconfigid,
          a.seconfigname,
          a.product_details as suggestion_product_details,
          a.suggestion_reasons,
          b.repActionTypeId,
          a.detailrepactiontypeid,
          a.productid as suggestion_productid,
          a.productuid as suggestion_productuid,
          a.productname as suggestion_productname,
          b.call_type,
          b.call_channel_raw,
          b.call_channel_category,
          a.iscompleted,
          a.reportedinteractionuid
     from suggestion_event_dtl_product a
     left join interaction_dtl_event b
          on a.corellationid = b.externalid
         and a.productid = b.productid
)
select
     'Interaction-factor-product level' as recordType,
     accountid,
     accountuid,
     accountname,
     productid,
     productuid,
     productname,
     segmentname,
     repid,
     repuid,
     repname,
     cast(null as integer) messageid,
     cast(null as varchar) messageuid,
     cast(null as varchar) as messagename,
     cast(null as varchar) as messagetopic,
     eventtypeid,
     eventtypename,
     interactionuid,
     duration,
     repActionTypeId,
     detailRepActiontypeid,
     eventdate,
     eventdatetimeutc,
     actiondatetime,
     eventlabel,
     mergeid,
     isconversionevent,
     cast(null as double) as conversionvalue, 
     product_details,
	suggestionreferenceid,
     internalsuggestionreferenceid,
     linkedinteractionuid,
     suggestiondriver,
     suggesteddetailrepactionname,
     suggestedactiondatetime,
     usecasename, 
     actiontaken,
     issuggestioncompleteddirect,
     issuggestioncompletedinfer,
     factoruid,
     factorname,
     strategyid,
     strategyname,
     seconfigid,
     seconfigname,
     suggestion_product_details,
     suggestion_reasons,
     call_type,
     call_channel_raw,
     call_channel_category,
     iscompleted,
     reportedinteractionuid
from interaction_suggestion_event_product_lvl
union
select
     'Interaction-factor-product level' as recordType,
	accountid,
	accountuid,
	accountname,
     suggestion_productid,
     suggestion_productuid,
     suggestion_productname,
     segmentname,
	repid,
	repuid,
	repname,
     cast(null as integer) messageid,
     cast(null as varchar) messageuid,
     cast(null as varchar) as messagename,
     cast(null as varchar) as messagetopic,
	eventtypeid,
	eventtypename,
	interactionuid,
	duration,
	repActionTypeId,
	detailRepActiontypeid,
	eventdate,
	eventdatetimeutc,
	actiondatetime,
	eventlabel,
	mergeid,
	isconversionevent,
	cast(null as double) as conversionvalue,
	product_details,
	suggestionreferenceid,
	internalsuggestionreferenceid,
     linkedinteractionuid,
	suggestiondriver,
	suggesteddetailrepactionname,
	suggestedactiondatetime,
	usecasename,
	actiontaken,
	issuggestioncompleteddirect,
	issuggestioncompletedinfer,
     factoruid,
	factorname,
     strategyid,
     strategyname,
	seconfigid,
	seconfigname,
	suggestion_product_details,
	suggestion_reasons,
     call_type,
     call_channel_raw,
     call_channel_category,
     iscompleted,
     reportedinteractionuid
from suggestion_interaction_event_product_lvl