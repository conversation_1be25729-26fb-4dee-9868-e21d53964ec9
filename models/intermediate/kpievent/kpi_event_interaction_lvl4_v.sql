-- depends_on: {{ ref('event_v') }}
{{ config(materialized='table') }}

with json_data as ({{ build_json_query() }} ),
eventtype_category as (
      select et.eventtypeid,
             et.eventtypename,
             et.externalid,
             eventtypemap.eventypeparentcategory,
             eventtypemap.eventtypecategory,
             eventtypemap.eventtypesubcategory
      from {{ ref('eventtype_category_mapping_v')}} eventtypemap
      inner join {{ ref('eventtype_v')}} et
            on eventtypemap.eventtypeuid = et.externalid
),
event_data as
(
    select  e.externalid,
            'External Event level' as recordType,
            e.accountid,
            adv.externalid as accountuid,
            adv.accountname as accountname,
            e.productid,
            p.externalid as productuid,
            p.productname,
            acsm.hcpsegmentname as segmentname,
            e.repid,
            a.externalid as repuid,
            a.repname,
            e.messageid,
            m.externalid as messageuid,
            m.messagename,
            ams.messagetopic,
            e.eventtypeid,
            eventcat.eventtypename,
            cast(null as varchar) interactionuid,
            cast(null as integer) duration,
            cast(null as integer) repactiontypeid,
            cast(null as integer) detailrepactiontypeid,
            e.eventdate,
            e.eventdatetimeutc,
            cast(null as timestamp) actiondatetime,
            e.eventlabel,
            cast (uuid() as varchar) as mergeid,
            0 isconversionevent,
            cast(null as integer) conversionvalue,
            cast(null as array(ROW(actionorder integer, repactiontypename VARCHAR, productid integer, productuid VARCHAR, productname VARCHAR, messageid integer, messageuid VARCHAR, messagename VARCHAR, messagetopic VARCHAR, messagereaction VARCHAR, message_doc_type VARCHAR, duration integer))) product_details,
            cast(null as varchar) suggestionreferenceid,
            cast(null as varchar) internalsuggestionreferenceid,
            cast(null as varchar) linkedinteractionuid,
            cast(null as varchar) suggestiondriver,
            cast(null as varchar) suggesteddetailrepactionname,
            cast(null as date) suggestedactiondatetime,
            cast(null as varchar) usecasename,
            cast(null as varchar) actiontaken,
            cast(null as integer) issuggestioncompleteddirect,
            cast(null as integer) issuggestioncompletedinfer,
            cast(null as varchar) factoruid,
            cast(null as varchar) factorname,
            cast(null as integer) strategyid,
            cast(null as varchar) strategyname,
            cast(null as integer) seconfigid,
            cast(null as varchar) seconfigname,
            cast(null as array(ROW(actionorder integer, detailrepactionname VARCHAR, productid integer, productuid VARCHAR, productname VARCHAR, messageid integer, messageuid VARCHAR, messagename VARCHAR, messagetopic VARCHAR, factoruid VARCHAR, factorname VARCHAR, strategyid integer, strategyname VARCHAR))) suggestion_product_details,
	      cast(null as array(ROW(reasonrank integer, reasontext VARCHAR, crmfieldname VARCHAR, reasonsourcesystemname VARCHAR, factoruid VARCHAR, factorname VARCHAR, strategyid integer, strategyname VARCHAR))) suggestion_reasons,
	      cast(null as varchar) call_type,
            cast(null as varchar) call_channel_raw,
            cast(null as varchar) call_channel_category,
            eventcat.externalid as eventTypeUid,
            eventcat.eventtypecategory as event_category,
            eventcat.eventtypesubcategory as event_sub_category,
            eventcat.eventypeparentcategory as event_parent_category,
            jd.json_row as eventAttributes
    from  {{ ref('event_v') }} e
    inner join {{ ref('account_dse_v') }} adv
          on adv.accountid = e.accountid
    inner join  {{ ref('product_v') }} p
          on p.productid = e.productid
    left join {{ ref('actor_v') }} a
          on a.repid = e.repid
    left join {{ ref('message_v') }} m
          on m.messageid = e.messageid
    inner join json_data jd
          on jd.externalid = e.externalid
    left join {{ ref('akt_account_segment_mapping_v') }} acsm
          on    acsm.accountid = e.accountid
    left join {{ ref('akt_messagetomessagetopicmap_v') }} ams
          on ams.messageid = e.messageid
    inner join eventtype_category eventcat
          on eventcat.eventtypeid = e.eventtypeid
)
select * from event_data