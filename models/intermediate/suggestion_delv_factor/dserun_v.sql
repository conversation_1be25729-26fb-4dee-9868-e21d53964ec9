{{ config(materialized='table') }}

select
   a.startDateTime as startDateTime
 , cast(at_timezone(a.startDateTime, 'UTC') as date) as startDateLocal
 , b.suggestedDate as suggestedDate
 , a.seConfigId as seConfigId
 , a.runGroupId as runGroupId
 , max(a.runId) as runId
 , max(a.runUID) as runUID
from {{ ref('sparkdserun_v') }} a
join {{ ref('sparkdserunrepdate_v') }} b
  on a.runUID = b.runUID
cross join {{ ref('rpt_go_live_filter_v') }} c
where at_timezone(a.startDateTime, 'UTC') >= c.goLiveDate
  and extract(day_of_week from (at_timezone(a.startDateTime, 'UTC'))) not in (1,7)
group by 
    a.startDateTime
  , cast(at_timezone(a.startDateTime, 'UTC') as date)
  , b.suggestedDate
  , a.seConfigId
  , a.runGroupId
