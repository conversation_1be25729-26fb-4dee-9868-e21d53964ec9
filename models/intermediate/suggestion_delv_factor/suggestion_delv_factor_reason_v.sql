{{ config(materialized='table') }}

select {{ dbt_utils.star(from=ref('suggestion_delv_reason_v'), except=['issuggestioncritical'], relation_alias='a') }}, b.reason_factoruid as factoruid, b.factorname, b.factortype, b.strategyid, b.strategyname,  b.tagid, b.tagname, b.tagtype, b.sourcesystemname, b.isactive, b.suggestedactiontext, b.issupersededbytrigger, b.issuggestioncritical, b.suggestiontype, b.reasonsourcesystemname,  case when a.actiontaken = 'No Action Taken' then 'No Action Taken' when a.actiontaken = 'Suggestions Dismissed' then 'Suggestions Dismissed'
             when a.actiontaken = 'Suggestions Completed' and coalesce(cast(iscompleted as boolean), false) = false then 'Action Incomplete'
             when a.actiontaken = 'Suggestions Completed' and cast(iscompleted as boolean) = true then 'Action Completed'
             end as actiontaken_rpt,
        case when cast(a.issuggestioncompletedinfer as boolean) = true then 'Inferred' else 'Not Inferred' end as inferred_status,
        case when cast(a.iscompleted as boolean) = true then 'Action Completed' else 'Action Incomplete' end as action_status
from {{ ref('suggestion_delv_reason_v') }} a
join {{ ref('akt_replicense_arc_v') }} akt
  on akt.externalId = a.repuid
 and a.startDateLocal between cast(akt.startDate as date) and cast(akt.endDate as date)
join {{ ref('rpt_dim_calendar_v') }} c
  on c.datefield = a.startDateLocal
left join {{ ref('dserun_v') }} d
  on a.runid = d.runid
cross join {{ ref('rpt_go_live_filter_v') }} gl
left join {{ ref('factor_reason_map_v') }} b
  on a.runuid = b.runuid
 and a.runrepdatesuggestionreasonid = b.runrepdatesuggestionreasonid
where a.startdatelocal >= gl.golivedate
  and case 
        when ((c.is_holiday = 1 or c.is_weekend = 1) and a.actionTaken != 'No Action Taken')
              or
              (d.runId is not null or a.actionTaken <> 'No Action Taken')
          then 1
        else 0
      end = 1

