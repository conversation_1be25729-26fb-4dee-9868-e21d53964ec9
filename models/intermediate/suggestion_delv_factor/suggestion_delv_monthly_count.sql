{{ config(materialized='table') }}

select  accountuid, repuid, date_trunc('month', startdatelocal) as startdatelocal, min(repname) as repname,
        count(*) total_suggestion_count, count(distinct suggestionreferenceid) total_unique_suggestion_count,
        sum(case when actiontaken = 'Suggestions Completed' and cast(iscompleted as boolean) = true then 1 else 0 end) as total_suggestion_accept_completed,
        sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(iscompleted as boolean), false) = false then 1 else 0 end) as total_suggestion_accept_incomplete,
        sum(case when actiontaken = 'Suggestions Dismissed' then 1 else 0 end) as total_suggestion_dismissed,
        sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(issuggestioncompletedinfer as boolean), false) = true then 1 else 0 end) as total_suggestion_inferred_accepted,
        sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(issuggestioncompleteddirect as boolean), false) = true then 1 else 0 end) as total_suggestion_direct_accepted
from {{ ref('suggestion_delv_factor_v') }}
group by accountuid, repuid, date_trunc('month', startdatelocal);
