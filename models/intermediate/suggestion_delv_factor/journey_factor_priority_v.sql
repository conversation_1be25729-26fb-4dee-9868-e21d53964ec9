{{ config(materialized='view') }}

with candidate as (
     select accountuid,min(accountname) accountname,
            productid,min(productname) productname,
            detailfactoruid factoruid,min(factorname) factorname,
            min(suggesteddate) min_suggesteddate
       from {{ ref('suggestion_delv_factor_dtl_v') }} 
      group by accountuid, productid, detailfactoruid 
),
candidate_triggered as (
     select a.accountuid, a.factoruid, 
            min(a.min_suggesteddate) min_suggesteddate
       from candidate a 
       join {{ ref('sparkdserunconfigfactortag_v') }} b on a.factoruid=b.factoruid
      where b.tagname in ('MAS_TRIGGERED','DRL_JOURNEY')
      group by 1,2
),
candidate_filtered as (
     select distinct a.accountuid, a.factoruid, a.min_suggesteddate
       from candidate_triggered a
       left join {{ ref('msrscenario_event_interaction_with_candidate_lvl2') }} b on a.factoruid=b.factoruid and a.accountuid=b.accountuid 
      where b.interactionuid is NULL
),
candidate_factortagparam as (
     select a.factoruid,
            b.tagparamname, b.value
       from {{ ref('sparkdserunconfigfactortag_v') }} a 
       join {{ ref('sparkdserunconfigfactortagparam_v') }} b 
         on a.runconfigfactortagid=b.runconfigfactortagid
      where a.tagname in ('MAS_TRIGGERED','DRL_JOURNEY')
        and b.tagparamname in ('MAX_DAYS')
),
candidate_journey as (
     select distinct a.accountuid, a.factoruid,
            date_diff('day', a.min_suggesteddate, CURRENT_DATE) as days_since_first_candidate,
            CURRENT_DATE as currentDate,
            b.value as max_days,
            cast(date_diff('day', a.min_suggesteddate, CURRENT_DATE) as double)/cast(b.value as double) as influence_value
       from candidate_filtered a 
       join candidate_factortagparam b
         on a.factoruid=b.factoruid
      where b.tagparamname='MAX_DAYS' and
            date_diff('day', a.min_suggesteddate, CURRENT_DATE) < cast(b.value as int) + 2
)
select * from candidate_journey

