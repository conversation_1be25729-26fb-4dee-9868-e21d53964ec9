{{ config(materialized='table') }}

with suggestion_dtl_delv as (
    select  accountuid, repuid, min(repname) as repname, productuid, min(productname) as productname, date_trunc('month', startdatelocal) as startdatelocal,
            count(*) product_suggestion_count, count(distinct suggestionreferenceid) product_unique_suggestion_count,
            sum(case when actiontaken = 'Suggestions Completed' and cast(iscompleted as boolean) = true then 1 else 0 end) as product_suggestion_accept_completed,
            sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(iscompleted as boolean), false) = false then 1 else 0 end) as product_suggestion_accept_incomplete,
            sum(case when actiontaken = 'Suggestions Dismissed' then 1 else 0 end) as product_suggestion_dismissed,
            sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(issuggestioncompletedinfer as boolean), false) = true then 1 else 0 end) as product_suggestion_inferred_accepted,
            sum(case when actiontaken = 'Suggestions Completed' and coalesce(cast(issuggestioncompleteddirect as boolean), false) = true then 1 else 0 end) as product_suggestion_direct_accepted
    from {{ ref('suggestion_delv_factor_dtl_v') }}
    group by accountuid, repuid, productuid, date_trunc('month', startdatelocal)
)
select a.accountuid, a.repuid, a.repname, a.productuid, a.productname, a.startdatelocal, 
       a.product_suggestion_count, a.product_unique_suggestion_count,
       a.product_suggestion_accept_completed, a.product_suggestion_accept_incomplete,
       a.product_suggestion_dismissed, a.product_suggestion_inferred_accepted, 
       a.product_suggestion_direct_accepted,
       b.total_suggestion_count, b.total_unique_suggestion_count, 
       b.total_suggestion_accept_completed, b.total_suggestion_accept_incomplete,
       b.total_suggestion_dismissed, b.total_suggestion_inferred_accepted,
       b.total_suggestion_direct_accepted
from suggestion_dtl_delv a inner join 
        {{ ref('suggestion_delv_monthly_count') }} b on a.accountuid = b.accountuid and 
        a.repuid = b.repuid  and a.startdatelocal = b.startdatelocal

