version: 2

exposures:
  - name: exp_sales_monthly_indicationproduct_v
    label: Sales Monthly (IP)
    description: “Montly Sales by Indication(I) and Product(P)”
    type: analysis
    maturity: high

    depends_on:
      - ref('sales_monthly_indicationproduct')

    owner:
      email: satya.dhanush<PERSON><PERSON>@aktana.com

  - name: exp_sales_monthly_indicationaccountproduct_v
    label: Sales Monthly (IAP)
    description: “Montly Sales by Indication(I), Account(A) and Product(P)”
    type: analysis
    maturity: high

    depends_on:
      - ref('sales_monthly_indicationaccountproduct')

    owner:
      email: <EMAIL>
