{{ config(materialized='table') }}

with weekly_sales as
(
select indication, diagnosis_group, product_group, accountuid, productuid, accountid, productid, account_ref_id, product_ref_id, accountname, productname, sale_date, sales_value_trx, sales_value_nrx, sales_value_nbrx, sales_value_unit, sales_value_revenue from {{ ref('sales_weekly_indicationaccountproduct') }}
)
select indication, diagnosis_group, product_group, accountuid, productuid, accountid, productid, account_ref_id, product_ref_id, accountname, productname, date_trunc('month', sale_date) sale_date, sum(coalesce(sales_value_trx, 0.0)) sales_value_trx, sum(coalesce(sales_value_nrx, 0.0)) sales_value_nrx, sum(coalesce(sales_value_nbrx, 0.0)) sales_value_nbrx, sum(coalesce(sales_value_unit, 0.0)) sales_value_unit, sum(coalesce(sales_value_revenue, 0.0)) sales_value_revenue 
from weekly_sales
group by indication, diagnosis_group, product_group,accountuid, productuid, accountid, productid, account_ref_id, product_ref_id, accountname, productname, date_trunc('month', sale_date);

