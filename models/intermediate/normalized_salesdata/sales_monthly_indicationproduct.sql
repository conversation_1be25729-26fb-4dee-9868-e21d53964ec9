{{ config(materialized='table') }}

select indication, diagnosis_group, product_group, productuid, productid, product_ref_id, accountname, productname, sale_date, sum(coalesce(sales_value_trx, 0.0)) sales_value_trx, sum(coalesce(sales_value_nrx, 0.0)) sales_value_nrx, sum(coalesce(sales_value_nbrx, 0.0)) sales_value_nbrx, sum(coalesce(sales_value_unit, 0.0)) sales_value_unit, sum(coalesce(sales_value_revenue, 0.0)) sales_value_revenue 
from {{ ref('sales_monthly_indicationaccountproduct') }}
group by indication, diagnosis_group, product_group, productuid, productid, product_ref_id, accountname, productname, sale_date;

