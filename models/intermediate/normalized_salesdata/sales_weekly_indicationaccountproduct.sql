{{ config(materialized='table') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

 -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

    select 'NA' indication, 'NA' diagnosis_group, 'NA' product_group, 'NA' accountuid,'NA' as accountname, 'NA' productuid, 'NA' as productname, 0 accountid, 0 productid, 'NA' account_ref_id, 'NA' product_ref_id,
       DATE_PARSE('2022-01-01', '%Y-%m-%d') sale_date, 0.0 sales_value_trx, 0.0 sales_value_nrx, 0.0 sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue 

{% endif %}

