{{ config(materialized='table') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

 -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

    select 'default' msrscenariouid, 
            'NA' indication, 
            'NA' diagnosis_group, 
            'NA' product_group, 
            a.accountuid,
            a.productuid, 
            b.accountid,
            c.productid,
            'NA' account_ref_id,
            'NA' product_ref_id,
            a.salesdate as sale_date,
            case when lower(a.datapointunit) = 'trx' then a.basketvolume else 0.0 end "sales_value_trx",
            case when lower(a.datapointunit) = 'nrx' then a.basketvolume else 0.0 end "sales_value_nrx",
            case when lower(a.datapointunit) = 'nbrx' then a.basketvolume else 0.0 end "sales_value_nbrx",
            0 as sales_value_unit, 
            0 as sales_value_revenue
    FROM {{ ref('kpi_aggregated_salesdata_v')  }} a
    JOIN {{ ref('accountmapping_v') }} b on a.accountuid = b.accountuid
    JOIN {{ ref('productmapping_v') }} c on a.productuid = c.productuid

{% endif %}

