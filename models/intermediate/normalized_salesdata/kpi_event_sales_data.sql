{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with sales_data as (
select s.productuid as product_uid, s.productid as product_id, s.productname as product_name, 
       s.accountuid, s.accountid, a.accountname,
       s.repuid, r.repname, r.repid, a.configcountrycode,
      sale_date as eventdate,
      sum(coalesce(sales_value_trx, 0.0)) sales_value_trx, sum(coalesce(sales_value_nrx, 0.0)) sales_value_nrx, sum(coalesce(sales_value_nbrx, 0.0)) sales_value_nbrx, sum(coalesce(sales_value_unit, 0.0)) sales_value_unit, sum(coalesce(sales_value_revenue, 0.0)) sales_value_revenue,
      sum(coalesce(adoption_ladder_rankchange_binary, 0)) as sales_adoption_ladder_rankchange
from {{ ref('msrscenario_sales_indacctprdrep') }} as s
inner join {{ ref('account_dse_v') }} a on s.accountid = a.accountid
inner join {{ ref('rep_v') }} r on r.externalid = s.repuid
group by s.productuid, s.productid, s.productname, s.accountuid, s.accountid, a.accountname, s.repuid, r.repname, r.repid, s.sale_date, a.configcountrycode
)

select s.accountuid, s.accountid, s.accountname,
       s.product_uid, s.product_id, s.product_name,
       s.repuid, s.repname, s.repid,
       cast(null as varchar) as facilityuid,
       cast(null as varchar) as facilityname,
       cast(null as integer) as facilityid,
       s.eventdate,
       {%- if var_multi_country == 'false' %}  
           '{{ build_country() }}' as configcountrycode,
       {%- else %}
            s.configcountrycode as configcountrycode,
       {%- endif %}
       s.sales_value_trx, s.sales_value_nrx, s.sales_value_nbrx, s.sales_value_unit, s.sales_value_revenue, s.sales_adoption_ladder_rankchange
from sales_data s 