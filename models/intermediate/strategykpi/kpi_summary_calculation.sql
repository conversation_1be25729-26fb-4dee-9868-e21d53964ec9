-- depends_on: {{ ref('kpi_data_calc_interim') }}
-- depends_on: {{ ref('kpi_additional_setting_predicate_v') }}
-- depends_on: {{ ref('strategy_audience_predicate_v') }}
-- depends_on: {{ ref('strategy_fiscalperiod_past_map_v') }}
-- depends_on: {{ ref('kpi_map_global_v') }}
-- depends_on: {{ ref('rep_data_calculation_v') }}
-- depends_on: {{ ref('kpi_target_calculation')}}

{{ config(materialized='table') }}


 {% set query %}
        SELECT v.kpiid,
               v.kpitypeid,
               v.strategyid,
               v.productuid,
               v.repteamuid,
               ads.additionalsetting_sql,
               acs.audience_predicate_sql,
               v.targetvalue,
               COALESCE(ads.additionalsetting, '#NA#') as additionalsetting,
               v.kpitypelabel,
               COALESCE(t.aggregationlevel, 'Brand') as aggregationlevel,
               lower(COALESCE(t.calculationmethod, 'sum')) as calculationmethod
         FROM {{ref('kpi_map_v')}} v
          inner join {{ref('kpitype_v')}} t on t.kpitypeid = v.kpitypeid
          left join {{ref('kpi_additional_setting_predicate_v')}} ads on ads.kpiid = v.kpiid
          left join {{ref('strategy_audience_predicate_v')}} acs on acs.strategyid = v.strategyid and v.applyaudiencecondition = true
          and audience_predicate_sql not like '%AUTO_QA%'
          where v.modeltype in ('EVENT', 'MESSAGE_TOPIC')
         
 {% endset %}

  {% set repquery %}
        SELECT v.kpiid,
               v.kpitypeid,
               v.strategyid,
               v.productuid,
               v.repteamuid,
               v.targetvalue
         FROM {{ref('kpi_map_v')}} v
          where v.modeltype = 'REP' and
              v.kpitypelabel not in ('cycle_plan_attainment_brand','cycle_plan_attainment_visit_brand','cycle_plan_attainment_rte_brand','cycle_plan_attainment_remote_brand',
              'cycle_plan_attainment_overall','cycle_plan_attainment_visit','cycle_plan_attainment_rte','cycle_plan_attainment_remote')
         
 {% endset %}

  {% set globalkpiproductsquery %}
        SELECT distinct v.repteamuid, v.productuid
         FROM {{ref('kpi_map_global_v')}} v
         inner join {{ref('kpitype_v')}} t on t.kpitypeid = v.kpitypeid
          where v.modeltype = 'EVENT'
            and (v.kpitypelabel = 'calls_completed' or COALESCE(t.aggregationlevel, 'Brand') <> 'Brand')
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}

    {% if results.rows|length > 0 %}
        {% for row in results.rows %}
        select {{ row[0] }} as kpiid,
            v.kpitypeid,
            m.strategyid, 
            v.fiscalperiodid, 
            '{{ row[3]}}' as productuid, 
            v.repteamuid,
            {{ row[7] }} as kpitarget,
            '{{ row[8] }}' as additionalsetting,
            v.countrycode,
            {% if row[9] in ('message_topic_coverage', 'message_topic_clm_coverage', 'message_topic_rte_coverage') or row[11] == 'average' %}
            avg(v.kpivalue) kpivalue
            {% else %}
            sum(v.kpivalue) kpivalue
            {% endif %}
        from {{ref('kpi_data_calc_interim')}}  v
        inner join {{ref('strategy_fiscalperiod_past_map_v')}} m on m.strategyid = {{ row[2] }} 
                                                        and m.fiscalperiodid = v.fiscalperiodid
                                                        and m.countrycode = v.countrycode
        {% if row[9] == 'calls_completed' or row[10] != 'Brand' %}  where {% else %}
        where product_uid = '{{ row[3]}}' and {% endif %}
        v.kpitypeid = {{ row[1] }}
        and v.repteamuid = '{{ row[4]}}'
        {% if row[5] %} AND  {{ row[5] }} {% endif %}
        {% if row[6] %} AND  {{ row[6] }} {% endif %}
        group by v.kpitypeid, m.strategyid, v.fiscalperiodid, '{{ row[3]}}', v.repteamuid, v.countrycode
            {% if not loop.last %} UNION ALL {% endif %}
        {% endfor %}

    {% else %}

        select
            cast(null as integer) as kpiid
          , cast(null as integer) as kpitypeid 
          , cast(null as integer) as strategyid 
          , cast(null as integer) as fiscalperiodid 
          , cast(null as varchar) as productuid 
          , cast(null as varchar) as repteamuid 
          , cast(null as integer) as kpitarget 
          , cast(null as varchar) as additionalsetting 
          , cast(null as varchar) as countrycode 
          , cast(null as double) as kpivalue
        where 1 = 0

    {% endif %}

{% endif %}

UNION ALL

select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           v.product_uid as productuid, 
           v.repteamuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           sum(v.kpivalue) kpivalue
    from {{ref('kpi_data_calc_interim')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.product_uid = g.productuid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.countrycode = g.countrycode
     and g.modeltype = 'EVENT'
     join {{ref('kpitype_v')}} t on t.kpitypeid = g.kpitypeid
     where (g.kpitypelabel <> 'calls_completed' or COALESCE(t.aggregationlevel, 'Brand') = 'Brand') 
     and lower(coalesce(t.calculationmethod,'sum')) <> 'average'
     group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid, v.product_uid, v.repteamuid, v.countrycode

UNION ALL

select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           v.product_uid as productuid, 
           v.repteamuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           CAST(avg(v.kpivalue) as DOUBLE) as kpivalue
    from {{ref('kpi_data_calc_interim')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.product_uid = g.productuid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.countrycode = g.countrycode
     and g.modeltype = 'EVENT'
     join {{ref('kpitype_v')}} t on t.kpitypeid = g.kpitypeid
     where (g.kpitypelabel <> 'calls_completed' or COALESCE(t.aggregationlevel, 'Brand') = 'Brand') 
     and lower(coalesce(t.calculationmethod,'sum')) = 'average'
     group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid, v.product_uid, v.repteamuid, v.countrycode

UNION ALL

{% set globalkpiproducts = run_query(globalkpiproductsquery) %}
 {% if execute %}
select * from (
    
    {% if globalkpiproducts.rows|length > 0 %} 
        {% for row in globalkpiproducts.rows %}
        select g.kpiid as kpiid,
            v.kpitypeid as kpitypeid,
            g.strategyid as strategyid, 
            v.fiscalperiodid, 
            '{{ row[1]}}' as productuid, 
            v.repteamuid,
            cast(null as integer) as kpitarget,
            '#NA#' as additionalsetting,
            v.countrycode,
            sum(v.kpivalue) kpivalue
            from {{ref('kpi_data_calc_interim')}}  v
            join {{ref('kpi_map_global_v')}}  g on
            v.fiscalperiodid = g.fiscalperiodid
            and v.kpitypeid = g.kpitypeid
            and v.repteamuid = g.repteamuid
            and g.repteamuid = '{{ row[0]}}'
            and v.countrycode = g.countrycode
            and g.modeltype = 'EVENT'
            join {{ref('kpitype_v')}} t on t.kpitypeid = g.kpitypeid
            where (g.kpitypelabel = 'calls_completed' or COALESCE(t.aggregationlevel, 'Brand') <> 'Brand')
                  and lower(coalesce(t.calculationmethod,'sum')) <> 'average'
            group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid, v.repteamuid, v.countrycode
            union all
        select g.kpiid as kpiid,
            v.kpitypeid as kpitypeid,
            g.strategyid as strategyid, 
            v.fiscalperiodid, 
            '{{ row[1]}}' as productuid, 
            v.repteamuid,
            cast(null as integer) as kpitarget,
            '#NA#' as additionalsetting,
            v.countrycode,
            CAST(avg(v.kpivalue) as DOUBLE) as kpivalue
            from {{ref('kpi_data_calc_interim')}}  v
            join {{ref('kpi_map_global_v')}}  g on
            v.fiscalperiodid = g.fiscalperiodid
            and v.kpitypeid = g.kpitypeid
            and v.repteamuid = g.repteamuid
            and g.repteamuid = '{{ row[0]}}'
            and v.countrycode = g.countrycode
            and g.modeltype = 'EVENT'
            join {{ref('kpitype_v')}} t on t.kpitypeid = g.kpitypeid
            where (g.kpitypelabel = 'calls_completed' or COALESCE(t.aggregationlevel, 'Brand') <> 'Brand')
                  and lower(coalesce(t.calculationmethod,'sum')) = 'average'
            group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid, v.repteamuid, v.countrycode
            {% if not loop.last %} UNION ALL {% endif %}
        {% endfor %}
    {% else %}
        select
            cast(null as integer) as kpiid
          , cast(null as integer) as kpitypeid 
          , cast(null as integer) as strategyid 
          , cast(null as integer) as fiscalperiodid 
          , cast(null as varchar) as productuid 
          , cast(null as varchar) as repteamuid 
          , cast(null as integer) as kpitarget 
          , cast(null as varchar) as additionalsetting 
          , cast(null as varchar) as countrycode 
          , cast(null as double) as kpivalue
        where 1 = 0
    {% endif %}
)
{% endif %}
 {% set results = run_query(repquery) %}
 {% if execute %}
      {% if results|length > 0 %}
              UNION ALL
      {% endif %}        
      {% for row in results.rows %}
    select {{ row[0] }} as kpiid,
           v.kpitypeid,
           {{ row[2] }} as strategyid, 
           v.fiscalperiodid, 
           '{{ row[3] }}' as productuid, 
           v.repteamuid,
           {{ row[5] }} as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           CAST(avg(v.kpivalue) as DOUBLE) as kpivalue
    from {{ref('rep_data_calculation_v')}}  v
       inner join {{ref('strategy_fiscalperiod_past_map_v')}} m on m.strategyid = {{ row[2] }} 
                                                       and m.fiscalperiodid = v.fiscalperiodid
                                                       and m.countrycode = v.countrycode
     where v.kpitypeid = {{ row[1] }}
     and v.repteamuid = '{{ row[4]}}'
     group by v.kpitypeid, m.strategyid, v.fiscalperiodid, v.repteamuid, v.countrycode
        {% if not loop.last %} UNION ALL {% endif %}

    {% endfor %}
{% endif %}
UNION ALL
select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           g.productuid, 
           v.repteamuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           CAST(avg(v.kpivalue) AS DOUBLE) as kpivalue
    from {{ref('rep_data_calculation_v')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.countrycode = g.countrycode
     and g.modeltype = 'REP'
     and g.kpitypelabel not in ('cycle_plan_attainment_brand','cycle_plan_attainment_visit_brand','cycle_plan_attainment_rte_brand','cycle_plan_attainment_remote_brand',
              'cycle_plan_attainment_overall','cycle_plan_attainment_visit','cycle_plan_attainment_rte','cycle_plan_attainment_remote')
     group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid,g.productuid, v.repteamuid, v.countrycode
UNION ALL
select * from  {{ref('cycleplan_summary_calculation')}}    