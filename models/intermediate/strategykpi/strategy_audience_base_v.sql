-- depends_on: {{ ref('accountproduct_with_time_grain_v') }}
-- depends_on: {{ ref('repaccount_with_time_grain') }}
-- depends_on: {{ ref('account_with_time_grain_v') }}

{{ config(materialized='table', bucketed_by=['recordlevel', 'accdate'], bucket_count=50) }}

with repacct_align as (
  select accountid,p.repid, p.repuid from 
  {{ ref('account_alignment_current_v') }} cross join unnest(aligned_reps) as reps(p)
),
relv_frequency_map as (
  select frequency, startdate, enddate from {{ ref('strategy_fiscalperiod_map_v') }}
          where startdate >= {{build_rolling_two_years_date(2)}} and startdate <= current_date and
                CASE
                  WHEN frequency = 'MONTH' Then enddate <= date_trunc('MONTH', startdate) + interval '1' month - interval '1' day
                  WHEN frequency = 'QUARTER' THEN enddate <= date_trunc('MONTH', startdate) + interval '3' month - interval '1' day
                  WHEN frequency = 'SEMESTER' THEN enddate <= date_trunc('MONTH', startdate) + interval '6' month - interval '1' day
                  WHEN frequency = 'TRIMESTER' THEN enddate <= date_trunc('MONTH', startdate) + interval '4' month - interval '1' day
                  WHEN frequency = 'YEAR' THEN enddate <= date_trunc('MONTH', startdate) + interval '1' year - interval '1'  day
                  ELSE enddate <= {{build_get_end_of_the_month(2)}}
	              END
)
select distinct
{% if build_check_table_exists('Account') == true %}
  {{ dbt_utils.star(from=ref('account_with_time_grain_v'),  relation_alias='a', except=['countrycode','recordlevel','accdate','externalid']) }},
{% endif %}
{% if build_check_table_exists('AccountProduct') == true %}
  {{ dbt_utils.star(from=ref('accountproduct_with_time_grain_v'), relation_alias='b', except=['countrycode','recordlevel','accountid','apdate']) }},
{% endif %}
{{ dbt_utils.star(from=ref('rep_with_time_grain_v'), relation_alias='d', except=['countrycode','recordlevel','repdate','externalid']) }},
{% if build_check_table_exists('RepAccountAssignment') == true %}
  {{ dbt_utils.star(from=ref('repaccount_with_time_grain'), relation_alias='e', except=['countrycode','recordlevel','apdate','repid','accountid']) }},
{% endif %}  
c.repid, c.repuid, a.externalid as accountuid, a.recordlevel, a.accdate
from {{ ref('account_with_time_grain_v') }} a 
  inner join relv_frequency_map r on a.recordlevel = r.frequency and a.accdate >= r.startdate and a.accdate <= r.enddate
  {% if build_check_table_exists('AccountProduct') == true %}
    left join 
    {{ ref('accountproduct_with_time_grain_v') }} b on a.accountid = b.accountid and a.accdate = b.apdate and a.recordlevel = b.recordlevel 
  {% endif %}
  inner join
  repacct_align c on a.accountid = c.accountid 
  inner join
  {{ref('rep_with_time_grain_v')}} d on c.repuid = d.externalid and a.accdate = d.repdate and a.recordlevel = d.recordlevel
  {% if build_check_table_exists('RepAccountAssignment') == true %}
    inner join
    {{ref('repaccount_with_time_grain')}} e on c.repid = e.repid and a.accdate = e.apdate and a.accountid = e.accountid and a.recordlevel = e.recordlevel
  {% endif %}
  inner join
  {{ref('account_alignment_engine_with_timegrain')}} f on c.repid = f.repid and a.accountid = f.accountid and a.accdate = f.apdate and a.recordlevel = f.recordlevel

