{{ config(materialized='table') }}

with single_row_rep as (
    select
		  t1.externalid,
		  t2.dt,
          min(date_diff('day', t1.startDate, t1.endDate)) diff
    from {{ ref('back_fill_rep') }} t1
	 	 join {{ ref('as_of_date') }} t2
		 on t2.dt between CAST(t1.startdate as DATE) and CAST(t1.enddate as DATE)		    
                group by t1.externalid, t2.dt 
),
unique_rep_entries as (
	select
	t1.cluster
	,t1.districtname
	,t1.repname
	,t1.territoryname
	,t1.territoryid
	,t1.externalid
	,t1.customerid
	,t1.regionname
	,t1.activateddate
	,t1.isactivated
	,t1.seconfigid
	,t1.testrungroupid
	,t1.rungroupid
	,t1.configcountrycode
	,t1.reptypename
	,t1.workweekname
	,t1.dmname
	,t1.dmuid
	,t1.startdate
	,t1.enddate
	from {{ ref('back_fill_rep') }} t1 group by 
		t1.cluster
		,t1.districtname
		,t1.repname
		,t1.territoryname
		,t1.territoryid
		,t1.externalid
		,t1.customerid
		,t1.regionname
		,t1.activateddate
		,t1.isactivated
		,t1.seconfigid
		,t1.testrungroupid
		,t1.rungroupid
		,t1.configcountrycode
		,t1.reptypename
		,t1.workweekname
		,t1.dmname
		,t1.dmuid
		,t1.startdate
		,t1.enddate
),
rep_details as (			
	select
		t1.*,
		t2.dt,
		date_diff('day', t1.startDate, t1.endDate) as maindiff
	FROM
		unique_rep_entries t1
		join {{ ref('as_of_date') }} t2
		on t2.dt between CAST(t1.startdate as DATE) and CAST(t1.enddate as DATE)
),
rep_fields as (
	select 	
		a.externalid,
		a.cluster,
		a.territoryname,
		a.districtname,
		a.regionname,
		a.configcountrycode,
		a.dt
	from 	rep_details a
	join single_row_rep b on a.externalid = b.externalid
	and a.dt = b.dt
	and a.maindiff = b.diff
)
select 
	a.externalid,
	a.cluster as repteamname,
	a.cluster as repteamuid,
	a.territoryname as territoryname,
	a.districtname as districtname,
	a.regionname as regionname,
	a.configcountrycode as configcountrycode,
    a.dt
from rep_fields	a