{{ config(materialized='table') }}

with accprod_with_mindate as (
select accountid, productid, min(updatedat) min_date from {{ ref('accountproduct_cdc_v') }}
where CAST(updatedat AS DATE)  > {{build_rolling_two_years_date(2)}} 
group by accountid, productid
), min_date_row as ( select a.*
    from {{ ref('accountproduct_cdc_v') }} a join accprod_with_mindate b
    on a.accountid = b.accountid and a.productid = b.productid and a.updatedat = b.min_date
) select {{ dbt_utils.star(from=ref('accountproduct_cdc_v'), 
    except=['updatedatyear','updatedatmonth','updatedatday', 'updatedat']) }},
    cast(EXTRACT(YEAR FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatyear,
    cast(EXTRACT(MONTH FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatmonth,
    cast(EXTRACT(DAY FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatday,
    {{build_rolling_two_years_date(2)}} AS updatedat from min_date_row 
UNION
select {{ dbt_utils.star(from=ref('accountproduct_cdc_v'), 
    except=['updatedat']) }}, updatedat from {{ ref('accountproduct_cdc_v') }}
