-- depends_on: {{ ref('kpitype_v') }}
-- depends_on: {{ ref('fiscalperiod_country_normalized') }}
-- depends_on: {{ ref('strategy_v') }}
-- depends_on: {{ ref('fiscalperioddefinition_country_normalized') }}

{{ config(materialized='view') }}

 {% set query %}
        SELECT frequency,countrycode FROM {{ref('fiscalperioddefinition_country_normalized')}} v where isDefault = true;
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}

with 
kpitype as 
(
   select distinct kpitypeid,upper(modeltype) as modeltype, additionalsetting,kpitypelabel from {{ ref ('kpitype_v') }} 
),
{% for row in results.rows %}
periods_{{row[1]}} as (
  select * from (select fiscalperiodid,countrycode from  {{ ref('fiscalperiod_country_normalized') }}  v 
where v.startdate <= CURRENT_DATE
  and v.frequency = '{{ row[0] }}'
  and v.countrycode = '{{ row[1] }}'
order by v.startdate desc limit 7)
UNION
select * from (select fiscalperiodid,countrycode from  {{ ref('fiscalperiod_country_normalized') }}  v 
where v.startdate <= CURRENT_DATE
  and v.frequency = '{{ row[0] }}'
  and v.countrycode = '{{ row[1] }}'
  and v.startdate >= to_date(date_format(now(), '%Y-01-01'), 'yyyy-mm-dd'))
),
 {% endfor %}
periods as (
   {% for row in results.rows %}
select * from periods_{{row[1]}}
{% if not loop.last %} UNION ALL {% endif %}
{% endfor %}
), 
strategies as 
   (select distinct countrycode,productuid,repteamuid
   from {{ ref('strategy_v') }} )
select distinct -99 as kpiid,
                -999 as strategyid, 
                s.countrycode, 
                s.productuid, 
                s.repteamuid,
                p.fiscalperiodid,
                k.kpitypeid,
                k.additionalsetting,
                k.modeltype,
                k.kpitypelabel
from strategies s, kpitype k, periods p
where s.countrycode = upper(p.countrycode) ;

{% endif %}