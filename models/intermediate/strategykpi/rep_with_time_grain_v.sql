{{ config(materialized='table', bucketed_by=['recordlevel', 'repdate'], bucket_count=50) }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with rep_details as (
	select distinct a.externalid,
		a.repteamname,
		a.repteamuid,
		a.territoryname,
		a.districtname,
		a.regionname,
		b.countrycode,	    
 	    b.frequency as recordlevel,
		b.enddate as repdate,
		b.fiscalperiodid,
		b.startdate
		from {{ ref('rep_details_v') }} a
       join {{ ref('fiscalperiod_country_normalized') }} b
          -- on YEAR(a.dt) = YEAR(b.enddate) and (month(a.dt) = month(b.enddate) or quarter(a.dt) = quarter(b.enddate))
          on a.dt = b.enddate
		  and a.dt >= {{build_rolling_two_years_date(2)}} 
          {%- if var_multi_country == 'true' %}  
              and b.countrycode = a.configcountrycode
          {%- endif %}
), rep_holiday as (
    select externalid, dt, country as countrycode from {{ ref('repavailableperiod') }}
)
select b.externalid,
		b.repteamname,
		b.repteamuid,
		b.territoryname,
		b.districtname,
		b.regionname,
		b.countrycode,	    
 	    b.recordlevel,
		b.repdate,
		b.fiscalperiodid,
		count(a.dt) as working_day_count
from rep_holiday a
join rep_details b  on a.externalid = b.externalid 
    and a.countrycode = b.countrycode
    and (a.dt >= b.startdate AND a.dt <= b.repdate)
GROUP BY
    b.startdate,
	b.repdate,
	b.externalid,
	b.repteamname,
	b.repteamuid,
	b.territoryname,
	b.districtname,
	b.regionname,
	b.countrycode,	    
 	b.recordlevel,
	b.repdate,
	b.fiscalperiodid		  
