{{ config(materialized='table', bucketed_by=['recordlevel', 'accdate'], bucket_count=50) }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

select distinct b.accountid,
       b.externalid,
       {% if build_check_table_exists('Account') == true %}
         {{ build_get_attribute_names('Account', 'b') }} ,
       {% endif %}
       a.countrycode,
 	    a.frequency as recordlevel,
	    a.enddate as accdate
   from {{ ref('account_details_v') }} b
     join {{ ref('account_dse_v') }} d on b.accountid = d.accountid 
     join {{ ref('fiscalperiod_country_normalized') }} a
          on YEAR(b.dt) = YEAR(a.enddate) and (month(b.dt) = month(a.enddate) or quarter(b.dt) = quarter(a.enddate))
          and b.dt >= {{build_rolling_two_years_date(2)}}
     {%- if var_multi_country == 'true' %}  
        and a.countrycode = d.configcountrycode
      {%- endif %}
