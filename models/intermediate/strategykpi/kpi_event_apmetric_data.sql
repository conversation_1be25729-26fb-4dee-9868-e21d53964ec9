{{ config(materialized='table') }}

{% set default_timegrain_query %}
select lower(frequency) from {{ ref('fiscalperioddefinition_default_v') }} where isdefault = true
{% endset %}

{% set results = run_query(default_timegrain_query) %}
{% if execute %}
{% set default_timegrain = results.columns[0].values() %}
{% else %}
{% set default_timegrain = ['MONTH'] %}
{% endif %}

with accountproduct_with_default_fiscalperiod as
(
    select ag.* from {{ ref('accountproduct_with_time_grain_v') }} ag
    inner join {{ ref('fiscalperioddefinition_default_v')}} fp on recordlevel = frequency and isdefault = true 
),

ap_data_level_ranked as
(
select ag.recordlevel, ag.countrycode, ag.apdate, ape.*,
row_number() over(partition by ape.accountid, ape.productid, ag.recordlevel, ag.apdate order by ape.updatedat desc) as row_no
from accountproduct_with_default_fiscalperiod ag 
inner join {{ ref('accountproduct_cdc_expiry_date_v') }} ape on ag.accountid = ape.accountid and ag.productid = ape.productid 
where ag.apdate >= ape.updatedat and ag.apdate <= ape.updatedat_expiry_dt 
),

ap_data_level_latest as
(
    select * from ap_data_level_ranked where row_no = 1
),

ap_data_level_latest_mapped as
(
  select a.*,
         acct.externalid as accountuid,
         prod.externalid as product_uid,
         prod.productid as product_id,
         acct.accountname,
         prod.productname as product_name,
         aa.repuid,
         r.repname,
         aa.repid,
         cast(null as varchar) as facilityuid,
         cast(null as varchar) as facilityname,
         cast(null as integer) as facilityid,
         a.apdate as eventdate,
         a.recordlevel as eventtypename,
       {%- if var_multi_country == 'false' %}  
           '{{ build_country() }}' as configcountrycode
       {%- else %}
            acct.configcountrycode as configcountrycode
       {%- endif %}
  from ap_data_level_latest a
  inner join {{ ref('account_dse_v') }} acct on a.accountid = acct.accountid
  inner join {{ ref('product_v') }} prod on a.productid = prod.productid
  inner join {{ ref('account_alignment_engine_monthly')}} aa on a.accountid = aa.accountid
  and aa.startdatelocal = date_trunc('{{ default_timegrain[0] }}', a.apdate) -- align to first day of the month
  inner join {{ ref('rep_v') }} r on r.externalid = aa.repuid
)

select * from ap_data_level_latest_mapped
