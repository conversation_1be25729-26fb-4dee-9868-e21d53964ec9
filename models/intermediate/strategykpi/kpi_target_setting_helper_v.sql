{{ config(materialized='view') }}

with targets as (
select k.strategyid,  k.kpitypeid, array_agg(p.fiscalperiodid) as fiscalperiodids, array_sort(array_agg(p.startdate)) as startdates, 
min(k.kpivalue) min_kpivalue, max(k.kpivalue) max_kpivalue, 
avg(k.kpivalue) avg_kpivalue, approx_percentile(k.kpivalue,0.5) median_kpivalue, stddev(k.kpivalue) stddev_kpivalue,
approx_percentile(k.kpivalue,0.25) p25_kpivalue, approx_percentile(k.kpivalue,0.75) p75_kpivalue,
approx_percentile(k.kpivalue,0.90) p90_kpivalue
from {{ref('kpi_target_calculation')}} as k 
 inner join {{ref('strategy_fiscalperiod_past_map_v')}} as p on k.strategyid = p.strategyid and k.fiscalperiodid = p.fiscalperiodid 
 left join {{ref('strategy_fiscalperiod_map_v')}} as f on k.strategyid = f.strategyid and k.fiscalperiodid = f.fiscalperiodid 
where k.strategyid <> -999 and f.strategyid is null and kpivalue > 0.0
group by k.strategyid, k.kpitypeid
)
select  t.strategyid, name, t.kpitypeid, kpitypename, kpitypelabel, array_join(t.fiscalperiodids, ',') as fiscalperiodids, 
array_join(t.startdates, ',') as startdates,
round(min_kpivalue,2) as min_kpivalue, round(max_kpivalue,2) as max_kpivalue, 
round(avg_kpivalue, 2) as avg_kpivalue,   
round(stddev_kpivalue,2) stddev_kpivalue, round(p25_kpivalue,2) p25_kpivalue, round(median_kpivalue,2) as p50_kpivalue,
round(p75_kpivalue,2) p75_kpivalue, round(p90_kpivalue,2) p90_kpivalue
from targets t inner join {{ref('strategy_v')}} s on t.strategyid = s.strategyid
inner join {{ref('kpitype_v')}} as k  on t.kpitypeid = k.kpitypeid
order by t.strategyid, t.kpitypeid
