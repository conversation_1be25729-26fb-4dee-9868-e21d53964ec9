{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

 select distinct b.repid, b.repuid,
        b.accountid,
        b.accountuid,
        a.countrycode,
	   a.enddate as apdate,
 	   a.frequency as recordlevel  from {{ ref('account_alignment_engine_monthly') }} b 
        join {{ ref('account_dse_v') }} d on b.accountid = d.accountid 
        join {{ ref('fiscalperiod_country_normalized') }}  a
          on YEAR(b.startdatelocal) = YEAR(a.enddate) and (month(b.startdatelocal) = month(a.enddate) or quarter(b.startdatelocal) = quarter(a.enddate))
          and b.startdatelocal >= {{build_rolling_two_years_date(2)}} 
          {%- if var_multi_country == 'true' %}  
              and a.countrycode = d.configcountrycode
          {%- endif %}