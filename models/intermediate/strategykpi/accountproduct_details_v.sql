{{ config(materialized='table') }}

-- depends_on: {{ ref('back_fill_accountproduct') }}
-- depends_on: {{ ref('as_of_date') }}
-- depends_on: {{ ref('fiscalperioddefinition_v') }}
-- depends_on: {{ ref('fiscalperiod_v') }}

{% if build_check_table_exists('AccountProduct') == false %}
  with interpolated_back_fill_accountproduct as (
   select a.*, a.updatedat as dt from {{ ref('back_fill_accountproduct') }} a
  ) 
{% else %}
   {{ build_get_attribute_type_def('AccountProduct', 'b.accountid,b.productid', 'b.accountid = a.accountid and b.productid = a.productid') }}
{% endif %}
select 
   *
from interpolated_back_fill_accountproduct