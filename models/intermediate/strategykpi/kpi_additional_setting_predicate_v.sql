with v1 as (
select
    kt.kpitypeid AS kpitypeid,
    coalesce(rt.attributetypeName, '') AS additionalsetting,
    atd.rdsobjectname AS rdsobjectname,    
    atd.rdsfieldname AS rdsfieldname,
    atd.s3objectname AS s3objectname,
    atd.s3fieldname AS s3fieldname
from {{ ref('kpitype_v') }} kt
left join {{ ref('attributetypedefinition_v') }} atd on kt.additionalsetting = atd.attributetypedefinitionname
left join {{ ref('attributetype_v') }} rt on atd.attributetypedefinitionname = rt.attributetypedefinitionname
where coalesce(rt.attributetypename, '') <> '')
select 
v.kpiid, v.kpitypeid, v1.additionalsetting, concat(s3fieldname, ' in (''', v1.additionalsetting, ''')') additionalsetting_sql
from {{ ref('kpi_v') }} v join v1 on v1.kpitypeid = v.kpitypeid and v.additionalsetting = v1.additionalsetting
