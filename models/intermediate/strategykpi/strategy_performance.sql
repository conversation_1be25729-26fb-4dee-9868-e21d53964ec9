{{ config(materialized='table') }}

-- depends_on: {{ ref('strategy_fiscalperiod_map_v') }}
-- depends_on: {{ ref('strategy_audience_base_v') }}
-- depends_on: {{ ref('strategy_v') }}
-- depends_on: {{ ref('strategy_audience_predicate_v') }}
-- depends_on: {{ ref('kpi_event_interaction_explode') }}


with audience_size as 
(
    select 
        strategyid,
        fiscalperiodid,
        countrycode,
        count(distinct accountid) as audiencesize
        from {{ ref('audience_pool') }} 
        group by strategyid, fiscalperiodid, countrycode
),
event_pool as 
(
    select 
        a.strategyid,
        a.fiscalperiodid,
        a.countrycode,
        count(distinct case when eventtypename in ('KPIVISIT-COMPLETED','KPISEND_ANY-COMPLETED','KPIWEB_EDETAIL-COMPLETED') then b.accountid end) audiencereach,
        count(distinct suggestionreferenceid) suggestioncount,
        count(distinct case when eventtypename in ('KPIVISIT-COMPLETED','KPISEND_ANY-COMPLETED','KPIWEB_EDETAIL-COMPLETED') then interactionuid end) interactioncount,
        count(distinct (case when actiontaken = 'Suggestions Completed' then suggestionreferenceid end)) as suggestion_accepted_count,
        count(distinct (case when actiontaken = 'Suggestions Dismissed' 
                                     and (NOT (coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is not null))
                                     and (NOT (coalesce(issuggestioncompletedinfer, 0) = 1)) 
                                     and (NOT (coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is null))
                        then suggestionreferenceid end)) as suggestion_dismissed_count,     
        count(distinct (case when actiontaken = 'Suggestions Completed' 
                                     and (NOT (coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is not null))
                                     and coalesce(issuggestioncompletedinfer, 0) = 1
                        then suggestionreferenceid end)) as suggestion_inferred_accepted_count,
        count(distinct (case when actiontaken = 'Suggestions Completed' 
                                     and coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is not null 
                        then suggestionreferenceid end)) as suggestion_direct_accepted_count,
        count(distinct (case when actiontaken = 'Suggestions Completed'
                                     and (NOT (coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is not null))
                                     and (NOT (coalesce(issuggestioncompletedinfer, 0) = 1)) 
                                     and coalesce(issuggestioncompleteddirect, 0) = 1 and reportedinteractionuid is null 
                        then suggestionreferenceid end)) as suggestion_marked_completed_count
    from 
        {{ ref('strategy_fiscalperiod_map_v') }} a inner join 
        {{ ref('kpi_event_interaction_explode') }} b on b.strategyid = a.strategyid and b.eventdate >= a.startdate and b.eventdate <= a.enddate
        left join {{ ref('audience_pool') }} c on a.strategyid = c.strategyid and a.fiscalperiodid = c.fiscalperiodid and b.accountid = c.accountid 
        and suggestionreferenceid is not null
    group by a.strategyid, a.fiscalperiodid, a.countrycode
)
select a.strategyid, a.fiscalperiodid, audiencesize, a.countrycode, audiencereach, 
                    case when audiencesize > 0 then round((audiencereach*1.0/audiencesize), 4) else 0.0 end as audiencereachpercent,
                    suggestioncount, interactioncount,
                    case when suggestioncount > 0 then round((interactioncount*1.0/suggestioncount), 4) else 0.0 end as nbaadoptionpercent,
                    suggestion_accepted_count, suggestion_dismissed_count, suggestion_inferred_accepted_count, 
                    suggestion_direct_accepted_count, suggestion_marked_completed_count,
                    round(((suggestion_inferred_accepted_count+suggestion_direct_accepted_count+suggestion_dismissed_count+suggestion_marked_completed_count)*1.0 / suggestioncount),4) as suggestion_engagement_percent
from audience_size a left join event_pool b on a.strategyid = b.strategyid and a.fiscalperiodid = b.fiscalperiodid and coalesce(a.countrycode, 'N/A') = coalesce(b.countrycode, 'N/A')
