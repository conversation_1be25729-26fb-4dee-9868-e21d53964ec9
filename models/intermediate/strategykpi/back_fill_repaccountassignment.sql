{{ config(materialized='table') }}

with repacc_with_mindate as (
select repid, accountid, min(updatedat) min_date from {{ ref('repaccountassignment_cdc_v') }}
where CAST(updatedat AS DATE)  > {{build_rolling_two_years_date(2)}} group by repid, accountid
), min_date_row as ( select a.*
    from {{ ref('repaccountassignment_cdc_v') }} a join repacc_with_mindate b
    on a.repid = b.repid and a.accountid = b.accountid and a.updatedat = b.min_date
) select {{ dbt_utils.star(from=ref('repaccountassignment_cdc_v'), 
    except=['createdat','updatedatyear','updatedatmonth','updatedatday', 'updatedat']) }},
    {{build_rolling_two_years_date(2)}} AS createdat,
    cast(EXTRACT(YEAR FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatyear,
    cast(EXTRACT(MONTH FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatmonth,
    cast(EXTRACT(DAY FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatday,
    {{build_rolling_two_years_date(2)}} AS updatedat from min_date_row 
UNION
select {{ dbt_utils.star(from=ref('repaccountassignment_cdc_v'), 
    except=['createdat','updatedatyear','updatedatmonth','updatedatday','updatedat']) }}, 
    createdat, updatedatyear, updatedatmonth, updatedatday, updatedat from {{ ref('repaccountassignment_cdc_v') }}
