{{ config(materialized='view') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

WITH
  kpimap AS (
   SELECT
     a.kpiid
   , a.kpitypeid
   , b.countrycode
   , b.frequency
   , min(b.startdate) minstartdate
   , max(enddate) maxenddate
   FROM
     {{ ref('kpi_map_v') }} a
   , {{ ref('fiscalperiod_country_normalized') }} b
   WHERE ((startperiod = periodname) OR (endperiod = periodname))
   GROUP BY a.kpiid, a.kpitypeid, b.countrycode, b.frequency
) 
SELECT
  a.kpiid
, a.countrycode
, a.kpitypeid
, c.fiscalperiodid
FROM
  kpimap a
, {{ ref('fiscalperiod_country_normalized') }} c
WHERE ((c.startdate >= a.minstartdate) AND (c.enddate <= a.maxenddate) AND (a.frequency = c.frequency)
{%- if var_multi_country == 'true' %}  
   AND (a.countrycode = c.countrycode)
{%- endif %}
)
ORDER BY a.kpiid ASC

