{{ config(materialized='view') }}

with cte_data as 
(
SELECT
    strategyid,
    s3fieldname,
    replace(replace(replace(replace(replace(json_format(json_EXTRACT(e, '$.typeNames')), '\"', ''), '''', ''), '"', ''''), '[', ''), ']', '') as valueslist,
    concat('cast(', s3fieldname, ' as varchar) ', ' IN ', replace(replace(replace(replace(replace(json_format(json_EXTRACT(e, '$.typeNames' )),'\"',''),'''',''), '"', ''''),'[','('),']',')')) as predicate_part,
    json_extract_scalar(e, '$.typeDefinitionName') typeDefinitionName,
    audienceconditions,
    a.multivalued
FROM {{ ref('strategy_v') }} as strategy_v
cross join UNNEST(CAST(json_parse(audienceconditions) AS array(json))) t(e) 
inner join {{ ref('attributetypedefinition_v') }} a on (json_extract_scalar(e, '$.typeDefinitionName') = attributetypedefinitionname)
where cast(a.isdeleted as boolean) = false
),
cte_predicate_parts as
(
select
    t.strategyid,
    t.predicate_part,
    t.typeDefinitionName,
    t.audienceconditions,
    t.multivalued,
    '('||array_join(array_agg(concat('cast(', t.s3fieldname, ' as varchar) like ''%', trim(x.value, ''''), '%''')),' or ')||')' as alternative_predicate_part
from cte_data t
cross join UNNEST(split(t.valueslist, ',')) as x(value)
group by
    t.strategyid,
    t.predicate_part,
    t.typeDefinitionName,
    t.audienceconditions,
    t.multivalued
)
select
    strategyid,
    array_join(array_agg(
    case when coalesce(cast(multivalued as integer), 0) = 0
                 then predicate_part
            else  
                      alternative_predicate_part
        end), ' and ') as audience_predicate_sql
from cte_predicate_parts
group by strategyid