-- depends_on: {{ ref('kpi_data_calc_interim') }}
-- depends_on: {{ ref('kpi_additional_setting_predicate_v') }}
-- depends_on: {{ ref('strategy_audience_predicate_v') }}
-- depends_on: {{ ref('strategy_fiscalperiod_map_v') }}
-- depends_on: {{ ref('kpi_map_global_v') }}

{{ config(materialized='table') }}


 {% set query %}
        SELECT v.kpiid,
               v.kpitypeid,
               v.strategyid,
               v.productuid,
               v.repteamuid,
               ads.additionalsetting_sql,
               acs.audience_predicate_sql,
               v.targetvalue,
               COALESCE(ads.additionalsetting, '#NA#') as additionalsetting,
               v.kpitypelabel,
               COALESCE(t.aggregationlevel, 'Brand') as aggregationlevel,
               lower(COALESCE(t.calculationmethod, 'sum')) as calculationmethod
         FROM {{ref('kpi_map_v')}} v
          inner join {{ref('kpitype_v')}} t on t.kpitypeid = v.kpitypeid
          left join {{ref('kpi_additional_setting_predicate_v')}} ads on ads.kpiid = v.kpiid
          left join {{ref('strategy_audience_predicate_v')}} acs on acs.strategyid = v.strategyid and v.applyaudiencecondition = true
          and audience_predicate_sql not like '%AUTO_QA%'
         WHERE v.modeltype in ('EVENT', 'MESSAGE_TOPIC')
         
 {% endset %}

 {% set results = run_query(query) %}
 {% if execute %}
    {% if results.rows|length > 0 %}
        {% for row in results.rows %}
        select {{ row[0] }} as kpiid,
            v.kpitypeid,
            m.strategyid, 
            v.fiscalperiodid, 
            v.product_uid as productuid, 
            v.repteamuid,
            v.repuid,
            {{ row[7] }} as kpitarget,
            '{{ row[8] }}' as additionalsetting,
            v.countrycode,
            {% if row[9] in ('message_topic_coverage', 'message_topic_clm_coverage', 'message_topic_rte_coverage') or row[11] == 'average' %}
                coalesce(cast(avg(v.kpivalue) as double), 0) kpivalue
            {% else %}
                {% if row[7] != 0 %}
                   {% if row[11] == 'average' %}
                      coalesce(cast(avg(v.kpivalue) as double)/nullif({{ row[7] }}, 0), 0) kpivalue
                   {% else %}
                      coalesce(cast(sum(v.kpivalue) as double)/nullif({{ row[7] }}, 0), 0) kpivalue
                   {% endif %}
                {% else %}
                   {% if row[11] == 'average' %}
                      coalesce(cast(avg(v.kpivalue) as double), 0) kpivalue
                   {% else %}
                      coalesce(cast(sum(v.kpivalue) as double), 0) kpivalue
                   {% endif %}
                {% endif %}
            {% endif %}
        from {{ref('kpi_data_calc_interim')}}  v
        inner join {{ref('strategy_fiscalperiod_map_v')}} m on m.strategyid = {{ row[2] }} 
                                                        and m.fiscalperiodid = v.fiscalperiodid
                                                        and m.countrycode = v.countrycode
        where v.kpitypeid = {{ row[1] }}
        and v.repteamuid = '{{ row[4]}}'
        {% if row[10] == 'Brand' %}
        and v.product_uid = '{{ row[3] }}'
        {% endif %}
        {% if row[5] %} and {{ row[5] }} {% endif %}
        {% if row[6] %} and {{ row[6] }} {% endif %}
        group by v.kpitypeid, m.strategyid, v.fiscalperiodid, v.product_uid, v.repteamuid, v.repuid, v.countrycode
            {% if not loop.last %} UNION ALL {% endif %}
        {% endfor %}
        UNION ALL
    {% endif %}
{% endif %}
select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           v.product_uid as productuid, 
           v.repteamuid,
           v.repuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           sum(v.kpivalue) kpivalue
    from {{ref('kpi_data_calc_interim')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.product_uid = g.productuid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.countrycode = g.countrycode
     and g.modeltype != 'MESSAGE_TOPIC'
     group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid, v.product_uid, v.repteamuid, v.repuid, v.countrycode