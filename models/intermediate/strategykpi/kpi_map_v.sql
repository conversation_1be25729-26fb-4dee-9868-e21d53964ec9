{{ config(materialized='view') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}


WITH
  kpi_summary_map AS (
   SELECT
     k.kpiid
   , k.kpitypeid
   , s.name strategyname
   , kt.kpitypename
   , s.startperiod
   , s.endperiod
   , k.additionalsetting
   , k.kpirank
   , k.targetvalue
   , s.strategyid
{%- if var_multi_country == 'false' %}  
   , '{{ build_country() }}' as countrycode
{%- else %}
   , s.countrycode
{%- endif %}
   , s.repteamuid
   , s.productuid
   , s.goalid
   , s.audienceconditions
   , s.strategyrank
   , s.status
   , kt.iscustom
   , kt.applyaudiencecondition
   , kt.iscalcactive
   , kt.aggregationlevel
   , kt.platform
   , kt.formula
   , kt.attribution
   , kt.additionalsetting kt_additionalsetting
   , kt.description
   , kt.formulareqts
   , upper(kt.modeltype) modeltype,
   kt.kpitypelabel
   FROM
     {{ ref('kpi_v') }} k INNER JOIN {{ ref('strategy_v') }} s ON k.strategyid = s.strategyid
   INNER JOIN {{ ref('kpitype_v') }} kt ON kt.kpitypeid = k.kpitypeid
   WHERE cast(k.isdeleted as boolean) = false AND cast(kt.isdeleted as boolean) = false
) 
SELECT *
FROM
  kpi_summary_map
