-- depends_on: {{ ref('strategy_fiscalperiod_map_v') }}
-- depends_on: {{ ref('kpi_map_global_v') }}
-- depends_on: {{ ref('rep_data_calculation_v') }}

{{ config(materialized='table') }}

 {% set repquery %}
        SELECT v.kpiid,
               v.kpitypeid,
               v.strategyid,
               v.productuid,
               v.repteamuid,
               v.targetvalue,
               v.kpitypelabel
         FROM {{ref('kpi_map_v')}} v
          where v.modeltype = 'REP' and
          v.kpitypelabel in ('cycle_plan_attainment_brand','cycle_plan_attainment_visit_brand','cycle_plan_attainment_rte_brand','cycle_plan_attainment_remote_brand',
              'cycle_plan_attainment_overall','cycle_plan_attainment_visit','cycle_plan_attainment_rte','cycle_plan_attainment_remote')
         
 {% endset %}

 {% set results = run_query(repquery) %}
 {% if execute %}       
      {% for row in results.rows %}
      {% if '{{row[6]}}' == 'cycle_plan_attainment_brand' or '{{ row[6]}}' == 'cycle_plan_attainment_visit_brand' or '{{ row[6]}}' == 'cycle_plan_attainment_rte_brand' or '{{ row[6]}}' == 'cycle_plan_attainment_remote_brand' %}  
              select {{ row[0] }} as kpiid,
                     v.kpitypeid,
                     {{ row[2] }} as strategyid, 
                     v.fiscalperiodid, 
                     '{{ row[3] }}' as productuid, 
                     v.repteamuid,
                     {{ row[5] }} as kpitarget,
                     '#NA#' as additionalsetting,
                     v.countrycode,
                     CAST(avg(v.kpivalue) as DOUBLE) as kpivalue
              from {{ref('rep_data_calculation_v')}}  v
                     inner join {{ref('strategy_fiscalperiod_map_v')}} m on m.strategyid = {{ row[2] }} 
                                                               and m.fiscalperiodid = v.fiscalperiodid
                                                               and m.countrycode = v.countrycode
              where v.kpitypeid = {{ row[1] }}
              and v.repteamuid = '{{ row[4]}}'
              and v.productuid = '{{ row[3] }}'
              group by v.kpitypeid, m.strategyid, v.fiscalperiodid, v.repteamuid, v.countrycode, v.productuid
                     {% if not loop.last %} UNION ALL {% endif %}
        {% else %} 
              select {{ row[0] }} as kpiid,
                     v.kpitypeid,
                     {{ row[2] }} as strategyid, 
                     v.fiscalperiodid,
                     '{{ row[3] }}' as productuid,
                     v.repteamuid,
                     {{ row[5] }} as kpitarget,
                     '#NA#' as additionalsetting,
                     v.countrycode,
                     CAST(avg(v.kpivalue) as DOUBLE) as kpivalue
              from {{ref('rep_data_calculation_v')}}  v
                     inner join {{ref('strategy_fiscalperiod_map_v')}} m on m.strategyid = {{ row[2] }} 
                                                               and m.fiscalperiodid = v.fiscalperiodid
                                                               and m.countrycode = v.countrycode
              where v.kpitypeid = {{ row[1] }}
              and v.repteamuid = '{{ row[4]}}'
              group by v.kpitypeid, m.strategyid, v.fiscalperiodid, v.repteamuid, v.countrycode
                     {% if not loop.last %} UNION ALL {% endif %}

        {% endif %} 

    {% endfor %}
{% endif %}
{% if results|length > 0 %}
       UNION ALL
{% endif %} 
select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           g.productuid, 
           v.repteamuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           CAST(avg(v.kpivalue) AS DOUBLE) as kpivalue
    from {{ref('rep_data_calculation_v')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.countrycode = g.countrycode
     and g.modeltype = 'REP'
     and g.kpitypelabel in ('cycle_plan_attainment_overall','cycle_plan_attainment_visit','cycle_plan_attainment_rte','cycle_plan_attainment_remote')
     group by g.kpiid,g.strategyid,v.kpitypeid, v.fiscalperiodid,g.productuid, v.repteamuid, v.countrycode
UNION ALL
select g.kpiid as kpiid,
           v.kpitypeid as kpitypeid,
           g.strategyid as strategyid, 
           v.fiscalperiodid, 
           v.productuid, 
           v.repteamuid,
           cast(null as integer) as kpitarget,
           '#NA#' as additionalsetting,
           v.countrycode,
           CAST(avg(v.kpivalue) AS DOUBLE) as kpivalue
    from {{ref('rep_data_calculation_v')}}  v
    join {{ref('kpi_map_global_v')}}  g on
     v.fiscalperiodid = g.fiscalperiodid
     and v.kpitypeid = g.kpitypeid
     and v.repteamuid = g.repteamuid
     and v.productuid = g.productuid
     and v.countrycode = g.countrycode
     and g.modeltype = 'REP'
     and g.kpitypelabel in ('cycle_plan_attainment_brand','cycle_plan_attainment_visit_brand','cycle_plan_attainment_rte_brand','cycle_plan_attainment_remote_brand' )
     group by g.kpiid, g.strategyid, v.kpitypeid, v.fiscalperiodid, v.productuid, v.repteamuid, v.countrycode     

