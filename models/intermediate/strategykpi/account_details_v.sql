{{ config(materialized='table') }}

-- depends_on: {{ ref('back_fill_account') }}
-- depends_on: {{ ref('as_of_date') }}
-- depends_on: {{ ref('fiscalperioddefinition_v') }}
-- depends_on: {{ ref('fiscalperiod_v') }}

{% if build_check_table_exists('Account') == false %}
  with interpolated_back_fill_account as (
   select a.*, a.updatedat as dt from {{ ref('back_fill_account') }} a
  ) 
{% else %}
   {{ build_get_attribute_type_def('Account', 'b.accountid,b.externalid', 'b.accountid = a.accountid') }}
{% endif %}
select 
   *
from interpolated_back_fill_account
