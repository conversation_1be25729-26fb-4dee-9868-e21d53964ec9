{{ config(materialized='table') }}

with custom_date_table as (
	select a.dt,
		b.startdate,
		b.enddate,
		b.frequency,
		b.countrycode
	from {{ ref('as_of_date') }} a
		join {{ ref('fiscalperiod_country_normalized') }} b on b.startdate <= a.dt
		and b.enddate >= a.dt
)
SELECT a.countrycode, a.dt AS date_day,
date_trunc('week', a.dt) AS date_week,
    MAX(CASE WHEN a.frequency = 'MONTH' THEN a.enddate END) AS date_month,
    MAX(CASE WHEN a.frequency = 'QUARTER' THEN a.enddate END) AS date_quarter,
    MAX(CASE WHEN a.frequency = 'TRIMESTER' THEN a.enddate END) AS date_trimester,
    MAX(CASE WHEN a.frequency = 'SEMESTER' THEN a.enddate END) AS date_semester,
    MAX(CASE WHEN a.frequency = 'YEAR' THEN a.enddate END) AS date_year
FROM custom_date_table a 
group by a.countrycode, a.dt