{{ config(materialized='table') }}


-- depends_on: {{ ref('strategy_fiscalperiod_map_v') }}
-- depends_on: {{ ref('strategy_audience_base_v') }}
-- depends_on: {{ ref('strategy_audience_predicate_v') }}

{% set strategyQuery %}
 SELECT strategyid, audience_predicate_sql
 FROM {{ ref('strategy_audience_predicate_v') }} 
 where audience_predicate_sql not like '%AUTO_QA%'
 order by strategyid asc     
{% endset %}

{% set results = run_query(strategyQuery) %}

{% if execute %}
with audience_pool as 
(
    {% if results|length > 0 %}
        {% for row in results.rows %}
            select 
                a.strategyid,
                a.fiscalperiodid,
                a.countrycode,
                b.accountid
            from 
            {{ ref('strategy_fiscalperiod_map_v') }} a inner join 
            {{ ref('strategy_audience_base_v') }} b on a.strategyid = {{ row[0] }} and a.frequency = b.recordlevel and
                a.repteamuid = b.repteamuid and b.accdate >= a.startdate and b.accdate <= a.enddate 
                {% if row[1] %}
                    AND  {{ row[1] }}
                {% endif %}
            group by
                a.strategyid,
                a.fiscalperiodid,
                a.countrycode,
                b.accountid
            {% if not loop.last %} UNION ALL {% endif %}
        {% endfor %}
    {% else %}
        select
            cast(null as integer) as strategyid,
            cast(null as integer) as fiscalperiodid,
            cast(null as varchar) as countrycode,
            cast(null as integer) as accountid
    {% endif %}
)
select 
        strategyid,
        fiscalperiodid,
        countrycode,
        accountid from audience_pool
{% else %}
    {{ "temp" }}
{% endif %}



