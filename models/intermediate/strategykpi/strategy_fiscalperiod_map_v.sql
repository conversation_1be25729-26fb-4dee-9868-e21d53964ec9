{{ config(materialized='view') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

WITH
  strategymap AS (
   SELECT
     a.strategyid,
     a.repteamuid
   , b.countrycode
   , b.frequency
   , min(b.startdate) minstartdate
   , max(enddate) maxenddate
   FROM
     {{ ref('strategy_v') }} a
   , {{ ref('fiscalperiod_country_normalized') }} b
   WHERE ((startperiod = periodname) OR (endperiod = periodname)) AND a.countrycode = b.countrycode
   GROUP BY a.strategyid, a.repteamuid, b.countrycode, b.frequency
) 
SELECT
  a.strategyid
, a.repteamuid
, a.countrycode
, c.fiscalperiodid
, c.frequency
, c.startdate
, c.enddate
FROM
  strategymap a
, {{ ref('fiscalperiod_country_normalized') }} c
WHERE ((c.startdate >= a.minstartdate) AND (c.enddate <= a.maxenddate) AND (a.frequency = c.frequency) 
{%- if var_multi_country == 'true' %}  
   AND (a.countrycode = c.countrycode)
{%- endif %}
      )
ORDER BY a.strategyid ASC

