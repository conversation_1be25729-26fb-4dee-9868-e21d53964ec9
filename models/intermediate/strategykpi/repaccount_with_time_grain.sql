{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

 select distinct b.repid,
        b.accountid,
        {% if build_check_table_exists('RepAccountAssignment') == true %}
         {{ build_get_attribute_names('RepAccountAssignment', 'b') }} ,
       {% endif %}
        a.countrycode,
	   a.enddate as apdate,
 	   a.frequency as recordlevel  from {{ ref('repaccountassignment_details') }} b 
        join {{ ref('account_dse_v') }} d on b.accountid = d.accountid 
        join {{ ref('fiscalperiod_country_normalized') }}  a
          on YEAR(b.dt) = YEAR(a.enddate) and (month(b.dt) = month(a.enddate) or quarter(b.dt) = quarter(a.enddate))
          and b.dt >= {{build_rolling_two_years_date(2)}} 
          {%- if var_multi_country == 'true' %}  
              and a.countrycode = d.configcountrycode
          {%- endif %}