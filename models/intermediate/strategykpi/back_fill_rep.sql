{{ config(materialized='table') }}

with rep_with_mindate as (
select externalid, min(startdate) min_start_date from {{ ref('akt_replicense_arc_v') }}
where CAST(updatedat AS DATE)  > {{build_rolling_two_years_date(2)}} group by externalid
), max_date_row as ( select {{ dbt_utils.star(from=ref('akt_replicense_arc_v'), 
    except=['startdate','updatedatyear','updatedatmonth','updatedatday', 'updatedat']) }},
    cast(EXTRACT(YEAR FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatyear,
    cast(EXTRACT(MONTH FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatmonth,
    cast(EXTRACT(DAY FROM {{build_rolling_two_years_date(2)}}) as varchar) AS updatedatday,
    {{build_rolling_two_years_date(2)}} AS startdate,
    {{build_rolling_two_years_date(2)}} AS updatedat,
    ROW_NUMBER() OVER(PARTITION BY externalid ORDER BY cast(updatedatyear as int) asc, 
        cast(updatedatmonth as int) asc, cast(updatedatday as int) asc, updatedat asc ) AS row_number
    from {{ ref('akt_replicense_arc_v') }}
), single_row_query as ( select * from max_date_row where row_number = 1),
back_fill_rows as ( select a.* from single_row_query a join rep_with_mindate b
on a.externalid = b.externalid
) select * from back_fill_rows 
UNION
select {{ dbt_utils.star(from=ref('akt_replicense_arc_v'), 
    except=['updatedat','startdate']) }}, startdate, updatedat, 1 as row_number from {{ ref('akt_replicense_arc_v') }}
