{{ config(materialized='table') }}

select accountid, productid, date_trunc('month',date_parse(cast(cast(as_of_year as int) * 10000 + cast(as_of_month as int) * 100 + 1 as varchar(255)), '%Y%m%d')) + interval '1' month - interval '1' day as eventdate, last_month_hcptier_std_akt as oldstate, month_hcptier_std_akt as newstate, case when (month_hcptier_std_akt < last_month_hcptier_std_akt) then -1 when (month_hcptier_std_akt > last_month_hcptier_std_akt) then 1 else 0 end as conversionvalue
from  {{ ref('accountproduct_tier_changes') }};

