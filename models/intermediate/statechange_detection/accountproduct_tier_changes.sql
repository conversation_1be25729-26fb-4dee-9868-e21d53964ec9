{{ config(materialized='view') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

 -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

    {{ build_statechange_changes_sql('accountproduct','hcptier_std_akt','hcptier_std_akt','a.accountid,a.productid') }}

{% endif %}

