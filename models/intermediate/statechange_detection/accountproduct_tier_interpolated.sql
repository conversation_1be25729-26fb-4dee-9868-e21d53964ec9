{{ config(materialized='view') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

 -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

    -- depends_on: {{ ref('as_of_month') }}
    {{ build_statechange_interpolated_sql('accountproduct_cdc_v','hcptier_std_akt','hcptier_std_akt','a.accountid,a.productid',"a.accountid = b.accountid and a.productid = b.productid") }}

{% endif %}
