
version: 2

metrics:
  - name: face_to_face_sum
    label: Sum of face to face interactions
    model: ref('msrscenario_activity_acctrep')
    description: "rep visit calculation"
    calculation_method: sum
    expression: visit_interaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country      

  - name: working_days_sum
    label: Sum of workingdays
    model: ref('repavailableperiod')
    description: "working days calculation"
    calculation_method: count
    expression: repid
    timestamp: dt
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country      
    filters: 
       - field: countrycode
         operator: '='
         value: country   
         
  - name: visit_interaction_metric
    label: Face to Face Calls per day per Rep
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('face_to_face_sum') }} AS DOUBLE) / CAST({{ metric('working_days_sum') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country 

  - name: remote_calls_sum
    label: Sum of remote call interactions
    model: ref('msrscenario_activity_acctrep')
    description: "rep remote call calculation"
    calculation_method: sum
    expression: remote_interaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country      

  - name: remote_interaction_metric
    label: Remote Calls per day per Rep
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('remote_calls_sum') }} AS DOUBLE) / CAST({{ metric('working_days_sum') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country

  - name: rte_sum
    label: Sum of rte interactions
    model: ref('msrscenario_activity_acctrep')
    description: "rep rte calculation"
    calculation_method: sum
    expression: email_interaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country      

  - name: rte_interaction_metric
    label: RTE per day per Rep
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('rte_sum') }} AS DOUBLE) / CAST({{ metric('working_days_sum') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country         
      
  - name: cycle_plan_attainment_targets
    label: Cycle Plan Attainment Targets
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment Targets"
    calculation_method: sum
    expression: visit_proratedtarget + email_proratedtarget + remote_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country 

  - name: cycle_plan_attainment_interactions
    label: Cycle Plan Attainment Interactions
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment Interactions"
    calculation_method: sum
    expression: cpa_visit_proratedaction_count + cpa_email_proratedaction_count + cpa_remote_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country   

  - name: cycle_plan_attainment_overall
    label: Cycle Plan Attainment Overall
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_interactions') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_targets') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country    

  - name: cycle_plan_attainment_targets_by_visit
    label: Cycle Plan Attainment targets by visit
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment targets by visit"
    calculation_method: sum
    expression: visit_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country 

  - name: cycle_plan_attainment_interactions_by_visit
    label: Cycle Plan Attainment interactions by visit
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment interactions by visit"
    calculation_method: sum
    expression: cpa_visit_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country

  - name: cycle_plan_attainment_visit
    label: Cycle Plan Attainment by visit channel
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_interactions_by_visit') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_targets_by_visit') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country

  - name: cycle_plan_attainment_targets_by_rte
    label: Cycle Plan Attainment targets by rte
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment targets by rte"
    calculation_method: sum
    expression: email_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country 

  - name: cycle_plan_attainment_interactions_by_rte
    label: Cycle Plan Attainment interactions by rte
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment interactions by rte"
    calculation_method: sum
    expression: cpa_email_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country

  - name: cycle_plan_attainment_rte
    label: Cycle Plan Attainment by rte channel
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_interactions_by_rte') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_targets_by_rte') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country

  - name: cycle_plan_attainment_targets_by_remote
    label: Cycle Plan Attainment targets by remote
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment targets by remote"
    calculation_method: sum
    expression: remote_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country 

  - name: cycle_plan_attainment_interactions_by_remote
    label: Cycle Plan Attainment interactions by remote
    model: ref('msrscenario_activity_acctrep')
    description: "Cycle Plan Attainment interactions by remote"
    calculation_method: sum
    expression: cpa_remote_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country

  - name: cycle_plan_attainment_remote
    label: Cycle Plan Attainment by remote channel
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_interactions_by_remote') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_targets_by_remote') }} AS DOUBLE) "
    dimensions:
      - repuid
      - country

  - name: cycle_plan_attainment_brand_targets
    label: Cycle Plan Attainment Targets by brand
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment Targets by brand"
    calculation_method: sum
    expression: visit_proratedtarget + email_proratedtarget + remote_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country       

  - name: cycle_plan_attainment_brand_interactions
    label: Cycle Plan Attainment Interactions by brand
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment Interactions by brand"
    calculation_method: sum
    expression: cpa_visit_proratedaction_count + cpa_email_proratedaction_count + cpa_remote_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country     

  - name: cycle_plan_attainment_brand
    label: Cycle Plan Attainment by brand
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_brand_interactions') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_brand_targets') }} AS DOUBLE) "
    dimensions:
      - repuid
      - productuid
      - country 

  - name: cycle_plan_attainment_brand_target_visit
    label: Cycle Plan Attainment by brand and visit
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment by brand and visit"
    calculation_method: sum
    expression: visit_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country       

  - name: cycle_plan_attainment_brand_interactions_visit
    label: Cycle Plan Attainment Visit Interactions by brand
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment Visit Interactions by brand"
    calculation_method: sum
    expression: cpa_visit_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country     

  - name: cycle_plan_attainment_visit_brand
    label: Cycle Plan Attainment visit by brand
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_brand_interactions_visit') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_brand_target_visit') }} AS DOUBLE) "
    dimensions:
      - repuid
      - productuid   
      - country 

  - name: cycle_plan_attainment_brand_target_rte
    label: Cycle Plan Attainment by brand and rte
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment by brand and rte"
    calculation_method: sum
    expression: email_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country       

  - name: cycle_plan_attainment_brand_interactions_rte
    label: Cycle Plan Attainment RTE Interactions by brand
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment RTE Interactions by brand"
    calculation_method: sum
    expression: cpa_email_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country     

  - name: cycle_plan_attainment_rte_brand
    label: Cycle Plan Attainment RTE by brand
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_brand_interactions_rte') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_brand_target_rte') }} AS DOUBLE) "
    dimensions:
      - repuid
      - productuid   
      - country 

  - name: cycle_plan_attainment_brand_target_remote
    label: Cycle Plan Attainment by brand and remote
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment by brand and remote"
    calculation_method: sum
    expression: remote_proratedtarget
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country       

  - name: cycle_plan_attainment_brand_interactions_remote
    label: Cycle Plan Attainment Remote Interactions by brand
    model: ref('msrscenario_activity_acctprdrep')
    description: "Cycle Plan Attainment Remote Interactions by brand"
    calculation_method: sum
    expression: cpa_remote_proratedaction_count
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    dimensions:
      - repuid 
      - productuid
      - country
    filters: 
       - field: countrycode
         operator: '='
         value: country     

  - name: cycle_plan_attainment_remote_brand
    label: Cycle Plan Attainment remote by brand
    timestamp: sale_date
    time_grains: [month, quarter, trimester, semester, year]
    calculation_method: derived
    expression: "CAST({{ metric('cycle_plan_attainment_brand_interactions_remote') }} AS DOUBLE) / CAST({{ metric('cycle_plan_attainment_brand_target_remote') }} AS DOUBLE) "
    dimensions:
      - repuid
      - productuid   
      - country                                                                                 
