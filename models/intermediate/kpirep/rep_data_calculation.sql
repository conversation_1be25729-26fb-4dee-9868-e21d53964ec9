-- depends_on: {{ ref('aktana_metrics_calendar') }}
-- depends_on: {{ ref('msrscenario_activity_acctrep') }}
-- depends_on: {{ ref('repavailableperiod') }}
-- depends_on: {{ ref('msrscenario_activity_acctprdrep') }}

{{ config(materialized='table') }}

{% set kpitypes_query %}
    select concat("kpitypelabel" ,':',cast ("kpitypeid" as varchar)) kpitype from {{ ref('kpitype_v')}} 
    where upper(modelType) = 'REP'
{% endset %}

{% set results = run_query(kpitypes_query) %}
{% if execute %}
    {% set kpitypes = results.columns[0].values() %}
{% else %}
    {% set kpitypes = [] %}
{% endif %}

{% set timegrain = ["month", "quarter", "trimester", "semester", "year"] %}
{% set startDate = build_rolling_two_years_date(1) %}

{% if kpitypes|length > 0 %}
    with
    {% for kpitypewithid in kpitypes %}
        {% set kpitype, kpitypeidval = kpitypewithid.split(':') %}

        {% set cust_dimensions=['repuid','country'] %}

        {% if kpitype == 'cycle_plan_attainment_brand' or kpitype == 'cycle_plan_attainment_visit_brand'or kpitype == 'cycle_plan_attainment_rte_brand'or kpitype == 'cycle_plan_attainment_remote_brand' %}  
            {% set cust_dimensions=['repuid','country','productuid'] %}       
        {% else %}
            {% set cust_dimensions=['repuid','country'] %}
        {% endif %}

        {% for c in timegrain %}
            {{kpitype}}_{{c}} as (
                select *
                from {{ metrics.calculate(
                metric(kpitype),
                dimensions=cust_dimensions,       
                grain = (c),
                start_date=startDate
                )
            }}
            )
            {% if not loop.last %},{% endif %}
        {% endfor %}
        {% if not loop.last %},{% endif %}
    {% endfor %}      

    select repuid, countrycode,
    recordtype,datevalue,productuid,kpivalue,kpitypeid from (
    {% for kpitypewithid in kpitypes %}  
        {% set kpitype, kpitypeidval = kpitypewithid.split(':') %}
        {% for c in timegrain %}
            select repuid, country as countrycode, upper('{{c}}') as recordtype, date_{{c}} as datevalue, 
            {% if kpitype == 'cycle_plan_attainment_brand' or kpitype == 'cycle_plan_attainment_visit_brand'or kpitype == 'cycle_plan_attainment_rte_brand'or kpitype == 'cycle_plan_attainment_remote_brand' %}    
                productuid,
            {% else %} 
                cast(null as varchar) as productuid,  
            {% endif %} 
            {{kpitype}} as kpivalue, 
            {{kpitypeidval}} as kpitypeid
            from {{kpitype}}_{{c}}
            {% if not loop.last %} UNION {% endif %}
        {% endfor %}
        {% if not loop.last %} UNION {% endif %}
    {% endfor %}
    ) tt
{% else %}
select
    cast(null as varchar) as repuid
  , cast(null as varchar) as countrycode

  , cast(null as varchar) as recordtype
  , cast(null as date) as datevalue
  , cast(null as varchar) as productuid
  , cast(null as decimal) as kpivalue
  , cast(null as integer) as kpitypeid

{% endif %}  