{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with rep_calculation_details as (
	select a.repuid,
		a.recordtype,
		a.datevalue,
		c.fiscalperiodid,
		c.countrycode,
		c.periodname,
		b.repteamname,
		b.repteamuid,
		b.territoryname,
		b.districtname,
		b.regionname,
		a.productuid,
		a.kpivalue,
		a.kpitypeid
	from {{ ref('rep_data_calculation') }} a
		inner join {{ ref('rep_with_time_grain_v') }} b on a.repuid = b.externalid
		and a.recordtype = b.recordlevel
		and a.datevalue = b.repdate
		left join {{ ref('fiscalperiod_country_normalized') }} c on a.datevalue = c.enddate
		and a.recordtype = c.frequency
		{%- if var_multi_country == 'true' %}  
			and a.countrycode = c.countrycode
		{%- endif %}	
)
select *
from rep_calculation_details