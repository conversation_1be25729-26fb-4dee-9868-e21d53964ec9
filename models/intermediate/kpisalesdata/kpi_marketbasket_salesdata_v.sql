{{ config(materialized='view') }}

-- depends_on: {{ ref('sales_market_basket_v') }}

{% set query %}
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema like '{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}'
     AND table_name = 'sales_market_basket' 
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}
     {% if results.columns[0][0] %} 
        {% set tabname = results.columns[0][0]  %}
        select
          data_point_unit as datapointunit,
          attr_1 as attribute1,
          attr_2 as attribute2,
          attr_3 as attribute3,        
          market_identifier,
          market_long_name,
          market_short_name,
          product_identifier,
          weighting_factor as weighing_factor,
          as_of_date,
          country_code as countrycode
        
        
        from {{ ref('sales_market_basket_v') }}; 
      {% else %}

    SELECT
        CAST(NULL as VARCHAR) as datapointunit,
        CAST(NULL as VARCHAR) as attribute1,
        CAST(NULL as VARCHAR) as attribute2,
        CAST(NULL as VARCHAR) as attribute3,          
        CAST(NULL as VARCHAR) as market_identifier,
        CAST(NULL as VARCHAR) as market_long_name,
        CAST(NULL as VARCHAR) as market_short_name,
        CAST(NULL as VARCHAR) as product_identifier,
        CAST(NULL as DOUBLE)  as weighing_factor,
        CAST(NULL as VARCHAR) as as_of_date,
        CAST(NULL as VARCHAR) as countrycode

     {% endif %}
{% endif %}
