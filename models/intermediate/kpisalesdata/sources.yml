version: 2

sources:
  - name: kpisalesdata
    description: “Manually Maintained aggregated sales data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: aggregated_sales_data 
          description: "This table holds aggregated sales data"
          external:
              location: "s3://aktana-bdp-{{ var('customer', 'default') }}/{{ var('src_env', 'prod') }}/salesdata/aggregated"
              row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde' "
              table_properties: ('skip.header.line.count'='1')
          columns:
              - name: accountuid 
                data_type: varchar(50)
              - name: productuid 
                data_type: varchar(50)
              - name: productName 
                data_type: varchar(100)
              - name: salesdate 
                data_type: Date
              - name: datapointunit
                data_type: varchar(100)
              - name: datapointvalue
                data_type: Double 
              - name: market_short_name
                data_type: varchar(100)   
              - name: countrycode
                data_type: varchar(10)                  
              - name: basketvolume
                data_type: Double  

        - name: accountlevel_sales_data 
          description: "This table holds account level sales data"
          external:
              location: "s3://aktana-bdp-{{ var('customer', 'default') }}/{{ var('src_env', 'prod') }}/salesdata/accountlevel"
              row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
              table_properties: ('skip.header.line.count'='1')
          columns:
              - name: account_identifier 
                data_type: varchar(255)
              - name: product_identifier
                data_type: varchar(100)
              - name: datapointunit
                data_type: varchar(100)                  
              - name: attribute1
                data_type: varchar(10)  
              - name: attribute2
                data_type: varchar(10)                                     
              - name: attribute3
                data_type: varchar(10)                             
              - name: salesdate 
                data_type: varchar(10)
              - name: periodtype
                data_type: varchar(100)                
              - name: datapointvalue
                data_type: Double            
              - name: countrycode
                data_type: varchar(10)    


        - name: marketbasket_sales_data 
          description: "This table holds account level sales data"
          external:
              location: "s3://aktana-bdp-{{ var('customer', 'default') }}/{{ var('src_env', 'prod') }}/salesdata/marketbasket"
              row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
              table_properties: ('skip.header.line.count'='1')
          columns:
              - name: datapointunit
                data_type: varchar(100)                  
              - name: attribute1
                data_type: varchar(10)  
              - name: attribute2
                data_type: varchar(10)                                     
              - name: attribute3
                data_type: varchar(10)                  
              - name: market_identifier 
                data_type: varchar(25)
              - name: market_long_name
                data_type: varchar(100)
              - name: market_short_name
                data_type: varchar(50)                
              - name: product_identifier
                data_type: varchar(100)               
              - name: weighing_factor
                data_type: varchar(100)
              - name: as_of_date
                data_type: varchar(10)
              - name: countrycode
                data_type: varchar(10) 