{{ config(materialized='view') }}

-- depends_on: {{ ref('kpi_accountlevel_salesdata_v') }}
-- depends_on: {{ ref('accountmapping_v') }}
-- depends_on: {{ ref('productmapping_v') }}
-- depends_on: {{ ref('product_v') }}
-- depends_on: {{ ref('kpi_marketbasket_salesdata_v') }}

{% set query %}
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema like '{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current'
     AND table_name = 'aggregated_sales_data' 
 {% endset %}

{% set countQuery %}
   select count(*) from {{ source('kpisalesdata', 'aggregated_sales_data') }}
 {% endset %}

 {% set results = run_query(query) %}
 {% set useNewQuery = false %}

 {% if execute %}
     {% if results.columns[0][0]%}        
        {% set countResults = run_query(countQuery) %}
        {% if execute %}
          {% if countResults.columns[0][0] > 0%}        
              select * from {{ source('kpisalesdata', 'aggregated_sales_data') }}
          {% else %}
            {% set useNewQuery = true %}
          {% endif %}
        {% endif %}
     {% else %}
        {% set useNewQuery = true %}
     {% endif %}

     {% if useNewQuery == true %}
          with accountlevel as (
          select  account_identifier,
            product_identifier,        
            TRIM(BOTH '-' FROM CONCAT_WS('-', 
                datapointunit, 
                NULLIF(attribute1, ''), 
                NULLIF(attribute2, ''),
                NULLIF(attribute3, '')
                ))as datapointunit, 
            CAST(salesdate AS DATE) as salesdate,        
            periodtype,        
            datapointvalue,
            countrycode
          from {{ ref('kpi_accountlevel_salesdata_v') }}
          ), marketlevel as (
            select  TRIM(BOTH '-' FROM CONCAT_WS('-', 
                datapointunit, 
                NULLIF(attribute1, ''), 
                NULLIF(attribute2, ''),
                NULLIF(attribute3, '')
                ))as datapointunit,          
            market_identifier,
            market_long_name,
            market_short_name,
            product_identifier,
            weighing_factor,
            CAST(as_of_date AS DATE) as as_of_date,
            countrycode
          from {{ ref('kpi_marketbasket_salesdata_v') }}
          )
            select a.account_identifier as accountuid,
              c.productuid as productuid, 
              d.productname as productname,
              a.salesdate,  
              a.datapointunit,
              a.datapointvalue,
              e.market_short_name,
              a.countrycode,
              sum(a.datapointvalue) over(partition by a.account_identifier, a.datapointunit, e.market_short_name, a.salesdate) as basketvolume
          from accountlevel a
          -- JOIN {{ ref('accountmapping_v') }} b on a.account_identifier = b.customerId
          JOIN {{ ref('productmapping_v') }} c on a.product_identifier = c.customerId
          JOIN {{ ref('product_v') }} d on c.productUID = d.externalid
          JOIN marketlevel e on a.product_identifier = e.product_identifier and a.datapointunit = e.datapointunit
     {% endif %}      
{% endif %}
