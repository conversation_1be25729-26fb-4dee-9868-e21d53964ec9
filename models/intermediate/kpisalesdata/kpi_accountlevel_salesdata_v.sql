{{ config(materialized='view') }}

-- depends_on: {{ ref('sales_account_level_v') }}

{% set query %}
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema like '{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}'
     AND table_name = 'sales_account_level' 
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}
     {% if results.columns[0][0] %} 
        {% set tabname = results.columns[0][0]  %}
        select account_identifier,
        product_identifier,        
        data_point_unit as datapointunit,
        attr_1 as attribute1,
        attr_2 as attribute2,
        attr_3 as attribute3,        
        CAST(sales_date as DATE) as salesdate,        
        period_type as periodtype,        
        data_point_value as datapointvalue,
        country_code as countrycode from {{ ref('sales_account_level_v') }}; 
      {% else %}

    SELECT
        CAST(NULL as VARCHAR) as account_identifier,
        CAST(NULL as VARCHAR) as product_identifier,        
        CAST(NULL as VARCHAR) as datapointunit,
        CAST(NULL as VARCHAR) as attribute1,
        CAST(NULL as VARCHAR) as attribute2,
        CAST(NULL as VARCHAR) as attribute3,        
        CAST(NULL as DATE) as salesdate,        
        CAST(NULL as VARCHAR) as periodtype,        
        CAST(NULL as DOUBLE)  as datapointvalue,
        CAST(NULL as VARCHAR) as countrycode

     {% endif %}
{% endif %}