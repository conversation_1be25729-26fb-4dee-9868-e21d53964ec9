{{ config(materialized='view') }}


with visit_acct_prod as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid, productid',
                        channel_name='VISIT_CHANNEL',
                        column_name='visit') }}
),

visit_acct as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid',
                        channel_name='VISIT_CHANNEL',
                        column_name='visit') }}
)

select
accountid, productid, yearMonth,
visit1MonthCount, visit1MonthDayCount, visit1MonthWeekCount
from visit_acct_prod

union all

select
accountid, -1 as productid, yearMonth,
visit1MonthCount, visit1MonthDayCount, visit1MonthWeekCount
from visit_acct