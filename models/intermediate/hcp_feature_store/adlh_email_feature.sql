{{ config(materialized='view') }}


with email_acct_prod as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid, productid',
                        channel_name='SEND_CHANNEL',
                        column_name='emailSent') }}
),

email_acct as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid',
                        channel_name='SEND_CHANNEL',
                        column_name='emailSent') }}
)

select
accountid, productid, yearMonth,
emailSent1MonthCount, emailSent1MonthDayCount, emailSent1MonthWeekCount
from email_acct_prod

union all

select
accountid, -1 as productid, yearMonth,
emailSent1MonthCount, emailSent1MonthDayCount, emailSent1MonthWeekCount
from email_acct