{{ config(materialized='table') }}


select a.accountid, a.productid, a.yearMonth,
coalesce(e.emailSent1MonthCount, 0) emailSent1MonthCount, coalesce(e.emailSent1MonthDayCount, 0) emailSent1MonthDayCount, coalesce(e.emailSent1MonthWeekCount, 0) emailSent1MonthWeekCount,
coalesce(v.visit1MonthCount, 0) visit1MonthCount, coalesce(v.visit1MonthDayCount, 0) visit1MonthDayCount, coalesce(v.visit1MonthWeekCount, 0) visit1MonthWeekCount,
coalesce(w.virtualVisit1MonthCount, 0) virtualVisit1MonthCount, coalesce(w.virtualVisit1MonthDayCount, 0) virtualVisit1MonthDayCount, coalesce(w.virtualVisit1MonthWeekCount, 0) virtualVisit1MonthWeekCount,
coalesce(eoc.emailOpen1MonthCount, 0) emailOpen1MonthCount, coalesce(eoc.emailOpen1MonthDayCount, 0) emailOpen1MonthDayCount, coalesce(eoc.emailOpen1MonthWeekCount, 0) emailOpen1MonthWeekCount,
coalesce(eoc.emailClick1MonthCount, 0) emailClick1MonthCount, coalesce(eoc.emailClick1MonthDayCount, 0) emailClick1MonthDayCount, coalesce(eoc.emailClick1MonthWeekCount, 0) emailClick1MonthWeekCount

from {{ ref('adlh_all_acct_prod_month') }} a

left join {{ ref('adlh_email_feature') }} e
on a.accountid = e.accountid
    and a.productid = e.productid
    and a.yearMonth = e.yearMonth

left join {{ ref('adlh_visit_feature') }} v
on a.accountid = v.accountid
    and a.productid = v.productid
    and a.yearMonth = v.yearMonth

left join {{ ref('adlh_web_feature') }} w
on a.accountid = w.accountid
    and a.productid = w.productid
    and a.yearMonth = w.yearMonth

left join {{ ref('adlh_email_open_click_feature') }} eoc
on a.accountid = eoc.accountid
    and a.productid = eoc.productid
    and a.yearMonth = eoc.yearMonth
