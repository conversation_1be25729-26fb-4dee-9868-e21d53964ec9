{{ config(materialized='view') }}

with interaction_with_dates as (
    select i.*,
    e.open_count_vod__c emailOpenCount, e.click_count_vod__c emailClickCount,
    date_format(activityDate, '%Y-%m') as yearMonth,
    week(activityDate) as interactionWeek
    from {{ ref('interaction_account_product_history_v') }} i
    join {{ ref('sent_email_vod__c_v') }} e
        on i.interactionuid = e.id
)


    SELECT 
        accountid, productid,
        yearMonth,
        count(distinct case when emailOpenCount > 0 then interactionid end) as emailOpen1MonthCount,
        count(distinct case when emailOpenCount > 0 then activityDate end) as emailOpen1MonthDayCount,
        count(distinct case when emailOpenCount > 0 then interactionWeek end) as emailOpen1MonthWeekCount,
        count(distinct case when emailClickCount > 0 then interactionid end) as emailClick1MonthCount,
        count(distinct case when emailClickCount > 0 then activityDate end) as emailClick1MonthDayCount,
        count(distinct case when emailClickCount > 0 then interactionWeek end) as emailClick1MonthWeekCount
    FROM interaction_with_dates
    WHERE activityDate >= date_add('month', -12, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }}) )
    GROUP BY 
    accountid, productid, yearMonth

    union all

    SELECT 
        accountid, -1 as productid,
        yearMonth,
        count(distinct case when emailOpenCount > 0 then interactionid end) as emailOpen1MonthCount,
        count(distinct case when emailOpenCount > 0 then activityDate end) as emailOpen1MonthDayCount,
        count(distinct case when emailOpenCount > 0 then interactionWeek end) as emailOpen1MonthWeekCount,
        count(distinct case when emailClickCount > 0 then interactionid end) as emailClick1MonthCount,
        count(distinct case when emailClickCount > 0 then activityDate end) as emailClick1MonthDayCount,
        count(distinct case when emailClickCount > 0 then interactionWeek end) as emailClick1MonthWeekCount
    FROM interaction_with_dates
    WHERE activityDate >= date_add('month', -12, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }}) )
    GROUP BY 
    accountid, yearMonth


