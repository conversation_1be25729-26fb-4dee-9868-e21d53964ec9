{{ config(materialized='view') }}

with all_months as (
select date_format(x, '%Y-%m') yearMonth from
(
    select
        sequence(date_add('month', -12, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }})), 
        date_add('month', -1, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }})), 
        interval '1' month) months
) a
cross join unnest(months) t(x)
)


select accountid, productid, yearMonth
from
(
    select distinct accountid, productid
    from {{ ref('interaction_account_product_history_v') }}
    where activityDate >= date_add('month', -12, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }}))

    union all 

    select distinct accountid, -1 as productid
    from {{ ref('interaction_account_product_history_v') }}
    where activityDate >= date_add('month', -12, ({{ get_max_date('interaction_account_product_history_v', 'activityDate') }}))

) apy

cross join
(
    select year<PERSON>onth from all_months
)