{{ config(materialized='view') }}


with web_acct_prod as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid, productid',
                        channel_name='WEB_INTERACTIVE_CHANNEL',
                        column_name='virtualVisit') }}
),

web_acct as (
{{ build_adlh_channel_count(table_name='interaction_account_product_history_v',
                        group_fields='accountid',
                        channel_name='WEB_INTERACTIVE_CHANNEL',
                        column_name='virtualVisit') }}
)

select
accountid, productid, yearMonth,
virtualVisit1MonthCount, virtualVisit1MonthDayCount, virtualVisit1MonthWeekCount
from web_acct_prod

union all

select
accountid, -1 as productid, yearMonth,
virtualVisit1MonthCount, virtualVisit1MonthDayCount, virtualVisit1MonthWeekCount
from web_acct