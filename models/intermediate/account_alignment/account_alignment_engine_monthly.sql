{{ config(materialized='table') }}

with engine_account_alignment as (
     select a.startdatelocal, b.accountid, b.accountuid,  array_agg(distinct cast(row(c.repid, c.repuid, d.repname)  AS ROW(repid INTEGER, repuid VARCHAR, repname VARCHAR))) as aligned_reps
     from {{ ref('sparkdserun_v') }} a
     inner join {{ ref('sparkdserunaccount_v') }} b on a.runuid = b.runuid
     inner join {{ ref('sparkdserunaccountrep_v') }} c on c.runAccountId = b.runAccountId
     left join {{ ref('actor_v') }} d on c.repuid = d.externalid
     group by a.startdatelocal, b.accountid, b.accountuid
),
engine_account_month_alignment as (
     select ROW_NUMBER() OVER(PARTITION BY accountid, accountuid, date_trunc('month', startdatelocal)
                         ORDER BY startdatelocal desc) AS row_number,
        * from engine_account_alignment
)
select date_trunc('month', startdatelocal) as startdatelocal, accountid, accountuid, i.repid, i.repuid,  cardinality(aligned_reps) alignmentcount
from engine_account_month_alignment 
     cross join UNNEST(aligned_reps) as t(i)
where row_number = 1;

