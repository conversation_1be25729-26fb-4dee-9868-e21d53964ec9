{{ config(materialized='table') }}

with accountrep  as 
(
  select rac.accountid, a.externalid as accountuid, array_agg(cast(row(rac.repid, r.externalid, r.repname, cast(r.isdeleted as boolean), rac.ismytarget_rpt)  AS ROW(repid INTEGER, repuid VARCHAR, repname VARCHAR, isdeleted INTEGER, ismytarget_rpt  INTEGER))) as aligned_reps
  from {{ ref('repaccountassignment_v') }} rac 
        inner join {{ ref('actor_v') }} r  on rac.repid = r.repid
        inner join {{ ref('account_dse_v') }} a on rac.accountid = a.accountid 
  group by rac.accountid, a.externalid
)
  select accountid, accountuid, aligned_reps, cardinality(aligned_reps) aligned_reps_count from accountrep
