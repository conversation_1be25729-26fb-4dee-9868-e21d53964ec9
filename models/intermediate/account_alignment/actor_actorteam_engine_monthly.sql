{{ config(materialized='table') }}

with rep_config_repteam as (
  select sra.repuid, r.startdatelocal, coalesce(c.repteamid, 0) as repteamid,  
         ROW_NUMBER() OVER(PARTITION BY sra.repuid, r.startdatelocal, c.repteamid ORDER BY coalesce(c.repteamid, 0) desc, r.startdatelocal desc) AS row_nos  
  from {{ ref('sparkdserun_v') }} r inner join {{ ref('sparkdserunaccount_v') }} sa on r.runuid = sa.runuid
        inner join  {{ ref('sparkdserunaccountrep_v') }} sra on sa.runaccountid = sra.runaccountid
        left join {{ ref('dseconfig_v') }} c on r.seconfigid = c.seconfigid
), 
rep_repteam_count as (
    select repuid, repteamid, date_trunc('month', startdatelocal) startdatelocal, count(*) team_occurance_count,
    (dense_rank() over (partition by repuid, date_trunc('month', startdatelocal) order by date_trunc('month', startdatelocal) asc) +
     dense_rank() over (partition by repuid, date_trunc('month', startdatelocal) order by date_trunc('month', startdatelocal) desc) -
     1
    ) AS team_count   
    from rep_config_repteam
    where row_nos = 1 and repteamid <> 0
    group by repuid, repteamid, date_trunc('month', startdatelocal)
),
rep_repteam_count_ordered as (
    select repuid, repteamid, startdatelocal, team_occurance_count, team_count,
         ROW_NUMBER() OVER(PARTITION BY repuid, repteamid, startdatelocal ORDER BY team_occurance_count desc) AS row_nos  
    from rep_repteam_count
)
select repuid, startdatelocal, min(repteamid) as repteamid, min(team_count) team_count
from rep_repteam_count_ordered
where row_nos = 1
group by repuid, startdatelocal

