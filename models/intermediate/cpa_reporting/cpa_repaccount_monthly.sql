{{ config(materialized='table') }}

with target_repaccount as (
select targetsperiodid, accountid, accountuid, repid, repuid, periodvalue, repactiontypename, sum(coalesce(metricvalue, 0)) as metricvalue, sum(coalesce(proratedmetricvalue, 0)) as proratedmetricvalue
from 
{{ ref('rpt_stage_customer_impact_agg_target_v') }}
where productid = -9999 and aggregationtype = 'Monthly'
group by targetsperiodid, accountid, accountuid, repid, repuid, periodvalue, repactiontypename
), call_repaccount as (
select targetsperiodid, accountid, accountuid, repid, repuid, periodvalue, repactiontypename, sum(coalesce(metricvalue, 0)) as metricvalue, sum(coalesce(proratedmetricvalue, 0)) as proratedmetricvalue
from 
{{ ref('rpt_stage_customer_impact_agg_call_v') }}
where productid = -9999 and aggregationtype = 'Monthly'
group by targetsperiodid, accountid, accountuid, repid, repuid, periodvalue, repactiontypename
)
, cpa_values as (
    select a.targetsperiodid, a.accountid, a.accountuid, a.repid, a.repuid, a.periodvalue,
    sum(case when a.repactiontypename = 'VISIT' then   coalesce(a.metricvalue, 0) else 0 end) as visit_target,
    sum(case when a.repactiontypename = 'SEND' then   coalesce(a.metricvalue, 0) else 0 end) as email_target,
    sum(case when a.repactiontypename = 'WEB_INTERACTION' then coalesce(a.metricvalue, 0) else 0 end) as remote_target,
    sum(case when a.repactiontypename = 'VISIT' then   coalesce(a.proratedmetricvalue, 0.0) else 0.0 end) as visit_proratedtarget,
    sum(case when a.repactiontypename = 'SEND' then   coalesce(a.proratedmetricvalue, 0.0) else 0.0 end) as email_proratedtarget,
    sum(case when a.repactiontypename = 'WEB_INTERACTION' then coalesce(a.proratedmetricvalue, 0.0) else 0.0 end) as remote_proratedtarget,
    sum(case when a.repactiontypename = 'VISIT' then   coalesce(b.metricvalue, 0) else 0 end) as cpa_visit_action,
    sum(case when a.repactiontypename = 'SEND' then   coalesce(b.metricvalue, 0) else 0 end) as cpa_email_action,
    sum(case when a.repactiontypename = 'WEB_INTERACTION' then coalesce(b.metricvalue, 0) else 0 end) as cpa_remote_action,
    sum(case when a.repactiontypename = 'VISIT' then   coalesce(b.proratedmetricvalue, 0.0) else 0.0 end) as cpa_visit_proratedaction,
    sum(case when a.repactiontypename = 'SEND' then   coalesce(b.proratedmetricvalue, 0.0) else 0.0 end) as cpa_email_proratedaction,
    sum(case when a.repactiontypename = 'WEB_INTERACTION' then coalesce(b.proratedmetricvalue, 0.0) else 0.0 end) as cpa_remote_proratedaction
    FROM
    target_repaccount a left join 
    call_repaccount b on a.repuid = b.repuid and a.accountuid = b.accountuid and a.periodvalue = b.periodvalue
    and a.repactiontypename = b.repactiontypename
    group by a.targetsperiodid, a.accountid, a.accountuid, a.repid, a.repuid, a.periodvalue
)
select a.targetsperiodid, a.accountid, a.accountuid, a.repid, a.repuid, a.periodvalue,
    a.visit_target, a.email_target, a.remote_target,
    a.visit_proratedtarget, a.email_proratedtarget, a.remote_proratedtarget,
    a.cpa_visit_action, a.cpa_email_action, a.cpa_remote_action,
    a.cpa_visit_proratedaction, a.cpa_email_proratedaction, a.cpa_remote_proratedaction,
    b.startdate as callplanstartdate,
    b.enddate as callplanenddate
from cpa_values a left join {{ ref('targetsperiod_v') }} b on a.targetsperiodid = b.targetsperiodid 