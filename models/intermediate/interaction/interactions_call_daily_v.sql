-- depends_on: {{ ref('call2_vod__c_v') }}
-- depends_on: {{ ref('recordtype_v') }}
-- depends_on: {{ ref('actor_v') }}
-- depends_on: {{ ref('interaction_call_type_v') }}
-- depends_on: {{ ref('call2_detail_vod__c_v') }}
-- depends_on: {{ ref('interaction_v') }}
-- depends_on: {{ ref('interactionaccount_v') }}
-- depends_on: {{ ref('account_dse_v') }}
-- depends_on: {{ ref('interactiontype_v') }}
-- depends_on: {{ ref('product_v') }}
-- depends_on: {{ ref('interactionproduct_v') }}

{{ config(materialized='view') }}

with call_interaction_count as (

{% if build_check_is_veeva_customer() %}
    select 
    cvc.id as interactionuid,
    case when rt.name is not null then rt.name else 'VISIT' end  as channel,
    cvct.call_channel_category as call_channel_category,  
    cvc.ownerid as repuid,
    max(UPPER(arl.repname)) as repname,
    cvc.call_date_vod__c as interaction_date,
    'NA' as territory_vod__c,
    cvc.account_vod__c accountuid ,
    COUNT(cvc.id) as interaction_count
    from {{ ref('call2_vod__c_v') }} cvc     
    left join {{ ref('recordtype_v') }} rt on rt.id = cvc.recordtypeid 
    join {{ ref('actor_v') }} arl on cvc.ownerid = arl.externalid  
    join {{ ref('interaction_call_type_v') }} cvct on cvct.interactionuid = cvc.id
    where  cvc.status_vod__c = 'Submitted_vod' and cast(cvc.isdeleted as boolean) = false
    group by cvc.id, rt.name, cvct.call_channel_category, cvc.ownerid, cvc.call_date_vod__c, account_vod__c
{% else %}
    select 
    i.externalid as interactionuid,
    'VISIT'  as channel,
    case when it.interactiontypename = 'WEB_INTERACTION' then 'Remote' else 'Visit' end  as call_channel_category,  
    a.externalid as repuid,
    max(UPPER(a.repname)) as repname,
    i.startdatetime as interaction_date,
    cast('NA' as varchar) as territory_vod__c,
    ad.externalid as accountuid,
    count(i.interactionid) as interaction_count
    from 
    {{ ref('interaction_v') }} i 
    inner join {{ ref('actor_v') }}  a on i.repid = a.repid 
    inner join {{ ref('interactionaccount_v') }}  ia on i.interactionid = ia.interactionid
    inner join {{ ref('account_dse_v') }}   ad on ia.accountid = ad.accountid 
    inner join {{ref('interactiontype_v') }} it on i.interactiontypeid = it.interactiontypeid and it.interactiontypename in  ('VISIT', 'WEB_INTERACTION')
    where cast(i.iscompleted as boolean) = true and cast(i.isdeleted as boolean) = false and cast(ia.isdeleted as boolean) = false
    group by i.externalid, case when it.interactiontypename = 'WEB_INTERACTION' then 'Remote' else 'Visit' end , a.externalid, i.startdatetime, ad.externalid
{% endif %}
),
call_detail_interaction_count as (

{% if build_check_is_veeva_customer() == true %} 
    select 
    cvc.id as interactionuid,
    case when rt.name is not null then rt.name else 'VISIT' end as channel,
    cvc.ownerid as repuid,
    max(UPPER(arl.repname)) as repname,
    cvc.call_date_vod__c as interaction_date,
    'NA' as territory_vod__c,
    cvc.account_vod__c accountuid ,
    case when p.externalid is null then 'OTHER' else cvcp.product_vod__c end as productuid,
    case when p.externalid is null then 'OTHER' else p.productname end as productname,
    COUNT(cvcp.id) as product_interaction_count
    from {{ ref('call2_vod__c_v') }} cvc 
    left join {{ ref('recordtype_v') }} rt on rt.id = cvc.recordtypeid 
    join {{ ref('actor_v') }} arl on cvc.ownerid = arl.externalid  
    join {{ ref('call2_detail_vod__c_v') }} cvcp on cvcp.call2_vod__c = cvc.id
    left join {{ ref('product_v') }} p on cvcp.product_vod__c = p.externalid and cast(p.iscompetitor as boolean) = false
    where  cvc.status_vod__c = 'Submitted_vod' and cast(cvc.isdeleted as boolean) = false 
            and cast(cvcp.isdeleted as boolean) = false
    group by cvc.id, rt.name, cvc.ownerid,  cvc.call_date_vod__c,  account_vod__c, case when p.externalid is null then 'OTHER' else cvcp.product_vod__c end, case when p.externalid is null then 'OTHER' else p.productname end
{% else %}
    select 
    i.externalid as interactionuid,
    'VISIT'  as channel,
    a.externalid as repuid,
    max(UPPER(a.repname)) as repname,
    i.startdatetime as interaction_date,
    cast('NA' as varchar) as territory_vod__c,
    ad.externalid as accountuid,
    case when p.externalid is null then 'OTHER' else p.externalid end as productuid,
    case when p.externalid is null then 'OTHER' else p.productname end as productname,
    count(concat(i.externalid, p.externalid)) as product_interaction_count
    from 
    {{ ref('interaction_v') }} i 
    inner join {{ ref('actor_v') }}  a on i.repid = a.repid 
    inner join {{ ref('interactionaccount_v') }}  ia on i.interactionid = ia.interactionid
    inner join {{ ref('account_dse_v') }}   ad on ia.accountid = ad.accountid 
    inner join {{ ref('interactionproduct_v') }}   ip on i.interactionid = ip.interactionid 
    inner join {{ ref('product_v') }}  p on ip.productid = p.productid and p.iscompetitor = false
    inner join {{ref('interactiontype_v') }} it on i.interactiontypeid = it.interactiontypeid and it.interactiontypename in  ('VISIT', 'WEB_INTERACTION')
    where cast(i.iscompleted as boolean) = true and cast(i.isdeleted as boolean) = false and cast(ip.isdeleted as boolean) = false and cast(ia.isdeleted as boolean) = false
    group by i.externalid, a.externalid, i.startdatetime, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, case when p.externalid is null then 'OTHER' else p.productname end
{% endif %}

)
select a.interactionuid, a.channel, a.call_channel_category, a.repuid, a.repname, a.interaction_date, a.territory_vod__c, a.accountuid, case when b.productuid is null then 'OTHER' else b.productuid end as productuid, case when b.productname is null then 'OTHER' else b.productname end as productname, a.interaction_count, b.product_interaction_count
from 
call_interaction_count a left join 
call_detail_interaction_count b on 
a.interactionuid = b.interactionuid and a.channel = b.channel and a.repuid = b.repuid and a.interaction_date = b.interaction_date and
                                a.territory_vod__c = b.territory_vod__c and a.accountuid = b.accountuid;

