{{ config(materialized='view') }}

select
    concat(t.corellationid, t.accountuid) externalid,  
    t.interactionuid,         
    coalesce(ip.actionorder, 1) as actionorder,
    rap.repactiontypeName as repactiontypename,
    ip.productid,
    p.externalid as productuid,
    p.productname,
    ip.messageid,
    m.externalid as messageuid,
    m.messagename,
    mt.messagetopic,
    mt.document_type,
    k.reaction_vod__c as messagereaction,
    k.Duration_vod__c as duration
from {{ ref('interaction_event_base_info_v') }} t
inner join {{ ref('interactionproduct_v') }} ip
    on t.interactionid = ip.interactionid and cast(ip.iscompleted as boolean) = true
inner join {{ ref('productinteractiontype_v') }} pt
    on pt.productinteractiontypeid = ip.productinteractiontypeid
inner join {{ ref('actiontype_v') }} rap
    on pt.repactiontypeid = rap.repactiontypeid
inner join {{ ref('product_v') }} p
    on ip.productid = p.productid
left join {{ ref('call2_key_message_vod__c_v') }} k
    on t.corellationid = k.call2_vod__c and p.externalid = k.product_vod__c
inner join {{ ref('akt_stablemessage_physicalmessage_v') }} m0
    on m0.physicalmessageuid = k.key_message_vod__c and p.externalid = k.product_vod__c
inner join {{ ref('message_v') }} m
    on m.externalid = m0.messageuid
left join {{ ref('akt_messagetomessagetopicmap_v') }} mt
    on m.externalid = mt.externalid
inner join {{ ref('kpi_eventtype') }} et
    on concat('KPI',rap.repactiontypename,'KEYMSG-COMPLETED') = et.eventtypename