{{ config(materialized='view') }}

select
    ia.accountid,
    i.repid,
    a.accountname,
    at.repname,
    a.externalid as accountuid,
    at.externalid as repuid,
    it.repActionTypeId,
    i.externalid as corellationid,
    i.externalid as interactionuid,
    i.duration as duration,
    i.startdatelocal as eventdate,
    i.startdatetime as eventdatetimeutc,
    i.startdatetime as actiondatetime,
    concat(ra.repactiontypeName, ' Completed') as eventlabel,
    concat(i.externalid, a.externalid, 'COMPLETE') externalid,
    i.interactionid,
    ra.repactiontypeName
from {{ ref('interaction_v') }} i
inner join {{ ref('interactionaccount_v') }} ia
    on i.interactionid = ia.interactionid and cast(i.iscompleted as boolean) = true and cast(i.isdeleted as boolean) = false
inner join {{ ref('interactiontype_v') }} it
    on i.interactiontypeid = it.interactiontypeid
inner join {{ ref('actiontype_v') }} ra
    on it.repactiontypeid = ra.repactiontypeid
inner join {{ ref('account_dse_v') }} a
    on ia.accountid = a.accountid
inner join {{ ref('actor_v') }} at
    on i.repid = at.repid