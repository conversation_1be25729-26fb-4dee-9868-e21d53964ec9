{{ config(materialized='view') }}

select 
    concat(t.corellationid, t.accountuid) externalid, 
    t.interactionuid,     
    coalesce(ip.actionorder, 1) as actionorder,
    rap.repactiontypeName as repactiontypename,
    ip.productid,
    p.externalid as productuid,
    p.productname,
    ip.messageid,
    m.externalid as messageuid,
    m.messagename,
    mt.messagetopic,
    ip.messagereaction as messagereaction,
    mt.document_type,
    cast(null as integer) as duration
from {{ ref('interaction_event_base_info_v') }} t
inner join {{ ref('interactionproduct_v') }} ip
    on t.interactionid = ip.interactionid and cast(ip.iscompleted as boolean) = true
inner join {{ ref('productinteractiontype_v') }} pt
    on pt.productinteractiontypeid = ip.productinteractiontypeid
inner join {{ ref('actiontype_v') }} rap
    on pt.repactiontypeid = rap.repactiontypeid
inner join {{ ref('product_v') }} p
    on ip.productid = p.productid
left join {{ ref('message_v') }} m
    on ip.messageid = m.messageid
left join {{ ref('akt_messagetomessagetopicmap_v') }} mt
    on m.externalid = mt.externalid
inner join {{ ref('kpi_eventtype') }} et
    on concat('KPI',rap.repactiontypename,'-COMPLETED') = et.eventtypename
