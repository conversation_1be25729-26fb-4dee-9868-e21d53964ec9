-- depends_on: {{ ref('sent_email_vod__c_v') }}
-- depends_on: {{ ref('actor_v') }}
-- depends_on: {{ ref('approved_document_vod__c_v') }}
-- depends_on: {{ ref('event_v') }}
-- depends_on: {{ ref('account_dse_v') }}
-- depends_on: {{ ref('product_v') }}
-- depends_on: {{ ref('eventtype_v') }}
-- depends_on: {{ ref('interaction_v') }}
-- depends_on: {{ ref('interactionaccount_v') }}
-- depends_on: {{ ref('interactionproduct_v') }}
-- depends_on: {{ ref('interactiontype_v') }}

{{ config(materialized='view') }}

{% if build_check_is_veeva_customer() %}

select 
'RTE'  as channel,
s.ownerid as repuid,
max(UPPER(arl.repname)) as repname,
s.email_sent_date_vod__c as interaction_date,
cast('NA' as varchar) as territory_vod__c,
s.account_vod__c accountuid,
case when p.externalid is null then 'OTHER' else s.product_vod__c end as productuid,
case when p.externalid is null then 'OTHER' else p.productname end as productname,
count(s.id) as interaction_count,
count(s.id) as product_interaction_count,
sum(case when coalesce(s.opened_vod__c,0) > 0 then 1 else 0 end) as open_count,
sum(case when coalesce(s.click_count_vod__c,0) > 0 then 1 else 0 end) as click_count
from {{ ref('sent_email_vod__c_v') }} s 
-- join {{ ref('approved_document_vod__c_v') }} a on a.id = s.approved_email_template_vod__c
join {{ ref('actor_v') }} arl on s.ownerid = arl.externalid  
left join {{ ref('product_v') }} p on s.product_vod__c = p.externalid
where s.Status_vod__c = 'Delivered_vod'
group by s.ownerid, s.email_sent_date_vod__c, s.account_vod__c, case when p.externalid is null then 'OTHER' else s.product_vod__c end, case when p.externalid is null then 'OTHER' else p.productname end;

{% else %}

with dim_data as 
(
    select
        a.externalid as repuid,
        max(UPPER(a.repname)) as repname,
        e.eventdatetimeutc as interaction_date,
        ad.externalid as accountuid,
        case when p.externalid is null then 'OTHER' else p.externalid end as productuid,
        case when p.externalid is null then 'OTHER' else p.productname end as productname
    from {{ ref('event_v') }} e
        inner join {{ ref('actor_v') }}  a on e.repid = a.repid 
        inner join {{ ref('account_dse_v') }}   ad on e.accountid = ad.accountid 
        inner join {{ ref('product_v') }}  p on e.productid = p.productid 
        inner join {{ref('eventtype_v') }} it on e.eventtypeid = it.eventtypeid and it.eventtypename in ('RTE_CLICK', 'RTE_OPEN')
    group by a.externalid, e.eventdatetimeutc, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, case when p.externalid is null then 'OTHER' else p.productname end
    union 
    select
        a.externalid as repuid,
        max(UPPER(a.repname)) as repname,
        i.startdatetime as interaction_date,
        ad.externalid as accountuid,
        case when p.externalid is null then 'OTHER' else p.externalid end as productuid,
        case when p.externalid is null then 'OTHER' else p.productname end as productname
     from 
    {{ ref('interaction_v') }} i 
    inner join {{ ref('actor_v') }}  a on i.repid = a.repid 
    inner join {{ ref('interactionaccount_v') }}  ia on i.interactionid = ia.interactionid
    inner join {{ ref('account_dse_v') }}   ad on ia.accountid = ad.accountid 
    inner join {{ ref('interactionproduct_v') }}   ip on i.interactionid = ip.interactionid 
    inner join {{ ref('product_v') }}  p on ip.productid = p.productid 
    inner join {{ref('interactiontype_v') }} it on i.interactiontypeid = it.interactiontypeid and it.interactiontypename = 'SEND_ANY'
    group by a.externalid, i.startdatetime, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, case when p.externalid is null then 'OTHER' else p.productname end
),
openclick_data as 
(
    select
        a.externalid as repuid,        
        e.eventdatetimeutc as interaction_date,
        ad.externalid as accountuid,
        case when p.externalid is null then 'OTHER' else p.externalid end as productuid,
        cast (null as bigint) interaction_count,
        cast (null as bigint) product_interaction_count,
        count(distinct case when it.eventtypename = 'RTE_OPEN' then concat(a.externalid, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, it.eventtypename, to_iso8601(e.eventdatetimeutc)) end) as open_count,
        count(distinct case when it.eventtypename = 'RTE_CLICK' then concat(a.externalid, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, it.eventtypename, to_iso8601(e.eventdatetimeutc)) end) as click_count
    from {{ ref('event_v') }} e
        inner join {{ ref('actor_v') }}  a on e.repid = a.repid 
        inner join {{ ref('account_dse_v') }}   ad on e.accountid = ad.accountid 
        inner join {{ ref('product_v') }}  p on e.productid = p.productid 
        inner join {{ref('eventtype_v') }} it on e.eventtypeid = it.eventtypeid and it.eventtypename in ('RTE_CLICK', 'RTE_OPEN')
    group by a.externalid, e.eventdatetimeutc, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end
),
interaction_data as
(
    select 
    a.externalid as repuid,
    i.startdatetime as interaction_date,
    ad.externalid as accountuid,
    case when p.externalid is null then 'OTHER' else p.externalid end as productuid,
    count(i.interactionid) as interaction_count,
    count(i.interactionid) as product_interaction_count,
    cast (null as bigint) open_count,
    cast (null as bigint) click_count
    from 
    {{ ref('interaction_v') }} i 
    inner join {{ ref('actor_v') }}  a on i.repid = a.repid 
    inner join {{ ref('interactionaccount_v') }}  ia on i.interactionid = ia.interactionid
    inner join {{ ref('account_dse_v') }}   ad on ia.accountid = ad.accountid 
    inner join {{ ref('interactionproduct_v') }}   ip on i.interactionid = ip.interactionid 
    inner join {{ ref('product_v') }}  p on ip.productid = p.productid 
    inner join {{ref('interactiontype_v') }} it on i.interactiontypeid = it.interactiontypeid and it.interactiontypename = 'SEND_ANY'
    group by a.externalid, i.startdatetime, ad.externalid, case when p.externalid is null then 'OTHER' else p.externalid end, case when p.externalid is null then 'OTHER' else p.productname end
)
select 
    'RTE'  as channel,
    d.repuid,
    d.repname,
    d.interaction_date,
    cast('NA' as varchar) as territory_vod__c,
    d.accountuid,
    d.productuid,
    d.productname,
    i.interaction_count,
    i.product_interaction_count,
    o.open_count,
    o.click_count
from dim_data d 
     left join interaction_data i on d.repuid = i.repuid and d.interaction_date = i.interaction_date and d.accountuid = i.accountuid and d.productuid = i.productuid
     left join openclick_data o on d.repuid = o.repuid and d.interaction_date = o.interaction_date and d.accountuid = o.accountuid and d.productuid = o.productuid

{% endif %}
