{{ config(materialized='view') }}

with suggestion_factor_tag as (
  select runuid, factoruid, min(tagid) tagid, min(tagname) tagname, min(tagtype) tagtype
  from {{ ref('sparkdserunconfigfactortag_v') }}
  where tagtype = 'USECASE'
  group by runuid, factoruid
),
suggestion_factor as (
  select a.runuid, a.factoruid, min(a.factorname) factorname, min(a.factortype) factortype, min(c.strategyid) strategyid, min(c.strategyname) strategyname
  from {{ ref('sparkdserunconfigfactor_v') }} a
        inner join {{ ref('sparkdserun_v') }} b on a.runuid = b.runuid 
        left join {{ ref('strategyfactor_map_v') }} c on b.seconfigid = c.seconfigid and a.factoruid = c.factoruid
  group by a.runuid, a.factoruid
)
select a.runuid, b.repid, b.repuid, b.suggesteddate, c.*, d.runrepdatesuggestionreasonid, d.reasonrank, d.crmfieldname, d.reasontext, d.sourcesystemname as reasonsourcesystemname, f.factoruid as reason_factoruid, f.factorname, f.factortype, f.strategyid, f.strategyname, t.tagid, t.tagname, t.tagtype
from {{ ref('sparkdserun_v') }} a  
     inner join {{ ref('sparkdserunrepdate_v') }} b on a.runuid = b.runuid
     inner join {{ ref('sparkdserunrepdatesuggestion_v') }} c on b.runrepdateid = c.runrepdateid
     inner join {{ ref('sparkdserunrepdatesuggestionreason_v') }} d on c.runrepdatesuggestionid = d.runrepdatesuggestionid
     left join suggestion_factor f on a.runuid = f.runuid and d.factoruid = f.factoruid
     left join suggestion_factor_tag t on a.runuid = t.runuid and d.factoruid = t.factoruid;


