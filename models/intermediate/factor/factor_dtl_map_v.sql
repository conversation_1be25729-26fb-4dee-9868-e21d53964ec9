{{ config(materialized='view') }}

with suggestion_factor_tag as (
  select runuid, factoruid, productuid, min(tagid) tagid, min(tagname) tagname, min(tagtype) tagtype
  from {{ ref('sparkdserunconfigfactortag_v') }}
  where tagtype = 'USECASE'
  group by runuid, factoruid, productuid
),
suggestion_factor as (
  select a.runuid, a.factoruid, min(a.factorname) factorname, min(a.factortype) factortype, min(c.strategyid) strategyid, min(c.strategyname) strategyname
  from {{ ref('sparkdserunconfigfactor_v') }} a
        inner join {{ ref('sparkdserun_v') }} b on a.runuid = b.runuid 
        left join {{ ref('strategyfactor_map_v') }} c on b.seconfigid = c.seconfigid and a.factoruid = c.factoruid
  group by a.runuid, a.factoruid
)
select a.runuid, b.repid, b.repuid, b.suggesteddate, c.*, d.runrepdatesuggestiondetailid,  d.actionorder, d.factoruid as detailfactoruid, d.detailrepactiontypeid, d.productuid, f.factorname, f.factortype, ff.strategyid, ff.strategyname, t.tagid, t.tagname, t.tagtype
from {{ ref('sparkdserun_v') }} a  
     inner join {{ ref('sparkdserunrepdate_v') }} b on a.runuid = b.runuid
     inner join {{ ref('sparkdserunrepdatesuggestion_v') }} c on b.runrepdateid = c.runrepdateid
     inner join {{ ref('sparkdserunrepdatesuggestiondetail_v') }} d on c.runrepdatesuggestionid = d.runrepdatesuggestionid
     left join {{ ref('sparkdserunconfigfactor_v') }} f on a.runuid = f.runuid and d.factoruid = f.factoruid and d.productuid = f.productuid
     left join suggestion_factor_tag t on a.runuid = t.runuid and d.factoruid = t.factoruid and d.productuid = t.productuid
     left join suggestion_factor ff on a.runuid = ff.runuid and d.factoruid = ff.factoruid 

