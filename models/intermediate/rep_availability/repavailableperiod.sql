{{ config(materialized='table') }}

-- depends_on: {{ ref('known_countrycode') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with rep_holiday as (
{%- if var_multi_country == 'false' %}  
select
    a.repid,
    a.externalid,
    h.date as dt,
    '{{ build_country() }}' as countrycode
    from {{ ref('actor_v') }} a cross join {{ ref('holiday_v') }} h 
{%- else %}
select a.repid, 
       a.externalid,
       h.date as dt, 
       k.countrycode 
 from {{ ref('actor_v') }} a inner join {{ ref('known_countrycode') }} k on a.configcountrycode = k.countrycode  
  cross join {{ ref('holiday_v') }} h 
  where h.configcountrycode = 'DEFAULT'
union all
select
    a.repid,
    a.externalid,
    h.date as dt,
    a.configcountrycode as countrycode
    from {{ ref('actor_v') }} a inner join {{ ref('holiday_v') }} h on a.configcountrycode = h.configcountrycode
{%- endif %}
),
rep_country as (
{%- if var_multi_country == 'false' %}  
select
    a.repid,
    a.externalid,
    '{{ build_country() }}' as countrycode
    from {{ ref('actor_v') }} a 
{%- else %}
select
    a.repid,
    a.externalid,
    a.configcountrycode as countrycode
    from {{ ref('actor_v') }} a
{%- endif %}
)
select rc.repid, rc.externalid, rc.externalid as repuid, dt, rc.countrycode as country
from rep_country rc  
cross join {{ ref('as_of_date') }}   
where dt >= {{ build_rolling_two_years_date(2) }} and dt <= {{ build_get_end_of_the_month(2) }} and 
      day_of_week(dt) not in (6,7)
except
select repid, externalid, externalid as repuid, dt, countrycode as country
from rep_holiday
except 
select rc.repid, rc.externalid, rc.externalid as repuid, dt, rc.countrycode as country 
from {{ ref('repunavailableperiod_v') }} ru
inner join rep_country rc on ru.repid = rc.repid 
inner join {{ ref('as_of_date') }} on (dt >= startdate and dt <= enddate)  
and dt >= {{ build_rolling_two_years_date(2) }} and dt <= {{ build_get_end_of_the_month(2) }}