{{ config(materialized='table') }}

-- depends_on: {{ ref('known_countrycode') }}
-- depends_on: {{ ref('fiscalperioddefinition_default_v') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

{%- if var_multi_country == 'false' %}  
select
    {{ dbt_utils.star(from=ref('fiscalperioddefinition_v'), except=['countrycode']) }} ,
    '{{ build_country() }}' as countrycode
from {{ ref('fiscalperioddefinition_v') }}
{%- else %}
with override_countries as 
(
    select countrycode 
    from {{ref('fiscalperioddefinition_v')}}
    where countrycode <> 'DEFAULT'
    group by countrycode
),
default_countries as 
(
    select countrycode 
    from 
    {{ ref('known_countrycode')}} where countrycode not in (select countrycode from override_countries)
)
select
    {{ dbt_utils.star(from=ref('fiscalperioddefinition_v'), except=['countrycode']) }},
    countrycode
from {{ref('fiscalperioddefinition_default_v')}} cross join default_countries
union 
select  {{ dbt_utils.star(from=ref('fiscalperioddefinition_v'), except=['countrycode']) }},
    countrycode 
from {{ref('fiscalperioddefinition_v')}} where countrycode <> 'DEFAULT'
{%- endif %}
