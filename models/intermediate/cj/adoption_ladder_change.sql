{{ config(materialized='table') }}

with cj_latest_combined as (
    SELECT
        row_number() over (partition by cj.accountid, cj.productid, cj.yearmonth 
        order by cj.yearmonth desc) as rn,
        cj.yearmonth,
        cj.accountid,
        acct.externalid as accountuid,
        cj.productid,
        prod.externalid as productuid,
        cj.segmentrank,
        cj.segment,
        cj.rankchange,
        case when cj.rankchange > 0 then 1 when cj.rankchange < 0 then -1 else 0 end as rankchange_binary
    FROM
      {{ref('cj_latest')}} cj
      inner join {{ ref('account_dse_v') }} acct on cj.accountid = acct.accountid
      inner join {{ ref('product_v') }} prod on cj.productid = prod.productid
      where cj.segmenttype='Combined'
),

unique_cj_latest_combined as (
    select * from cj_latest_combined where rn = 1
)
select accountid, productid, accountuid, productuid, segment, segmentrank, rankchange, rankchange_binary, 
date_parse(concat(yearMonth,'-01'), '%Y-%m-%d') as yearmonth 
from unique_cj_latest_combined








