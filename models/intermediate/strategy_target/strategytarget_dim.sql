{{ config(materialized='table') }}

with repteam_target as 
(
select repteamid, accountid, productid, startdate, enddate, 
       sum(case when p.interactiontypename = 'VISIT' and target > 0 then 1 else 0 end) as visit_target,
       sum(case when p.interactiontypename = 'SEND_ANY' and target > 0 then 1 else 0 end) as email_target
from 
{{ ref('strategytarget_v') }} s inner join {{ ref('targetsperiod_v') }} t on s.targetsperiodid = t.targetsperiodid
inner join {{ ref('interactiontype_v') }} p on s.interactiontypeid = p.interactiontypeid
where repteamid is not null and repid is null and productid is not null
group by repteamid, accountid, productid, startdate, enddate
),
rep_target as 
(
select repid, accountid, productid, startdate, enddate, 
       sum(case when p.interactiontypename = 'VISIT' and target > 0 then 1 else 0 end) as visit_target,
       sum(case when p.interactiontypename = 'SEND_ANY' and target > 0 then 1 else 0 end) as email_target
from 
{{ ref('strategytarget_v') }} s inner join {{ ref('targetsperiod_v') }} t on s.targetsperiodid = t.targetsperiodid
inner join {{ ref('interactiontype_v') }} p on s.interactiontypeid = p.interactiontypeid
where repid is not null and repteamid is null and productid is not null
group by repid, accountid, productid, startdate, enddate
)
select rt.accountid, rt.productid, rt.startdate, rt.enddate, rt.repteamid, cast(null as int) repid,  visit_target, email_target 
from repteam_target rt
union 
select accountid, productid, startdate, enddate, cast(null as int) repteamid, repid,  visit_target, email_target 
from rep_target
