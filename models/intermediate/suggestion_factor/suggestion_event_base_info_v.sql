{{ config(materialized='view') }}

select 
    i.accountid, 
    i.repid,  
    et.eventtypeid,
    i.accountuid, 
    i.repuid, 
    i.accountname, 
    coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
    i.repname, 
    et.eventtypename,
    case when reportedinteractionuid is not null then concat(i.reportedinteractionuid, i.accountuid, 'COMPLETE') 
        when inferredinteractionuid is not null then concat(i.inferredinteractionuid, i.accountuid, 'COMPLETE')
        else null
    end as corellationid ,
    case when reportedinteractionuid is not null then reportedinteractionuid
        when inferredinteractionuid is not null then inferredinteractionuid
        else null
    end as linkedinteractionuid,
    i.reportedinteractionuid,
    i.suggestiondriver,
    i.suggestionreferenceid,
    i.internalsuggestionreferenceid,
    i.detailrepactionname,
    i.startdatelocal as eventdate,
    i.startdatelocal as eventdatetimeutc,
    i.suggesteddate as actiondatetime, 
    concat(i.detailrepactionname, ' ', actiontaken) eventlabel,
    i.suggestionreferenceid externalid,
    et.isconversionevent, 
    coalesce(i.tagname, 'Channel Execution') as usecasename,
    actiontaken actiontaken,
    i.issuggestioncompleteddirect issuggestioncompleteddirect, 
    i.issuggestioncompletedinfer issuggestioncompletedinfer,
    i.factoruid as factoruid,
    coalesce(i.factorname, 'Channel Execution') as factorname,
    i.strategyid,
    coalesce(i.strategyname, 'Undefined Strategy') as strategyname,
    i.seconfigid as seconfigid, 
    i.seconfigname as seconfigname,
    i.detailrepactiontypeid,
    i.productid,
    i.productuid,
    i.productname,
    i.iscompleted
from {{ ref('suggestion_factor_v') }}  i
inner join {{ ref('kpi_eventtype') }} et
    on concat('KPI',i.detailrepactiontypeuid,'-SUGGESTED') = et.eventtypename 
left join {{ ref('akt_account_segment_mapping_v') }} aasm 
    on aasm.accountId = i.accountid
