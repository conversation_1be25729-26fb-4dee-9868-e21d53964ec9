{{ config(materialized='view') }}

with 
suggestion_candidate_products as
(
    select 
        suggestionreference.suggestionreferenceid as suggestionreferenceid,
        --suggesteddate,
        --rundate,
        i.productid.externalid as productuid
    from {{ ref('suggestion_candidates_v') }} 
    cross join UNNEST(products) as t(i)
    group by
        suggestionreference.suggestionreferenceid,
        --suggesteddate,
        --rundate,
        i.productid.externalid
)
select 
    s.accountid.externalid accountuid,
    coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
    pr.productuid,
    actorid.externalid repuid,
    concat('KPI',channelinfo.actiontype.externalId,'-CANDIDATE') eventtypename,
    concat('KPI', channelinfo.actiontype.externalId, '-COMPLETED') intmatcheventtypename,
    coalesce(fn.tagname, 'Channel Execution') as usecasename,
    coalesce(fn.strategyname, 'Undefined Strategy') as strategyname,
    fn.factoruid as factoruid,
    coalesce(fn.factorname, 'Channel Execution') as factorname,
    cast(null as VARCHAR) messagetopic, 
    date_parse(suggesteddate, '%Y-%m-%d') as eventdatetimeutc,
    source.type as suggestiondriver,
    suggestionreference.suggestionreferenceid as suggestionreferenceid,  
    suggestionreference.internalsuggestionreferenceid as internalsuggestionreferenceid,  
    channelinfo.actiontype.id actionTypeId,
    s.metadata.runUID runUID,
    row_number() OVER (
        PARTITION BY
            s.accountid.externalid,
            actorid.externalid,
            channelinfo.actiontype.externalId,
            fn.factoruid,
            fn.factorname,
            pr.productuid,
            date_parse(suggesteddate, '%Y-%m-%d')
        ORDER BY
            s.accountid.externalid,
            actorid.externalid,
            channelinfo.actiontype.externalId,
            fn.factoruid,
            fn.factorname,
            pr.productuid,
            date_parse(suggesteddate, '%Y-%m-%d') desc,
            rundate desc ) as candidate_order,
    s.isRecommended as isRecommended
from {{ ref('suggestion_candidates_v') }} s
cross join UNNEST(reasons) as t(i)
left join (
        select
            f.runuid,
            f.factoruid,
            f.productuid,
            min(factorname) as factorname,
            min(tagname) as tagname,
            max(strategyname) as strategyname
        from {{ ref('sparkdserunconfigfactor_v') }} f
        left join {{ ref('sparkdserunconfigfactortag_v') }} t
            on f.runuid = t.runuid
            and f.factoruid = t.factoruid
            and t.tagtype = 'USECASE'            
        group by
            f.runuid,
            f.factoruid,
            f.productuid) as fn
    on fn.runuid = metadata.runuid
    and fn.factoruid = i.factoruid
inner join suggestion_candidate_products pr
    on suggestionreference.suggestionreferenceid = pr.suggestionreferenceid
    --and s.suggesteddate = pr.suggesteddate
    --and s.rundate = pr.rundate
    and fn.productuid = pr.productuid
inner join {{ ref('all_msrscenario_daterange_v') }} p
    on p.period_type = 'Analysis Period'
left join {{ ref('akt_account_segment_mapping_v') }} aasm 
    on aasm.accountId = s.accountid.id 
where (p.period_type = 'Analysis Period'
    and date_parse(suggesteddate, '%Y-%m-%d') >= p.dt_begin
    and date_parse(suggesteddate, '%Y-%m-%d') <= p.dt_end)