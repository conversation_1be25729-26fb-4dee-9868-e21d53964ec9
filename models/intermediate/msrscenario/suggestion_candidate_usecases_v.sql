{{ config(materialized='view') }}

select 
    s.accountid.externalid accountuid,
    coalesce(aasm.hcpSegmentName, 'notier') as segmentname,
    actorid.externalid repuid,
    concat('KPI',channelinfo.actiontype.externalId,'-CANDIDATE') eventtypename,
    concat('KPI', channelinfo.actiontype.externalId, '-COMPLETED') intmatcheventtypename,
    case when cardinality(primaryfactor.tags) > 0 then primaryfactor.tags[1].tagname else cast (null as VARCHAR) end as usecasename,
    strategyname,
    primaryfactor.factorId as factoruid,
    primaryfactor.name as factorname,
    cast(null as VARCHAR) messagetopic, 
    date_parse(suggesteddate, '%Y-%m-%d') as eventdatetimeutc,
    source.type as suggestiondriver,
    suggestionreference.suggestionreferenceid as suggestionreferenceid,  
    suggestionreference.internalsuggestionreferenceid as internalsuggestionreferenceid,  
    channelinfo.actiontype.id actionTypeId,
    row_number() OVER (
        PARTITION BY
            s.accountid.externalid,
            actorid.externalid,
            channelinfo.actiontype.externalId,
            date_parse(suggesteddate, '%Y-%m-%d')
        ORDER BY
            s.accountid.externalid,
            actorid.externalid,
            channelinfo.actiontype.externalId,
            date_parse(suggesteddate, '%Y-%m-%d') desc,
            rundate desc ) as candidate_order
from {{ ref('suggestion_candidates_v') }} s
inner join {{ ref('all_msrscenario_daterange_v') }} p
    on p.period_type = 'Analysis Period'
left join {{ ref('akt_account_segment_mapping_v') }} aasm 
    on aasm.accountId = s.accountid.id 
where (p.period_type = 'Analysis Period'
    and date_parse(suggesteddate, '%Y-%m-%d') >= p.dt_begin
    and date_parse(suggesteddate, '%Y-%m-%d') <= p.dt_end)