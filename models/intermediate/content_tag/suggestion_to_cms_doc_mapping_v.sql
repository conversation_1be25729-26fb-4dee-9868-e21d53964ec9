{{ config(materialized='view') }}

with stable_message as (
select m.messageid, sm.messageuid, sm.messagesource, sm.lastphysicalmessageuid 
from {{ ref('message_v') }} m inner join {{ ref('akt_stablemessage_v') }} sm on sm.messageuid = m.externalid
),
cms_message_mapping as (
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.vault_document_id_vod__c cms_messageuid from stable_message sm join {{ ref('approved_document_vod__c_v') }} d on sm.messageuid = d.id where messagesource = 'RTE'
union
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.vault_doc_id_vod__c cms_messageuid from stable_message sm join {{ ref('key_message_vod__c_v') }}  d on sm.messageuid = d.id where messagesource = 'KEY'
union
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.presentation_id_vod__c cms_messageuid /* vault_doc_id_vod__c */ from stable_message sm join {{ ref('clm_presentation_vod__c_v') }}  d on sm.messageuid = d.id where messagesource = 'CLM'
),
suggestion_message as (
select s.runrepdatesuggestionid, s.suggestionreferenceid, s.internalsuggestionreferenceid, rrd.suggesteddate, accountid, accountuid, suggestedchanneluid, d.runrepdatesuggestiondetailid, d.productid, d.productuid, d.messageid
from {{ ref('sparkdserunrepdate_v') }}  rrd 
inner join {{ ref('sparkdserunrepdatesuggestion_v') }} s on rrd.runrepdateid = s.runrepdateid
left join {{ ref('sparkdserunrepdatesuggestiondetail_v') }} d on s.runrepdatesuggestionid = d.runrepdatesuggestionid
where cast(s.isactive as boolean) = true
)
select runrepdatesuggestionid, suggestionreferenceid, internalsuggestionreferenceid, suggesteddate, accountid, accountuid, suggestedchanneluid, runrepdatesuggestiondetailid, productid, productuid, s.messageid, m.messageuid, m.lastphysicalmessageuid, m.messagesource, m.cms_messageuid
from suggestion_message s inner join cms_message_mapping m on s.messageid = m.messageid
