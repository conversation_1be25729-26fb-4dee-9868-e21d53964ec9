{{ config(materialized='view') }}

select i.accountId , i.accountUid , i.productId, i.productName, i.productUid, i.repId, i.repUid, 
i.currentChannel, i.activityDate, i.interactionId, m.cms_messageuid, t.tag, t.document_type
from {{ ref('interaction_account_product_history_v') }} i
left join {{ ref('interaction_to_cms_doc_mapping_v') }} m on i.interactionId = m.interactionId and i.productUid = m.productuid -- get CMS document for interaction/product
left join {{ ref('ctag_out_document_topics_v')}} t on m.cms_messageuid = t.cms_messageuid and m.productuid = t.productuid and t.topic_rank = 1
