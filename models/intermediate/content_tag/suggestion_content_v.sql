{{ config(materialized='view') }}

select runrepdatesuggestionid, suggestionreferenceid, internalsuggestionreferenceid, suggesteddate, runrepdatesuggestiondetailid, accountid, accountuid, suggestedchanneluid, s.productid, s.productuid, messageid, s.messageuid, lastphysicalmessageuid, messagesource, s.cms_messageuid, t.tag, t.document_type
from {{ ref('suggestion_to_cms_doc_mapping_v') }} s
left join {{ ref('ctag_out_document_topics_v')}} t on s.cms_messageuid = t.cms_messageuid and s.productuid = t.productuid and t.topic_rank = 1