models:
  - name: ctag_messages_recently_used
    description: recently used messages for tagging
    config:
      materialized: table
    meta:
      fal:
        post-hook:
          - utils/content_tag/tagging.py
  - name: ctag_document_topics_v
    description: Document Topics
    meta:
      fal:
        post-hook:
          - path: utils/kpi/kpi_table_move.py
            with:
              source_table_name: "ctag_document_topics_v"
              target_table_name: "MessageTopic"
              post_load_operations: ["ALTER TABLE MessageTopic ADD UNIQUE INDEX messageTopic_unique1 (messageTopicName(256))",
              						 "CALL PopulateAudienceType()"]
  - name: ctag_output_export_v
    description: top ranked topic for output of tagging
    config:
      materialized: view
    meta:
      fal:
        post-hook:
          - utils/content_tag/export_output.py
