
{{ config(materialized='view') }}

select i.interactionId, date(i.startDateTime) startDate, a.Id Sent_Email_vod__c, a.product_vod__c productuid, a.approved_email_template_vod__c, b.vault_document_id_vod__c --, max(b.document_description_vod__c) messagedescription
from {{ ref('interaction_v') }}  i
inner join {{ ref('sent_email_vod__c_v') }} a on i.externalId = a.Id
inner join {{ ref('approved_document_vod__c_v') }} b on a.approved_email_template_vod__c = b.id
-- group by i.interactionId, a.Id, a.approved_email_template_vod__c, b.vault_document_id_vod__c 
