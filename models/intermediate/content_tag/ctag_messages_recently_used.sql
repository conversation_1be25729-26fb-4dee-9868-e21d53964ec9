{{ config(materialized='table', format='parquet') }}

with key_messages as 
(
    select 'CLM' as document_type, c.product_vod__c as productuid, k.vault_doc_id_vod__c as cms_messageuid, max(key_message_name_vod__c) messagedescription,
	-- select 'CLM' as document_type, k.product_vod__c as productuid, "Vault_Doc_Id_vod__c" as messageuid, max(key_message_name_vod__c) as messagedescription,
	-- max(coalesce(k.Description_vod__c, k.Name)) as messagedescription,
    max(sc.content) as messagecontent, max(productname) as productname, count(*) document_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', 0, date_trunc('month', current_date)) then 1 else 0 end) current_month_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -1, date_trunc('month', current_date)) then 1 else 0 end) month_1_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -2, date_trunc('month', current_date)) then 1 else 0 end) month_2_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -3, date_trunc('month', current_date)) then 1 else 0 end) month_3_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -4, date_trunc('month', current_date)) then 1 else 0 end) month_4_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -5, date_trunc('month', current_date)) then 1 else 0 end) month_5_count,
		sum(case when date_trunc('month', start_time_vod__c) = date_add('month', -6, date_trunc('month', current_date)) then 1 else 0 end) month_6_count,
		sum(case when date_trunc('month', start_time_vod__c) <= date_add('month', -7, date_trunc('month', current_date)) then 1 else 0 end) month_beyond6_count
    -- from {{ ref('call2_key_message_vod__c_v') }} c
	-- inner join {{ ref('key_message_vod__c_v') }} k on c.Key_Message_vod__c = k.Id 
	-- inner join {{ ref('product_v') }} b on k.product_vod__c = b.externalid and  b.iscompetitor = false
    -- where start_time_vod__c > date_add('month', -12, now()) and Vault_Doc_Id_vod__c is not null
    -- group by k.product_vod__c, "Vault_Doc_Id_vod__c"
	from {{ ref('call2_key_message_vod__c_v') }} c
    inner join {{ ref('product_v') }} b on c.product_vod__c = b.externalid and cast(b.iscompetitor as boolean) = false
    inner join {{ ref('key_message_vod__c_v') }} k on c.key_message_vod__c = k.Id
    inner join {{ ref('ctag_scraped_content') }} sc on sc.cms_messageuid = k.vault_doc_id_vod__c
    where start_time_vod__c > date_add('month', -12, now()) and k.vault_doc_id_vod__c is not null
    group by c.product_vod__c, k.vault_doc_id_vod__c
    having count(*) > (SELECT ctag_docs_recent_usage_count FROM {{ ref('param_msrscenario_ctag_count_v') }})
),
approved_documents as
(
    select 'RTE' as document_type, a.product_vod__c as productuid, b.vault_document_id_vod__c as cms_messageuid, max(document_description_vod__c) messagedescription, 
                 max(a.email_content_vod__c) as messagecontent, max(c.productname) as productname, count(*) document_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', 0, date_trunc('month', current_date)) then 1 else 0 end) current_month_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -1, date_trunc('month', current_date)) then 1 else 0 end) month_1_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -2, date_trunc('month', current_date)) then 1 else 0 end) month_2_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -3, date_trunc('month', current_date)) then 1 else 0 end) month_3_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -4, date_trunc('month', current_date)) then 1 else 0 end) month_4_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -5, date_trunc('month', current_date)) then 1 else 0 end) month_5_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) = date_add('month', -6, date_trunc('month', current_date)) then 1 else 0 end) month_6_count,
		sum(case when date_trunc('month', email_sent_date_vod__c) <= date_add('month', -7, date_trunc('month', current_date)) then 1 else 0 end) month_beyond6_count
    from {{ ref('sent_email_vod__c_v') }} a
    inner join {{ ref('approved_document_vod__c_v') }} b on a.approved_email_template_vod__c = b.id
	inner join {{ ref('product_v') }} c on a.product_vod__c = c.externalid and cast(c.iscompetitor as boolean) = false
    where "email_sent_date_vod__c" > date_add('month', -12, now()) and b.vault_document_id_vod__c is not null
	and coalesce(document_description_vod__c, '') not like '%Z_DONOTSEND%'
    group by a.product_vod__c, b.vault_document_id_vod__c
    having count(*) > (SELECT ctag_docs_recent_usage_count FROM {{ ref('param_msrscenario_ctag_count_v') }})
),

ctag_document_count as 
(
	select ctag_docs_per_topic 
	from {{ ref('param_msrscenario_ctag_v') }} p
),

interaction_messages as (
select document_type, productuid, productname, cms_messageuid, messagedescription, messagecontent, document_count, current_month_count, month_1_count, month_2_count, month_3_count, month_4_count, month_5_count, month_6_count, month_beyond6_count
from key_messages
UNION
select document_type, productuid, productname, cms_messageuid, messagedescription, messagecontent, document_count, current_month_count, month_1_count, month_2_count, month_3_count, month_4_count, month_5_count, month_6_count, month_beyond6_count
from approved_documents
UNION
SELECT 'NCO' as document_type, j_productuid as productuid, p.productname, split_part(messageuid, '~', 2) as cms_messageuid, j_messagedescription as messagedescription,
	'' messagecontent, 1 document_count, 1 current_month_count, 1 month_1_count, 1 month_2_count, 1 month_3_count, 1 month_4_count, 1 month_5_count, 1 month_6_count, 1 month_beyond6_count
	from abbottus_prod_current.tempdata_mas_message t
	join product_v p on t.j_productuid = p.externalId
	where messageuid like '%ADC%'
)

select * from interaction_messages
-- TODO: union with candidates messages
-- union all
-- select * from candidates_content where cms_messageuid not exist in (interaction_messages)

