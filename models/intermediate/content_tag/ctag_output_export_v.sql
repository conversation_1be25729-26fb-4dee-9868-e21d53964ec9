{{ config(materialized='view') }}

with message_topic_scores as (
select document_type, productuid, split_part(topic_name, ' - ', 2) productname, tag, ms.topic_uid, ms.topic_name, split_part(messageproductuid, '-', 1) as cms_messageuid, doc_score, doc_rank, ms.category as category
from {{ source( 'content_tag', 'ctag_out_messageset')  }} ms inner join {{ source( 'content_tag', 'ctag_out_messagesetmessage')  }} msm on ms.topic_uid = msm.topic_uid
),
ranked_topics AS (
SELECT document_type, productuid, productname, tag, topic_uid, topic_name, cms_messageuid, doc_score, ROW_NUMBER() OVER (PARTITION BY cms_messageuid ORDER BY doc_score DESC) AS topic_rank, category
from message_topic_scores
)
-- return the top topic for each CMS document/message to export as csv
SELECT * FROM ranked_topics where topic_rank < 2
