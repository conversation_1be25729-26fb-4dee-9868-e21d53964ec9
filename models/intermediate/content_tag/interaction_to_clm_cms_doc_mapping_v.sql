
{{ config(materialized='view') }}

select i.interactionId interactionId, date(i.startDateTime) startDate, a.Call2_vod__c, a.Id Call2_Key_Message_vod__c, b.product_vod__c productuid, a.Key_Message_vod__c, a.presentation_id_vod__c, b.Vault_Doc_Id_vod__c --, max(b.Description_vod__c) as messagedescription
from
{{ ref('interaction_v') }}  i
inner join {{ ref('call2_key_message_vod__c_v') }} a on i.externalId = a.Call2_vod__c
inner join {{ ref('key_message_vod__c_v') }} b on a.Key_Message_vod__c = b.Id
-- group by i.interactionId, a.Call2_vod__c, a.Id, a.Key_Message_vod__c, b.Vault_Doc_Id_vod__c