
{{ config(materialized='view') }}

select 'RTE' document_type, interactionId, startDate, Sent_Email_vod__c interactionuid, productuid, approved_email_template_vod__c document_uid, vault_document_id_vod__c cms_messageuid
from {{ ref('interaction_to_email_cms_doc_mapping_v') }}
union ALL
select 'CLM' document_type, interactionId, startDate, Call2_vod__c interactionuid, productuid, Key_Message_vod__c document_uid, presentation_id_vod__c cms_messageuid -- Vault_Doc_Id_vod__c cms_messageuid
from {{ ref('interaction_to_clm_cms_doc_mapping_v') }}

