{{ config(materialized='view') }}

select a.accountId , a.accountUid ,
p.parentProductId productId, 
p.parentProductName productName,
p.parentProductUid productUid,
a.repId, a.repUid, 
a.currentChannel, a.activityDate, a.interactionId, a.interactionuid
from {{ ref('interaction_account_product_history_v') }} a
join {{ ref('product_group_mapping_v') }} p
on a.productUid = p.productUid
where p.productType = 'Child'
-- and p.groupType = 'Default'