{{ config(materialized='view') }}

select ia.accountId , a.externalId accountUid , ip.productId, p.productName, p.externalId productUid , i.repId, r.externalId repUid, 
case when ictv.call_channel_dse is not null then ictv.call_channel_dse
     else acm.channelName end currentChannel, 
date(startDateTime) activityDate, i.interactionId, i.externalid as interactionuid
from {{ ref('interaction_v') }} i
inner join {{ ref('interactionaccount_v') }} ia on i.interactionId = ia.interactionId 
inner join {{ ref('interactionproduct_v') }} ip on i.interactionId = ip.interactionId
inner join {{ ref('rep_v') }} r on i.repId = r.repId
inner join {{ ref('actionchannelmap_v') }} acm on acm.actionTypeId = i.repActionTypeId and acm.actorTypeId = r.repTypeId 
inner join {{ ref('account_dse_v') }} a on ia.accountId = a.accountId 
inner join {{ ref('product_v') }} p on ip.productId = p.productId 
left join {{ ref('interaction_call_type_v') }} ictv on i.externalid = ictv.interactionuid
where cast(i.isCompleted as boolean) = true and cast(i.isDeleted as boolean) != true