{{ config(materialized='view') }}

WITH stable_message AS (
    SELECT
        m.messageid,
        sm.messageuid,
        sm.messagesource,
        sm.lastphysicalmessageuid
    FROM
    {{ ref ('message_v')}}   m
    INNER JOIN
     {{ ref('akt_stablemessage_v')}}  sm
        ON sm.messageuid = m.externalid
),
cms_message_mapping AS (
    SELECT
        messagesource,
        sm.messageid,
        sm.messageuid,
        sm.lastphysicalmessageuid,
        d.vault_document_id_vod__c AS cms_messageuid
    FROM
        stable_message sm
    JOIN
      {{  ref('approved_document_vod__c_v')}}  d
        ON sm.messageuid = d.id
    WHERE
        messagesource = 'RTE'

    UNION

    SELECT
        messagesource,
        sm.messageid,
        sm.messageuid,
        sm.lastphysicalmessageuid,
        d.vault_doc_id_vod__c AS cms_messageuid
    FROM
        stable_message sm
    JOIN
      {{ ref('key_message_vod__c_v')}}  d
        ON sm.messageuid = d.id
    WHERE
        messagesource = 'KEY'
    UNION
    SELECT
        messagesource,
        sm.messageid,
        sm.messageuid,
        sm.lastphysicalmessageuid,
        d.presentation_id_vod__c AS cms_messageuid
    FROM
        stable_message sm
    JOIN
       {{ ref('clm_presentation_vod__c_v')}} d
        ON sm.messageuid = d.id
    WHERE
        messagesource = 'CLM'
),

scored_candidates AS (
    SELECT
        d.*,
        p.productid.id AS unnested_productid,
        p.messageinfo.messages[1].externalid AS external_messageuid
    FROM
        {{ ref('scored_candidates_v') }} d
    CROSS JOIN
        UNNEST(d.products) AS t(p)
    WHERE
        d.message IS NOT NULL
        AND p.productid.id IS NOT NULL
),

hcp_content_affinity_split AS (
    SELECT
        hca.accountid,
        hca.productid,
        SPLIT_PART(hca.recommended_content, ',', 1) AS recommended_content_1,
        SPLIT_PART(hca.recommended_content, ',', 2) AS recommended_content_2
    FROM
        {{ ref('hcp_content_affinity_v') }} hca
),
cj_latest_max_yearmonth AS (
    SELECT
        accountid,
        productid,
        MAX(yearmonth) AS max_yearmonth
    FROM
      {{ ref('cj_latest')}}
    GROUP BY accountid, productid
),
cj_latest_with_max AS (
    SELECT
        cj.accountid,
        cj.productid,
        cj.segmenttype,
        cj.segmentrank,
        cj.rankchange,
        cj.attribute,
        cj.yearmonth,
        cj.segment
    FROM
      {{ref('cj_latest')}} cj
    INNER JOIN cj_latest_max_yearmonth cj_max
        ON cj.accountid = cj_max.accountid
        AND cj.productid = cj_max.productid
        AND cj.yearmonth = cj_max.max_yearmonth
        AND cj.segmenttype='Combined'
)
SELECT
    hca_split.recommended_content_1,
    hca_split.recommended_content_2,
    scored_candidates.*,
    cms_message_mapping.cms_messageuid,
    t.topic_name,
    t.topic_rank,
    r.repname,
    ac_cs.firstname as account_firstname,
    ac_cs.lastname as account_lastname,
    ac.accountname,
    ac.specialties_std_akt,
    f.facilityname,
    ac.specialties_std_akt gnespecialty1_akt,
    pro.productname,
    cj_with_max.segmenttype,
    cj_with_max.segmentrank,
    cj_with_max.segment,
    cj_with_max.rankchange,
    ap.hcpTier_std_akt,
    tag.tagname AS use_case_tag,
    factor.factorname,
    tag.displaylabel,
    insight_summary_v.latest_insight_text,
    cp_latest_v.send_prob_cali_segment,
    cp_latest_v.visit_prob_cali_segment,
    cp_latest_v.virtual_visit_prob_cali_segment,
    cp_latest_v.preferred_channel,
    cp_latest_v.preferred_channel_segment,
    Case
        WHEN dslc.isSuggestionCompleted IS NULL THEN 'Not Published'
	    When (cast(dslc.isSuggestionActioned as tinyint)=1 and (dslc.reportedInteractionUID is not null or dslc.inferredInteractionUID is not null)) or dslc.isSuggestionCompleted=1 or dslc.isSuggestionCompletedInfer=1 then 'Accepted'
	    When dslc.isSuggestionDismissed=1 then 'Dismissed'
	    Else 'Not Acted Upon'
    END AS candidateStatus,
    dslc.externalid as sfId,
    {{ dbt_utils.star(from=ref('account_dse_v'), relation_alias="ac", prefix="ac_") }},
    {{ dbt_utils.star(from=ref('repaccountassignment_v'), relation_alias="ra", prefix="ra_") }},
    {{ dbt_utils.star(from=ref('account_dse_v'), relation_alias="ap", prefix="ap_") }}

FROM
    scored_candidates
    LEFT JOIN cms_message_mapping
        ON scored_candidates.external_messageuid = cms_message_mapping.messageuid
    LEFT JOIN {{ ref('ctag_out_document_topics_v') }} t
        ON CAST(cms_message_mapping.cms_messageuid AS VARCHAR) = t.cms_messageuid
        AND CAST(scored_candidates.unnested_productid AS VARCHAR) = t.productuid
    LEFT JOIN {{ ref('rep_v') }} r
        ON scored_candidates.repid = r.repid
    LEFT JOIN {{ ref('account_dse_v') }} ac
        ON ac.accountid = scored_candidates.accountid
    LEFT JOIN {{ ref('account_cs_v') }} ac_cs
        ON ac_cs.id = scored_candidates.accountuid
    LEFT JOIN {{ ref('facility_v') }} f
        ON ac.facilityid = f.facilityid
    LEFT JOIN  hcp_content_affinity_split hca_split
        ON scored_candidates.unnested_productid = hca_split.productid
        AND ac.accountid = hca_split.accountid
    LEFT JOIN {{ref('product_v')}} pro
        ON scored_candidates.unnested_productid = pro.productid
    LEFT JOIN cj_latest_with_max cj_with_max
        ON scored_candidates.accountid = cj_with_max.accountid
        AND scored_candidates.unnested_productid = cj_with_max.productid
    LEFT JOIN {{ref('accountproduct_v')}} ap
        ON scored_candidates.accountid = ap.accountid
        AND scored_candidates.unnested_productid = ap.productid
    LEFT JOIN {{ref('repaccountassignment_v')}} ra
        ON scored_candidates.accountid = ra.accountid
        AND scored_candidates.repid = ra.repid
    LEFT JOIN {{ref('sparkdserunconfigfactor_v')}} factor
        ON scored_candidates.factoruid = factor.factoruid
        AND scored_candidates.dserunuid = factor.runuid
        AND scored_candidates.unnested_productid = factor.productid
    LEFT JOIN {{ref('sparkdserunconfigfactortag_v')}} tag
        ON scored_candidates.factoruid = tag.factoruid
        AND scored_candidates.dserunuid = tag.runuid
        AND tag.tagtype='USECASE'
    LEFT JOIN {{ref('insight_summary_v')}}
        ON scored_candidates.accountid = insight_summary_v.accountId
        AND TRY_CAST(scored_candidates.suggestedDate AS DATE) >= insight_summary_v.first_insight_date
    LEFT JOIN {{ref('cp_latest_v')}}
        ON scored_candidates.accountid = cp_latest_v.accountId
    LEFT JOIN {{ ref('dsesuggestionlifecycle_v') }} dslc
        ON scored_candidates.suggestionReferenceId = dslc.suggestionReferenceId



