{{ config(materialized='view') }}

with stable_message as (
select m.messageid, sm.messageuid, sm.messagesource, sm.lastphysicalmessageuid 
from {{ ref('message_v') }} m inner join {{ ref('akt_stablemessage_v') }} sm on sm.messageuid = m.externalid
),
cms_message_mapping as (
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.vault_document_id_vod__c cms_messageuid from stable_message sm join {{ ref('approved_document_vod__c_v') }} d on sm.messageuid = d.id where messagesource = 'RTE'
union
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.vault_doc_id_vod__c cms_messageuid from stable_message sm join {{ ref('key_message_vod__c_v') }}  d on sm.messageuid = d.id where messagesource = 'KEY'
union
select messagesource, sm.messageid, sm.messageuid, sm.lastphysicalmessageuid, d.presentation_id_vod__c cms_messageuid /* vault_doc_id_vod__c */ from stable_message sm join {{ ref('clm_presentation_vod__c_v') }}  d on sm.messageuid = d.id where messagesource = 'CLM'
)

select suggestionCandidateUid, suggestionReferenceId, internalSuggestionReferenceId, suggestedDate, accountid, accountuid, suggestedChannelId,
c.productid, c.productuid,
m.messageid, m.messageuid as messageuid, p.messageUID lastphysicalmessageuid, m.messagesource, p.cmsMessageUid cms_messageuid
from {{ source('engine_generated', 'dco_candidates') }} d
cross join unnest(content_recommended) as t(c)
cross join unnest(t.c.potentialMessages) as t2(p)
join cms_message_mapping m on p.cmsMessageUid = m.cms_messageuid
where d.suggestionreferenceid is not null