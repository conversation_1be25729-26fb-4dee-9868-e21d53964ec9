{{ config(materialized='view') }}

select i.accountId , i.accountUid , i.productId, i.productName, i.productUid, i.repId, i.repUid,
i.currentChannel, i.activityDate, i.interactionId, cast(null as varchar) as cms_messageuid, cast(null as varchar) as tag, cast(null as varchar) as document_type,
{{ dbt_utils.star(from=ref('account_dse_cdc_expiry_date_v'), except=['accountId', 'accountUid', 'createdat', 'updatedat', 'updatedatyear', 'updatedatmonth', 'updatedatday', 'updatedat_expiry_dt', 'is_dummy_record'], relation_alias='a', prefix='a') }},
{{ dbt_utils.star(from=ref('accountproduct_v'), except=['accountId', 'accountUid', 'productId', 'productUid', 'createdat', 'updatedat', 'updatedatyear', 'updatedatmonth', 'updatedatday', 'updatedat_expiry_dt', 'squadpriority_akt', 'is_dummy_record'], relation_alias='ap', prefix='ap') }},
{{ dbt_utils.star(from=ref('repaccountassignment_cdc_expiry_date_v'), except=['accountId', 'accountUid', 'repId', 'repUid', 'createdat', 'updatedat', 'updatedatyear', 'updatedatmonth', 'updatedatday', 'updatedat_expiry_dt', 'is_dummy_record'], relation_alias='ra', prefix='ra') }}
from {{ ref('interaction_account_product_history_v') }} i
left join {{ ref('account_dse_cdc_expiry_date_v') }} a on i.accountId = a.accountId and i.activityDate between a.updatedat and a.updatedat_expiry_dt
left join {{ ref('accountproduct_v') }} ap on i.accountId = ap.accountId and i.productid = ap.productid
left join {{ ref('repaccountassignment_cdc_expiry_date_v') }} ra on i.accountId = ra.accountId and i.repid = ra.repid and i.activityDate between ra.updatedat and ra.updatedat_expiry_dt
