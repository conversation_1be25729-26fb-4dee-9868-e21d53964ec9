{{ config(materialized='view') }}

SELECT
    sdaei.accountenhancedinsightreferenceid,  -- Insight reference ID
    sda.accountId,                            -- Account ID
    MIN(r.startDateLocal) AS first_insight_date,  -- First insight date
    MAX(r.startDateLocal) AS last_insight_date,   -- Last insight date
    MAX(sdaei.insightText) AS latest_insight_text  -- Most recent insight text
FROM
    {{ ref("sparkdserun_v") }} r
INNER JOIN
    {{ ref("sparkdserunaccount_v") }} sda
    ON r.runUID = sda.runUID
INNER JOIN
    {{ ref("sparkdserunaccountenhancedinsight_v") }} sdaei
    ON sda.runAccountId = sdaei.runAccountId
WHERE
    r.startDateLocal >= DATE_ADD('month', -1, current_date)
GROUP BY
    sdaei.accountenhancedinsightreferenceid,
    sda.accountId
