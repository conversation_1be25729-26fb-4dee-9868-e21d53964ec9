{{ config(materialized='table') }}

with msrscenario_sales_interaction_offset as
(
select msrscenariouid, sales_interaction_offset_months from {{ ref('param_msrscenario_sales_interaction_offset_v') }}
)
select ofst.msrscenariouid, accountuid, interaction_date,
  date_add('month', ofst.sales_interaction_offset_months, interaction_date) as sales_interaction_match_date,
  repuid, min(repname) as repname,  
sum(coalesce(visit_interaction_count,0)) as visit_interaction_count, 
sum(coalesce(remote_interaction_count,0)) as remote_interaction_count, 
sum(coalesce(email_interaction_count,0)) as email_interaction_count, 
sum(coalesce(open_count,0)) as open_count, 
sum(coalesce(click_count,0)) as click_count,
sum(coalesce(total_suggestion_count,0)) as total_suggestion_count,
sum(coalesce(total_unique_suggestion_count,0)) as total_unique_suggestion_count,
sum(coalesce(total_suggestion_accept_completed,0)) as total_suggestion_accept_completed,
sum(coalesce(total_suggestion_accept_incomplete,0)) as total_suggestion_accept_incomplete,
sum(coalesce(total_suggestion_dismissed,0)) as total_suggestion_dismissed,
sum(coalesce(total_suggestion_inferred_accepted,0)) as total_suggestion_inferred_accepted,
sum(coalesce(total_suggestion_direct_accepted,0)) as total_suggestion_direct_accepted
from {{ ref('imp_all_monthly_interactions_suggestions') }} cross join msrscenario_sales_interaction_offset ofst
group by  ofst.msrscenariouid, accountuid, interaction_date, date_add('month', ofst.sales_interaction_offset_months, interaction_date), repuid
