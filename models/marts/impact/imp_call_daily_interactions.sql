{{ config(materialized='view') }}

select 
rt.name  as channel,
cvc.ownerid as repuid,
UPPER(arl.repname) as repname,
cvc.call_date_vod__c as interaction_date,
cvc.territory_vod__c,
cvc.account_vod__c ,
COUNT(cvc.id) as interaction_count
from {{ ref('call2_vod__c_v') }} cvc 
join {{ ref('recordtype_v') }} rt on rt.id = cvc.recordtypeid 
join {{ ref('actor_v') }} arl on cvc.ownerid = arl.externalid  
where  cvc.status_vod__c = 'Submitted_vod'
group by rt.name, cvc.ownerid, arl.repname, cvc.call_date_vod__c, cvc.territory_vod__c , account_vod__c;

