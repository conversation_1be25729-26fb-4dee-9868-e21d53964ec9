{{ config(materialized='view') }}

-- depends_on: {{ ref('repaccountassignment_cdc_v') }}
-- depends_on: {{ ref('param_msrscenario_repaccountassignment_asof_v') }}

{{ build_asofdate_snapshot_sql ('repaccountassignment_cdc_v','msrscenariouid,repid,accountid','param_msrscenario_repaccountassignment_asof_v', 'cast(updatedatyear as int) DESC, cast(updatedatmonth as int) DESC, cast(updatedatday as int) DESC', " cast(updatedatyear as int) <= as_of_year and cast(updatedatmonth as int) <= as_of_month and cast(updatedatday as int) <= as_of_day and cast(updatedatyear as int) >= begin_year and cast(updatedatmonth as int) >= begin_month and cast(updatedatday as int) >= begin_day")  }}
