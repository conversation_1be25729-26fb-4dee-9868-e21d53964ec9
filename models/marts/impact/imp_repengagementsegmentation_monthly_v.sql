{{ config(materialized='view') }}

with segmentation_ranked as (
  SELECT
    *,
    ROW_NUMBER() OVER(PARTITION BY repuid, periodvalue  ORDER BY engagementrate desc ) AS row_number
  FROM {{ ref('rpt_rep_engagement_segmentation_v') }}
),
eng_bucket_metrics as
(select periodvalue, engagementsegment, avg(engagementrate) avg_bucket_engagementrate, min(engagementrate) min_bucket_engagementrate, max(engagementrate) max_bucket_engagementrate, approx_percentile(engagementrate, 0.5) median_bucket_engagementrate 
    from segmentation_ranked
    where row_number = 1
 group by periodvalue, engagementsegment
) 
SELECT b.*, 
 round(ebm.avg_bucket_engagementrate,2) as avg_bucket_engagementrate, round(median_bucket_engagementrate,2) as median_bucket_engagementrate,
 round(ebm.min_bucket_engagementrate,2) as min_bucket_engagementrate, round(max_bucket_engagementrate,2) as max_bucket_engagementrate
from segmentation_ranked b
inner join eng_bucket_metrics ebm on ebm.engagementsegment = b.engagementsegment and ebm.periodvalue = b.periodvalue
WHERE row_number = 1;
