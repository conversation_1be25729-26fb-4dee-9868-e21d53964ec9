{{ config(materialized='table') }}

with msrscenario_sales_interaction_offset as
(
select msrscenariouid, sales_interaction_offset_months from {{ ref('param_msrscenario_sales_interaction_offset_v') }}
)
select ofst.msrscenariouid, accountuid, productuid, min(productname) as productname, interaction_date,
  date_add('month', ofst.sales_interaction_offset_months, interaction_date) as sales_interaction_match_date,
  repuid, min(repname) as repname,  
max(coalesce(visit_interaction_count,0)) as visit_interaction_count, 
max(coalesce(remote_interaction_count,0)) as remote_interaction_count, 
max(coalesce(email_interaction_count,0)) as email_interaction_count, 
sum(coalesce(product_visit_interaction_count,0)) as product_visit_interaction_count, 
sum(coalesce(product_remote_interaction_count,0)) as product_remote_interaction_count, 
sum(coalesce(product_email_interaction_count,0)) as product_email_interaction_count, 
sum(coalesce(open_count,0)) as open_count, sum(click_count) as click_count,
sum(coalesce(product_suggestion_count,0)) as product_suggestion_count, 
sum(coalesce(product_unique_suggestion_count,0)) as product_unique_suggestion_count,
sum(coalesce(product_suggestion_accept_completed,0)) as product_suggestion_accept_completed, 
sum(coalesce(product_suggestion_accept_incomplete,0)) as product_suggestion_accept_incomplete,
sum(coalesce(product_suggestion_dismissed,0)) as product_suggestion_dismissed,
sum(coalesce(product_suggestion_inferred_accepted,0)) as product_suggestion_inferred_accepted,
sum(coalesce(product_suggestion_direct_accepted,0)) as product_suggestion_direct_accepted,
max(coalesce(total_suggestion_count,0)) as total_suggestion_count,
max(coalesce(total_unique_suggestion_count,0)) as total_unique_suggestion_count,
max(coalesce(total_suggestion_accept_completed,0)) as total_suggestion_accept_completed,
max(coalesce(total_suggestion_accept_incomplete,0)) as total_suggestion_accept_incomplete,
max(coalesce(total_suggestion_dismissed,0)) as total_suggestion_dismissed,
max(coalesce(total_suggestion_inferred_accepted,0)) as total_suggestion_inferred_accepted,
max(coalesce(total_suggestion_direct_accepted,0)) as total_suggestion_direct_accepted
from {{ ref('imp_all_monthly_interactions_suggestions_dtl') }} cross join msrscenario_sales_interaction_offset ofst
group by  ofst.msrscenariouid, accountuid, productuid, interaction_date, date_add('month', ofst.sales_interaction_offset_months, interaction_date), repuid
