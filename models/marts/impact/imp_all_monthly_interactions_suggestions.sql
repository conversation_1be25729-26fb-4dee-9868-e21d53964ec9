{{ config(materialized='table') }}

with all_dims as
(
    select accountuid, repuid, UPP<PERSON>(repname) repname, interaction_date
    from {{ ref('imp_all_monthly_interactions') }}
    union
    select accountuid, repuid, UPPER(repname) repname, startdatelocal as interaction_date
    from {{ ref('suggestion_delv_monthly_count') }} 
),
all_unique_dims as
(
    select accountuid, repuid, min(repname) as repname, interaction_date
    from all_dims
    group by accountuid, repuid, interaction_date
),
interaction_with_suggestion as 
(
   select a.accountuid, a.repuid, a.repname, a.interaction_date,
       b.visit_interaction_count, b.email_interaction_count, b.remote_interaction_count,
       b.open_count, b.click_count,       
       c.total_suggestion_count, c.total_unique_suggestion_count, 
       c.total_suggestion_accept_completed, c.total_suggestion_accept_incomplete,
       c.total_suggestion_dismissed, c.total_suggestion_inferred_accepted,
       c.total_suggestion_direct_accepted
from all_unique_dims a inner join {{ ref('imp_all_monthly_interactions') }} b on a.accountuid = b.accountuid
    and a.repuid = b.repuid and a.interaction_date = b.interaction_date
    inner join {{ ref('suggestion_delv_monthly_count') }} c on a.accountuid = c.accountuid 
        and a.repuid = c.repuid and a.interaction_date = c.startdatelocal
),
interaction_without_suggestion as
(
   select a.accountuid,  a.repuid, a.repname, a.interaction_date,
       b.visit_interaction_count, b.email_interaction_count, b.remote_interaction_count,
       b.open_count, b.click_count,       
    cast(null as bigint) total_suggestion_count,
    cast(null as bigint) total_unique_suggestion_count,
    cast(null as bigint) total_suggestion_accept_completed,
    cast(null as bigint) total_suggestion_accept_incomplete,
    cast(null as bigint) total_suggestion_dismissed,
    cast(null as bigint) total_suggestion_inferred_accepted,
    cast(null as bigint) total_suggestion_direct_accepted
from all_unique_dims a inner join {{ ref('imp_all_monthly_interactions') }} b on a.accountuid = b.accountuid
        and a.repuid = b.repuid and a.interaction_date = b.interaction_date
    left join {{ ref('suggestion_delv_monthly_count') }} c on a.accountuid = c.accountuid 
       and a.repuid = c.repuid and a.interaction_date = c.startdatelocal
    where c.accountuid is null
),
suggestion_without_interaction as
(
   select a.accountuid, a.repuid, a.repname, a.interaction_date,
       cast(null as bigint) as visit_interaction_count, 
       cast(null as bigint) as email_interaction_count, 
       cast(null as bigint) as remote_interaction_count, 
       cast(null as bigint) as open_count, 
       cast(null as bigint) as click_count, 
       c.total_suggestion_count, c.total_unique_suggestion_count, 
       c.total_suggestion_accept_completed, c.total_suggestion_accept_incomplete,
       c.total_suggestion_dismissed, c.total_suggestion_inferred_accepted,
       c.total_suggestion_direct_accepted
from all_unique_dims a 
    inner join {{ ref('suggestion_delv_monthly_count') }} c on a.accountuid = c.accountuid 
        and a.repuid = c.repuid and a.interaction_date = c.startdatelocal
    left join {{ ref('imp_all_monthly_interactions') }} b on a.accountuid = b.accountuid
        and a.repuid = b.repuid and a.interaction_date = b.interaction_date       
    where b.accountuid is null
)
select accountuid, repuid, repname, interaction_date,
visit_interaction_count, email_interaction_count, remote_interaction_count,
open_count, click_count,       
total_suggestion_count,
total_unique_suggestion_count,
total_suggestion_accept_completed,
total_suggestion_accept_incomplete,
total_suggestion_dismissed,
total_suggestion_inferred_accepted,
total_suggestion_direct_accepted
from interaction_with_suggestion
union 
select accountuid, repuid, repname, interaction_date,
visit_interaction_count, email_interaction_count, remote_interaction_count,
open_count, click_count,       
total_suggestion_count,
total_unique_suggestion_count,
total_suggestion_accept_completed,
total_suggestion_accept_incomplete,
total_suggestion_dismissed,
total_suggestion_inferred_accepted,
total_suggestion_direct_accepted
from interaction_without_suggestion
union 
select accountuid, repuid, repname, interaction_date,
visit_interaction_count, email_interaction_count, remote_interaction_count,
open_count, click_count,       
total_suggestion_count,
total_unique_suggestion_count,
total_suggestion_accept_completed,
total_suggestion_accept_incomplete,
total_suggestion_dismissed,
total_suggestion_inferred_accepted,
total_suggestion_direct_accepted
from suggestion_without_interaction



