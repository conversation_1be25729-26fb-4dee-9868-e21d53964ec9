 -- depends_on: {{ ref('actor_v') }}
 -- depends_on: {{ ref('user_v') }}

{{ config(materialized='view') }}

with all_user as (

{% if not build_check_is_veeva_customer() %}
select a.externalid as userid, profile_name_vod__c, companyname, division, department,  country, id, a.repname as username, lastname, firstname, name, title, email, userroleid, localesidkey, usertype,languagelocalekey, lastlogindate, createddate, a.configcountrycode as country_code_vod__c, user_type_vod__c, backupcreateddate, backupmodifieddate, functional_role_rpt, active_rpt, campaign_member_rpt, bu_grouping_rpt, training_completed_rpt, a.isactivated as isactive
FROM  
 {{ ref('actor_v') }} a left join {{ ref('user_v') }} b on a.externalid = b.id 
{%- else %}
SELECT id as userid, profile_name_vod__c, companyname, division, department, country, id, username, lastname, firstname, name, title, email, userroleid, localesidkey, usertype,languagelocalekey, lastlogindate, createddate,  country_code_vod__c, user_type_vod__c, backupcreateddate, backupmodifieddate, functional_role_rpt, active_rpt, campaign_member_rpt, bu_grouping_rpt, training_completed_rpt, isactive 
FROM  
 {{ ref('user_v') }}
{%- endif %}
),
pilot_period_calls as (
  select p.msrscenariouid, repuid, sum(interaction_count) pilot_call_activity_count, avg(interaction_count) pilot_call_activity_avg_monthly_count from 
  {{ ref('imp_call_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end 
  group by p.msrscenariouid, repuid
),pilot_territory_count as (
  select p.msrscenariouid, repuid, count(distinct repuid) pilot_period_territory_count from 
  {{ ref('imp_call_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end 
  group by p.msrscenariouid, repuid
),pilot_period_emails as (
  select p.msrscenariouid, repuid, sum(interaction_count) pilot_email_activity_count, avg(interaction_count) pilot_email_activity_avg_monthly_count from 
  {{ ref('imp_email_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end 
  group by p.msrscenariouid, repuid
), before_pilot_period_calls as (
  select p.msrscenariouid, repuid, sum(interaction_count) before_pilot_call_activity_count from 
  {{ ref('imp_call_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Before Pilot' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end 
  group by p.msrscenariouid, repuid
),before_pilot_period_emails as (
  select  p.msrscenariouid, repuid, sum(interaction_count) before_pilot_email_activity_count from
  {{ ref('imp_email_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Before Pilot' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end
  group by p.msrscenariouid, repuid
), lastyear_pilot_period_calls as (
  select  p.msrscenariouid, repuid, sum(interaction_count) lastyear_pilot_call_activity_count from
  {{ ref('imp_call_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'LY Pilot Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end
  group by p.msrscenariouid, repuid
),lastyear_pilot_period_emails as (
  select  p.msrscenariouid, repuid, sum(interaction_count) lastyear_pilot_email_activity_count from
  {{ ref('imp_email_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'LY Pilot Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end
  group by p.msrscenariouid, repuid
), analysis_period_calls as (
  select  p.msrscenariouid, repuid, sum(interaction_count) analysis_call_activity_count from
  {{ ref('imp_call_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Analysis Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end
  group by p.msrscenariouid, repuid
),analysis_period_emails as (
  select p.msrscenariouid, repuid, sum(interaction_count) analysis_email_activity_count from
  {{ ref('imp_email_monthly_interactions') }} i inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Analysis Period' and
        i.interaction_date >= p.dt_begin and i.interaction_date <= p.dt_end
  group by p.msrscenariouid, repuid
), all_user_view as (
select a.*, z.msrscenariouid, 
        p.pilot_call_activity_count, p.pilot_call_activity_avg_monthly_count, pp.before_pilot_call_activity_count, l.lastyear_pilot_call_activity_count,
        q.pilot_email_activity_count, q.pilot_email_activity_avg_monthly_count, qq.before_pilot_email_activity_count,ll.lastyear_pilot_email_activity_count,
        n.analysis_call_activity_count, nn.analysis_email_activity_count,
        t.pilot_period_territory_count,
        case when rr.repuid is not null then cast(1 as boolean) else cast(0 as boolean) end is_aktana_user,
        firstdate as suggestion_firstdate, lastdate as suggestion_lastdate, active_month_count as suggestion_active_month_count, 
         nbrdays as suggestion_nbrdays, suggestion_avg_monthly_count,
        suggestion_count, config_count as suggestion_config_count, seconfigid, seconfigname, repteamid,
        eng.engagementsegment_median as engagementsegment_median, a.isactive as is_user_active
from all_user a
     cross join {{ ref('param_msrscenario_events_v') }} z 
     left join {{ ref('imp_repsummary_v') }} rr on a.userid = rr.repuid
     left join pilot_period_calls p on z.msrscenariouid = p.msrscenariouid and a.userid = p.repuid
     left join before_pilot_period_calls pp on z.msrscenariouid = pp.msrscenariouid and a.userid = pp.repuid
     left join lastyear_pilot_period_calls l on z.msrscenariouid = l.msrscenariouid and a.userid = l.repuid
     left join analysis_period_calls n on z.msrscenariouid = l.msrscenariouid and a.userid = n.repuid
     left join pilot_period_emails q on z.msrscenariouid = q.msrscenariouid and a.userid = q.repuid
     left join before_pilot_period_emails qq on z.msrscenariouid = qq.msrscenariouid and a.userid = qq.repuid
     left join lastyear_pilot_period_emails ll on z.msrscenariouid = ll.msrscenariouid and a.userid = ll.repuid
     left join analysis_period_emails nn on z.msrscenariouid = nn.msrscenariouid and a.userid = nn.repuid
     left join pilot_territory_count t on z.msrscenariouid = t.msrscenariouid and a.userid = t.repuid
     left join {{ ref('imp_repengagementsegmentation_v') }} eng on a.userid = eng.repuid
) 
select *
      {{ build_rep_expr()  }} 
from 
all_user_view usr;

