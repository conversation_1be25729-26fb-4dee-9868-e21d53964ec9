{{ config(materialized='view') }}

with rep_suggestion_by_yearmonth as (
SELECT repuid, max(repname) as repname, year(startdatelocal) year, month(startdatelocal) month, min(startdatelocal) mindate, max(startdatelocal) maxdate, count(distinct startdatelocal) nbrdays, count(concat(cast(year(startdatelocal) as VARCHAR), '-', cast(month(startdatelocal) as VARCHAR))) OVER (PARTITION BY repuid) active_month_count,  
    count(distinct seconfigid) config_count, count(distinct suggestionreferenceid) suggestion_monthly_count
FROM  
 {{ ref('suggestion_delv_v') }}
 group by repuid,  year(startdatelocal), month(startdatelocal)
 order by repuid,  year(startdatelocal), month(startdatelocal)
),
rep_suggestion_count as (
SELECT i.repuid, max(i.repname) as repname, count(distinct i.suggestionreferenceid) as suggestion_count, count(distinct i.seconfigid) config_count
FROM
 {{ ref('suggestion_delv_v') }} as i
 group by i.repuid
),
rep_suggestion_agg as (
  select repuid, max(repname) as repname, min(mindate) firstdate, max(maxdate) lastdate, max(active_month_count) active_month_count, sum(nbrdays) as nbrdays, avg(suggestion_monthly_count) as suggestion_avg_monthly_count
  from rep_suggestion_by_yearmonth  
  group by repuid
),
rep_config_repteam as (
  select *, ROW_NUMBER() OVER(PARTITION BY repuid ORDER BY startdatelocal desc) AS row_nos FROM {{ ref('suggestion_delv_v') }}
), rep_config_repteam_sel as (
  select repuid, min(seconfigid) seconfigid, min(seconfigname) seconfigname, min(repteamid) repteamid from rep_config_repteam where row_nos = 1
  group by repuid
)
select a.repuid, a.repname, a.firstdate, a.lastdate, a.active_month_count, a.nbrdays, b.suggestion_count, b.config_count, 
        c.seconfigid, c.seconfigname, c.repteamid, a.suggestion_avg_monthly_count
from rep_suggestion_agg a inner join rep_suggestion_count b on a.repuid = b.repuid
     left join rep_config_repteam_sel as c on a.repuid = c.repuid;

