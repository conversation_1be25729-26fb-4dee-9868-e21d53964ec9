{{ config(materialized='table') }}

with interaction_sum as 
(
select accountuid, date_trunc('month', interaction_date) as interaction_date, call_channel_category,
channel, repuid, count(distinct interactionuid) interaction_count
from 
{{ ref('interactions_call_daily_v') }}
group by accountuid, date_trunc('month', interaction_date), channel, call_channel_category, repuid
),
product_sum as 
(
select accountuid, productuid, min(productname) as productname,  date_trunc('month', interaction_date) as interaction_date, channel,
       call_channel_category, repuid, min(repname) as repname, sum(product_interaction_count) product_interaction_count
from
{{ ref('interactions_call_daily_v') }}
group by accountuid, productuid, date_trunc('month', interaction_date), channel, call_channel_category, repuid
)
select a.accountuid, a.productuid,  a.productname,  a.interaction_date, a.channel, a.call_channel_category,
       a.repuid, a.repname,  b.interaction_count, a.product_interaction_count
from
product_sum a inner join interaction_sum b on a.accountuid = b.accountuid and a.interaction_date = b.interaction_date and a.channel = b.channel and 
a.repuid = b.repuid and a.call_channel_category = b.call_channel_category