{{ config(materialized='table') }}

with all_interactions as
(
select call_channel_category as channel_source, accountuid, productuid, productname, interaction_date, repuid, repname,  interaction_count, product_interaction_count, cast(0 as integer) as open_count,
       cast(0 as integer) as click_count 
from {{ ref('imp_call_monthly_interactions_dtl') }} 
union
select 'email' as channel_source, accountuid, productuid, productname, interaction_date, repuid, repname,  interaction_count, product_interaction_count, open_count,
       click_count 
from  {{ ref('imp_email_monthly_interactions_dtl') }} 
)
select accountuid, productuid, min(productname) as productname, interaction_date, 
       repuid, min(repname) as repname, 
       sum(case when lower(channel_source) = 'visit' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as visit_interaction_count, 
       sum(case when lower(channel_source) = 'email' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as email_interaction_count,               
       sum(case when lower(channel_source) = 'remote' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as remote_interaction_count, 
       sum(case when lower(channel_source) = 'visit' then coalesce(product_interaction_count,0)*1 else coalesce(product_interaction_count,0)*0 end) 
       as product_visit_interaction_count, 
       sum(case when lower(channel_source) = 'email' then coalesce(product_interaction_count,0)*1 else coalesce(product_interaction_count,0)*0 end) 
       as product_email_interaction_count,               
       sum(case when lower(channel_source) = 'remote' then coalesce(product_interaction_count,0)*1 else coalesce(product_interaction_count,0)*0 end) 
       as product_remote_interaction_count,               
       sum(coalesce(open_count,0)) as open_count, sum(coalesce(click_count,0)) as click_count
from all_interactions
group by  accountuid, productuid, interaction_date, repuid; 

