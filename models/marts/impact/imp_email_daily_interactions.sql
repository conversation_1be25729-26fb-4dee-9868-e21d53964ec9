{{ config(materialized='view') }}

select 
'AE'  as channel,
s.ownerid as repuid,
UPPER(arl.repname) as repname,
s.email_sent_date_vod__c as interaction_date,
cast('NA' as varchar) as territory_vod__c,
s.account_vod__c,
count(s.id) as interaction_count,
sum(s.opened_vod__c) as open_count,
sum(s.click_count_vod__c) as click_count
from {{ ref('sent_email_vod__c_v') }} s 
-- join {{ ref('approved_document_vod__c_v') }} a on a.id = s.approved_email_template_vod__c
join {{ ref('actor_v') }} arl on s.ownerid = arl.externalid  
group by s.ownerid, arl.repname, s.email_sent_date_vod__c, s.account_vod__c;
