{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with account_product_rep_date_scope as 
(
  select a.msrscenariouid, a.accountuid, ad.accountid, a.productuid, p.productid, a.repuid, a.sales_interaction_match_date from 
       {{ ref('msrscenario_all_monthly_interactions_dtl') }} a
       left join {{ ref('account_dse_v') }} ad on a.accountuid = ad.externalid
       left join {{ ref('product_v') }} p on a.productuid = p.externalid
       where a.msrscenariouid = 'default' and a.productuid is not null
  union 
  select s.msrscenariouid, tgt.accountuid, ad.accountid, tgt.productuid, p.productid, tgt.repuid, tgt.periodvalue as sales_interaction_match_date
  from {{ ref('cpa_repaccountproduct_monthly')}} tgt
        cross join {{ ref('param_msrscenario_v') }} s
        left join {{ ref('account_dse_v') }} ad on tgt.accountuid = ad.externalid
        left join {{ ref('product_v') }} p on tgt.productuid = p.externalid
       where s.msrscenariouid = 'default'
  union
  select raa.msrscenariouid, raa.accountuid, ad.accountid, p.externalid as productuid, p.productid, raa.repuid, date_trunc('month', m.dt) as sales_interaction_match_date
  from {{ ref('msrscenario_repaccountassignment_v') }} raa 
        inner join {{ ref('repproductauthorization_v') }} rap on raa.repid = rap.repid and raa.repaccount_istarget_rpt = 1
        inner join {{ ref('param_msrscenario_period_definition_v')}} prd on raa.msrscenariouid = prd.msrscenariouid and prd.period_type = 'Analysis Period'
        left join {{ ref('account_dse_v') }} ad on raa.accountuid = ad.externalid
        left join {{ ref('product_v') }} p on rap.productid = p.productid and cast(p.iscompetitor as boolean) = false
{%- if var_multi_country == 'true' %} and p.configcountrycode = ad.configcountrycode {%- endif %}
        join {{ ref ('as_of_date') }} m on m.dt >= prd.computed_begin_dt and m.dt <= prd.computed_end_dt
       where raa.msrscenariouid = 'default'
   group by raa.msrscenariouid, raa.accountuid, ad.accountid, p.externalid, p.productid, raa.repuid, date_trunc('month', m.dt)     
), 
accountproduct_history as (
   select a.msrscenariouid, ad.externalid as accountuid, p.externalid as productuid, 
          a.as_of_year, a.as_of_month, map_agg(a.field_name, a.month_value) as accountproduct_value_map
  from {{ ref('msrscenario_accountproduct_history') }} a 
   inner join {{ ref('account_dse_v') }} ad on a.accountid = ad.accountid
   inner join {{ ref('product_v') }} p on a.productid = p.productid 
   group by a.msrscenariouid, ad.externalid, p.externalid, a.as_of_year, a.as_of_month  
),
account_history as (
   select a.msrscenariouid, ad.externalid as accountuid, 
          a.as_of_year, a.as_of_month,
          map_agg(a.field_name, a.month_value) as account_value_map
   from {{ ref('msrscenario_account_dse_history') }} a 
   inner join {{ ref('account_dse_v') }} ad on a.accountid = ad.accountid
   group by a.msrscenariouid, ad.externalid, ad.configcountrycode, a.as_of_year, a.as_of_month  
),
country_map as (
   select ad.externalid as accountuid, ad.configcountrycode as account_country_code
   from {{ ref('account_dse_v') }} ad
),
data_without_sales as
(
    select a.msrscenariouid, cast(null as varchar) indication, cast(null as varchar) diagnosis_group, cast(null as varchar) product_group,
       a.accountuid, a.productuid, a.accountid, a.productid, p.productname,
       cast(null as varchar) account_ref_id, cast(null as varchar) product_ref_id, a.sales_interaction_match_date as sale_date, a.repuid,
       (cast(null as double)) as sales_value_trx, 
       (cast(null as double)) as sales_value_nrx, 
       (cast(null as double)) as sales_value_nbrx, 
       (cast(null as double)) as sales_value_unit, 
       (cast(null as double)) as sales_value_revenue, 
       b.alignmentcount,
       b.reptargetcount,
       b.repaccount_istarget_rpt,
       case when b.alignmentcount is not null then 1 else 0 end as repaccount_aligned_rpt,
       b.account_dim1_rpt,
       b.account_dim2_rpt,
       intr.visit_interaction_count,
       intr.remote_interaction_count,
       intr.email_interaction_count,
       intr.product_visit_interaction_count,
       intr.product_remote_interaction_count,
       intr.product_email_interaction_count,
       intr.open_count,
       intr.click_count,
       intr.product_suggestion_count,
       intr.product_unique_suggestion_count,
       intr.product_suggestion_accept_completed,
       intr.product_suggestion_accept_incomplete,
       intr.product_suggestion_dismissed,
       intr.product_suggestion_inferred_accepted,
       intr.product_suggestion_direct_accepted,
       intr.total_suggestion_count,
       intr.total_unique_suggestion_count,
       intr.total_suggestion_accept_completed,
       intr.total_suggestion_accept_incomplete,
       intr.total_suggestion_dismissed,
       intr.total_suggestion_inferred_accepted,
       intr.total_suggestion_direct_accepted,
       usr.pilotcontrol_rep,
       usr.repteamid,
       x.repteamname,
       aat.repteamid as m_repteamid,
       xx.repteamname as m_repteamname,
       eng.engagementsegment_median as engagementsegment,
       engm.engagementsegment as engagementsegment_monthly,
       engq.engagementsegment_median as engagementsegment_quarterly,
       engm.engagementrate as engagementrate_monthly,
       usr.is_user_active,
       y.accountproduct_segment_rpt,
       y.accountproduct_tier_rpt,
       y.accountproduct_dim1_rpt,
       y.accountproduct_dim2_rpt,
       aph.accountproduct_value_map['accountproduct_segment_rpt'] as m_accountproduct_segment_rpt,
       aph.accountproduct_value_map['accountproduct_tier_rpt'] as m_accountproduct_tier_rpt,
       aph.accountproduct_value_map['accountproduct_dim1_rpt'] as m_accountproduct_dim1_rpt,
       aph.accountproduct_value_map['accountproduct_dim2_rpt'] as m_accountproduct_dim2_rpt,
       ah.account_value_map['account_dim1_rpt'] as m_account_dim1_rpt,
       ah.account_value_map['account_dim2_rpt'] as m_account_dim2_rpt,
       cm.account_country_code,
       case when cpa_rap.accountuid is not null then 1 else 0 end as repaccountproduct_target,
       case when cpa_ra.accountuid is not null then 1 else 0 end as repaccount_target,
       case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end as targetsperiodid,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanstartdate else cpa_ra.callplanstartdate end as callplanstartdate,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanenddate else cpa_ra.callplanenddate end as callplanenddate,
       coalesce(cpa_rap.visit_target, cpa_ra.visit_target) as visit_target,
       coalesce(cpa_rap.email_target, cpa_ra.email_target) as email_target,
       coalesce(cpa_rap.remote_target, cpa_ra.remote_target) as remote_target,
       coalesce(cpa_rap.visit_proratedtarget, cpa_ra.visit_proratedtarget) as visit_proratedtarget,
       coalesce(cpa_rap.email_proratedtarget, cpa_ra.email_proratedtarget) as email_proratedtarget,
       coalesce(cpa_rap.remote_proratedtarget, cpa_ra.remote_proratedtarget) as remote_proratedtarget,
       coalesce(cpa_rap.cpa_visit_action, cpa_ra.cpa_visit_action) as cpa_visit_action_count,
       coalesce(cpa_rap.cpa_email_action, cpa_ra.cpa_email_action) as cpa_email_action_count,
       coalesce(cpa_rap.cpa_remote_action, cpa_ra.cpa_remote_action) as cpa_remote_action_count,
       coalesce(cpa_rap.cpa_visit_proratedaction, cpa_ra.cpa_visit_proratedaction) as cpa_visit_proratedaction_count,
       coalesce(cpa_rap.cpa_email_proratedaction, cpa_ra.cpa_email_proratedaction) as cpa_email_proratedaction_count,
       coalesce(cpa_rap.cpa_remote_proratedaction, cpa_ra.cpa_remote_proratedaction) as cpa_remote_proratedaction_count,
       sum(intr.visit_interaction_count) over (partition by case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end order by a.sales_interaction_match_date) as targetsperiod_cum_visit_interaction_count,
       sum(intr.remote_interaction_count) over (partition by case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end order by a.sales_interaction_match_date) as targetsperiod_cum_remote_interaction_count,
       sum(intr.email_interaction_count) over (partition by case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end order by a.sales_interaction_match_date) as targetsperiod_cum_email_interaction_count,
       case when b.msrscenariouid is not null then 1 else 0 end as is_inalignment,
       case when intr.msrscenariouid is not null then 1 else 0 end as is_ininteraction,
       case when cpa_rap.accountuid is not null then 1 else 0 end as is_incpa_repacctprd,
       wdays.working_days
       from account_product_rep_date_scope a
       left join {{ ref('msrscenario_repaccountassignment_v') }} b on a.msrscenariouid = b.msrscenariouid and a.accountuid = b.accountuid and a.repuid = b.repuid
       join {{ ref('msrscenario_usersummary_v')  }} usr on  a.msrscenariouid = usr.msrscenariouid and usr.userid = a.repuid
       join {{ ref('param_msrscenario_period_definition_v')}} prd on a.msrscenariouid = prd.msrscenariouid and a.msrscenariouid = 'default' and prd.period_type = 'Analysis Period' and a.sales_interaction_match_date >= prd.computed_begin_dt and a.sales_interaction_match_date <= prd.computed_end_dt
       left join {{ ref('msrscenario_all_monthly_interactions_dtl') }} intr on a.msrscenariouid = intr.msrscenariouid and a.accountuid = intr.accountuid and a.productuid = intr.productuid and a.repuid = intr.repuid and a.sales_interaction_match_date = intr.sales_interaction_match_date
       left join {{ ref('imp_repengagementsegmentation_v') }} eng on a.repuid = eng.repuid 
       left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on a.repuid = engm.repuid and a.sales_interaction_match_date = engm.periodvalue
       left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on a.repuid = engq.repuid and date_trunc('quarter', a.sales_interaction_match_date) = engq.periodvalue
       left join {{ ref('actorteam_v') }} x on x.repteamid  = usr.repteamid
       left join {{ ref('msrscenario_accountproduct') }} y on a.msrscenariouid = y.msrscenariouid and a.accountuid = y.accountuid and a.productuid = y.productuid       
       left join accountproduct_history aph on a.msrscenariouid = aph.msrscenariouid and a.accountuid = aph.accountuid and a.productuid = aph.productuid and  year(a.sales_interaction_match_date) = aph.as_of_year and month(a.sales_interaction_match_date) = aph.as_of_month
       left join account_history ah on a.msrscenariouid = ah.msrscenariouid and a.accountuid = ah.accountuid and  year(a.sales_interaction_match_date) = ah.as_of_year and month(a.sales_interaction_match_date) = ah.as_of_month
       left join {{ ref('actor_actorteam_engine_monthly') }} aat on a.repuid = aat.repuid and a.sales_interaction_match_date = aat.startdatelocal
       left join {{ ref('actorteam_v') }} xx on xx.repteamid  = aat.repteamid
       left join country_map cm on a.accountuid = cm.accountuid
       left join {{ ref('cpa_repaccount_monthly')}} cpa_ra on a.accountuid = cpa_ra.accountuid and a.repuid = cpa_ra.repuid and cast(a.sales_interaction_match_date as date) = cpa_ra.periodvalue
       left join {{ ref('cpa_repaccountproduct_monthly')}} cpa_rap on a.accountuid = cpa_rap.accountuid and a.repuid = cpa_rap.repuid and cast(a.sales_interaction_match_date as date) = cpa_rap.periodvalue and a.productuid = cpa_rap.productuid
       left join {{ ref('product_v') }} p on a.productuid = p.externalid       
       left join {{ ref('rep_month_workingdays') }} wdays on wdays.repuid = a.repuid and 
{%- if var_multi_country == 'false' %}
      wdays.country = '{{ build_country() }}' 
{%- else %}
      wdays.country = cm.account_country_code
{%- endif %}
      and wdays.dt_month = a.sales_interaction_match_date 
)
select msrscenariouid, '{{ build_company() }}' as company, 
{%- if var_multi_country == 'false' %}
      '{{ build_country() }}' as country, 
{%- else %}
      account_country_code as country,
{%- endif %}
       accountuid, productuid, accountid, productid, productname,
       sale_date, repuid,
       alignmentcount, reptargetcount, repaccount_istarget_rpt, account_dim1_rpt, account_dim2_rpt, 
       product_visit_interaction_count, product_remote_interaction_count, product_email_interaction_count, open_count, click_count,
       product_suggestion_count,product_unique_suggestion_count,product_suggestion_accept_completed,
       product_suggestion_accept_incomplete,product_suggestion_dismissed,product_suggestion_inferred_accepted,
       product_suggestion_direct_accepted,
       pilotcontrol_rep,
       repteamid, repteamname, engagementsegment, engagementsegment_monthly,
       engagementsegment_quarterly, engagementrate_monthly, is_user_active,
       accountproduct_segment_rpt, accountproduct_tier_rpt,accountproduct_dim1_rpt,accountproduct_dim2_rpt,
       m_accountproduct_segment_rpt, m_accountproduct_tier_rpt, m_accountproduct_dim1_rpt, m_accountproduct_dim2_rpt, 
       m_account_dim1_rpt,m_account_dim2_rpt, m_repteamid, m_repteamname,
       repaccount_aligned_rpt, repaccountproduct_target,repaccount_target,targetsperiodid, callplanstartdate, callplanenddate,
       visit_target, email_target, remote_target,
       visit_proratedtarget, email_proratedtarget, remote_proratedtarget,
       cpa_visit_action_count, cpa_email_action_count, cpa_remote_action_count,
       cpa_visit_proratedaction_count, cpa_email_proratedaction_count, cpa_remote_proratedaction_count,
       is_inalignment, is_ininteraction, is_incpa_repacctprd, working_days
from
data_without_sales;
