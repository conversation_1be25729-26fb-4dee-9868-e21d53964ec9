{{ config(materialized='table') }}

select accountuid, productuid, min(productname) as productname, date_trunc('month', interaction_date) as interaction_date,
channel, repuid, min(repname) as repname,  sum(interaction_count) as interaction_count, sum(product_interaction_count) product_interaction_count,
sum(open_count) as open_count, sum(click_count) as click_count
from
{{ ref('interactions_email_daily_v') }}
group by accountuid, productuid, date_trunc('month', interaction_date), channel, repuid