{{ config(materialized='view') }}

with total_suggestions as (
    select  msrscenariouid, count(*) total_suggestion_count 
    from {{ ref('suggestion_factor_v') }} join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and startdatelocal >= p.dt_begin and startdatelocal <= p.dt_end
    group by msrscenariouid
)
select p.msrscenariouid, coalesce(tagname,'Channel Execution') usecasename, count(*) suggestion_count, avg(total_suggestion_count) as total_suggestion_count, round(count(*) * 100.0/avg(total_suggestion_count), 2) suggestion_pct
from {{ ref('suggestion_factor_v') }} join  {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and startdatelocal >= dt_begin and startdatelocal <= dt_end inner join total_suggestions t on t.msrscenariouid = p.msrscenariouid
group by p.msrscenariouid, tagname;

