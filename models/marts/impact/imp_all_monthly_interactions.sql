{{ config(materialized='table') }}

with all_interactions as
(
select call_channel_category as channel_source, accountuid,  interaction_date, repuid, repname,  interaction_count, cast(0 as integer) as open_count,
       cast(0 as integer) as click_count 
from {{ ref('imp_call_monthly_interactions') }} 
union
select 'email' as channel_source, accountuid,  interaction_date, repuid, repname,  interaction_count, open_count,
       click_count 
from  {{ ref('imp_email_monthly_interactions') }} 
)
select accountuid, interaction_date, 
       repuid, min(repname) as repname, 
       sum(case when lower(channel_source) = 'visit' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as visit_interaction_count, 
       sum(case when lower(channel_source) = 'email' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as email_interaction_count,               
       sum(case when lower(channel_source) = 'remote' then coalesce(interaction_count,0)*1 else coalesce(interaction_count,0)*0 end) 
       as remote_interaction_count, 
       sum(coalesce(open_count,0)) as open_count, sum(coalesce(click_count,0)) as click_count
from all_interactions
group by  accountuid, interaction_date, repuid; 

