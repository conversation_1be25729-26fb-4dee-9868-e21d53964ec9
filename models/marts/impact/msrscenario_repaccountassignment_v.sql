{{ config(materialized='view') }}

with repaccount_assignment_count as (
  select raa.msrscenariouid, a.externalid as accountuid, raa.accountid,
         count(x.externalid) as alignmentcount
  from {{ ref('msrscenario_repaccountassignment_util_with_target') }} raa
  join {{ ref('actor_v') }} x on x.repid =raa.repid
  join {{ ref('msrscenario_account_dse') }} a on raa.msrscenariouid = a.msrscenariouid and a.accountid =raa.accountid
  group by raa.msrscenariouid, a.externalid, raa.accountid
),
target_count as (
  select raa.msrscenariouid, x.externalid as repuid, raa.repid,
  sum(case when repaccount_istarget_rpt = 1 then 1 else 0 end) reptargetcount
  from {{ ref('msrscenario_repaccountassignment_util_with_target') }} raa
  join {{ ref('actor_v') }} x on x.repid = raa.repid
  group by raa.msrscenariouid, x.externalid, raa.repid
)
select 
'{{ var('customer') }}' as client,
'{{ var('customer')[-2:] }}' as country, current_date as analysisdate,
rac.alignmentcount, 
tac.reptargetcount,
a.externalid as accountuid,
x.externalid as repuid,
CONCAT(cast(YEAR(raa.createdat) as VARCHAR), '-', cast(MONTH(raa.createdat) as varchar)) AS yearmonthperiodvalue, 
a.account_dim1_rpt,
a.account_dim2_rpt,
raa.*
from {{ ref('msrscenario_repaccountassignment_util_with_target') }} raa 
join repaccount_assignment_count rac on raa.msrscenariouid = rac.msrscenariouid and raa.accountid = rac.accountid
join target_count tac on raa.msrscenariouid = tac.msrscenariouid and raa.repid = tac.repid
join {{ ref('actor_v') }} x on x.repid =raa.repid 
join {{ ref('msrscenario_account_dse') }} a on  raa.msrscenariouid = a.msrscenariouid and a.accountid =raa.accountid 
