{{ config(materialized='view') }}

with repaccount_assignment_count as (
  select a.externalid as accountuid,
         count(x.externalid) as alignmentcount
  from {{ ref('repaccountassignment_v') }} raa
  join {{ ref('actor_v') }} x on x.repid =raa.repid
  join {{ ref('account_dse_v') }} a on a.accountid =raa.accountid
  group by a.externalid
)
select 
'{{ var('customer') }}' as client,
'{{ var('customer')[-2:] }}' as country, current_date as analysisdate,
a.externalid as accountuid,
x.externalid as repuid,
CONCAT(cast(YEAR(raa.createdat) as VARCHAR), '-', cast(MONTH(raa.createdat) as varchar)) AS yearmonthperiodvalue ,
rac.alignmentcount,
raa.*  
from {{ ref('repaccountassignment_v') }} raa 
join {{ ref('actor_v') }} x on x.repid =raa.repid 
join {{ ref('account_dse_v') }} a on a.accountid =raa.accountid 
join repaccount_assignment_count rac on rac.accountuid = a.externalid;

