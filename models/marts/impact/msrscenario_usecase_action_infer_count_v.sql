{{ config(materialized='view') }}

with total_suggestions as (
    select  msrscenariouid, coalesce(tagname, 'Channel Execution') tagname, count(*) suggestion_count_bytag
    from {{ ref('suggestion_factor_v') }} join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and startdatelocal >= p.dt_begin and startdatelocal <= p.dt_end
    group by msrscenariouid, coalesce(tagname, 'Channel Execution') 
)
   select p.msrscenariouid, coalesce(a.tagname, 'Channel Execution') tagname, a.actiontaken_rpt, a.inferred_status,
   count(*) suggestion_count_bytagactioninfer, avg(t.suggestion_count_bytag) suggestion_count_bytag,
   round(count(*) * 100.0/avg(suggestion_count_bytag), 2) suggestion_pct_bytagactioninfer
   from {{ ref('suggestion_factor_v') }} a
        join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Pilot Period' and a.startdatelocal >= p.dt_begin and a.startdatelocal <= p.dt_end
        join total_suggestions t on  t.msrscenariouid = p.msrscenariouid and t.tagname = coalesce(a.tagname, 'Channel Execution') 
   group by p.msrscenariouid, coalesce(a.tagname, 'Channel Execution'), a.actiontaken_rpt, a.inferred_status;
