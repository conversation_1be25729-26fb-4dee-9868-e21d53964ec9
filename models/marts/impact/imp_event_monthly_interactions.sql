{{ config(materialized='view') }}

select accountuid, i.productuid as productuid, date_trunc('month', eventdatetimeutc) as event_date,
       array_agg(cast(Row(eventtypename, coalesce(usecasename,'UNKNOWN'), suggestiondriver) as Row(eventtypename varchar, usecasename varchar, suggestiondriver varchar))) as interaction_usecases,
       count(distinct mergeid) event_count
       from
       {{ ref('kpi_event_interaction') }} CROSS JOIN UNNEST(product_details) AS t(i)
       where eventtypename like '%-COMPLETED'
       group by accountuid, i.productuid, date_trunc('month', eventdatetimeutc)
