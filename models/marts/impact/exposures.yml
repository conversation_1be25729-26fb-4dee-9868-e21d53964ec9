version: 2

exposures:
  - name: exp_imp_reprepteamconfig_v
    label: Aktana Reps 
    description: “Reps Activated in Aktana with RepTeams and Configs”
    type: analysis
    maturity: high

    depends_on:
      - ref('imp_reprepteamconfig_v')
       
    owner:
      email: satya.dhan<PERSON><PERSON><PERSON>@aktana.com    

  - name: exp_imp_repengagementsegmentation_monthly_v
    label: Rep Engagement By Month  
    description: “Rep Engagement Segmentation Monthly View”
    type: analysis
    maturity: high

    depends_on:
      - ref('imp_repengagementsegmentation_monthly_v')

    owner:
      email: <EMAIL>

  - name: exp_imp_repengagementsegmentation_quarterly_v
    label: Rep Engagement By Quarter  
    description: “Rep Engagement Segmentation Quarterly View”
    type: analysis
    maturity: high

    depends_on:
      - ref('imp_repengagementsegmentation_quarterly_v')

    owner:
      email: <EMAIL>

  - name: exp_imp_repengagementsegmentation_v
    label: Rep Engagement Aggregate 
    description: “Rep Engagement Segmentation”
    type: analysis
    maturity: high

    depends_on:
      - ref('imp_repengagementsegmentation_monthly_v')

    owner:
      email: satya.dhan<PERSON><PERSON><PERSON>@aktana.com

  - name: msrscenario_suggdelv_daily_count_v
    label: Suggestion Delivered
    description: “Suggestions Delivered Daily Count”
    type: dashboard
    maturity: medium

    depends_on:
      - ref('suggestion_delv_factor_v')

    owner:
      email: <EMAIL>

  - name: msrscenario_usecase_mix_v
    label: Suggestion By Usecases
    description: “Suggestions By Usecase Count”
    type: dashboard
    maturity: medium

    depends_on:
      - ref('suggestion_factor_v')

    owner:
      email: <EMAIL>

  - name: msrscenario_usecase_action_count_v
    label: Suggestion By Usecase and Action
    description: “Suggestions By Usecase and Action Count”
    type: dashboard
    maturity: medium

    depends_on:
      - ref('suggestion_factor_v')

    owner:
      email: <EMAIL>

  - name: msrscenario_usecase_action_infer_count_v
    label: Suggestion By Usecase and Action, Infer
    description: “Suggestions By Usecase Action, Infer Count”
    type: dashboard
    maturity: medium

    depends_on:
      - ref('suggestion_factor_v')

    owner:
      email: <EMAIL>

  - name: msrscenario_usersummary_v
    label: User Summary with Features
    description: “User Summary with features”
    type: dashboard
    maturity: medium

    depends_on:
      - ref('user_v')

    owner:
      email: <EMAIL>

  - name: msrscenario_sales_indacctprdrep
    label: Scenario Sales by Rep with all features for analysis
    description: “Scenario Sales with features”
    type: analysis
    maturity: high

    depends_on:
      - ref('msrscenario_sales_monthly_indicationaccountproduct')
       
    owner:
      email: <EMAIL>    

  - name: msrscenario_repaccountassignment_v
    label: Rep Account Assignment by Scenario
    description: “Rep Account Assignment by Scenario”
    type: analysis
    maturity: high

    depends_on:
      - ref('msrscenario_repaccountassignment_util_v')
       
    owner:
      email: <EMAIL>    
