{{ config(materialized='table') }}

with ap_table as (
select aa.*,  a.externalid as accountuid, p.externalid as productuid  
from {{ ref('accountproduct_v') }} aa
inner join {{ ref('account_dse_v') }} a on a.accountid = aa.accountid 
inner join {{ ref('product_v') }} p on p.productid = aa.productid
)
select msrscenariouid, accountid, accountuid, productid, productuid {{ build_accountproduct_expr() }}
from {{ ref('param_msrscenario_accountproduct_fields_v') }} cross join ap_table; 
