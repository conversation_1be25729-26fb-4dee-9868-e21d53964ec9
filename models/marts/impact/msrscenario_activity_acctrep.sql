{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with account_product_rep_date_scope as 
(
  select a.msrscenariouid, a.accountuid, ad.accountid, a.repuid, a.sales_interaction_match_date from 
       {{ ref('msrscenario_all_monthly_interactions') }} a
       left join {{ ref('account_dse_v') }} ad on a.accountuid = ad.externalid
       where a.msrscenariouid = 'default'
       group by a.msrscenariouid, a.accountuid, ad.accountid, a.repuid, a.sales_interaction_match_date
  union 
  select s.msrscenariouid, tgt.accountuid, ad.accountid, tgt.repuid, tgt.periodvalue as sales_interaction_match_date
  from {{ ref('cpa_repaccount_monthly')}} tgt
        cross join {{ ref('param_msrscenario_v') }} s
        left join {{ ref('account_dse_v') }} ad on tgt.accountuid = ad.externalid
       where s.msrscenariouid = 'default'
  union
  select raa.msrscenariouid, raa.accountuid, ad.accountid, raa.repuid, date_trunc('month', m.dt) as sales_interaction_match_date
  from {{ ref('msrscenario_repaccountassignment_v') }} raa 
        inner join {{ ref('param_msrscenario_period_definition_v')}} prd on raa.msrscenariouid = prd.msrscenariouid and prd.period_type = 'Analysis Period' and raa.repaccount_istarget_rpt = 1
        left join {{ ref('account_dse_v') }} ad on raa.accountuid = ad.externalid
        join {{ ref ('as_of_date') }} m on m.dt >= prd.computed_begin_dt and m.dt <= prd.computed_end_dt
       where raa.msrscenariouid = 'default'
   group by raa.msrscenariouid, raa.accountuid, ad.accountid, raa.repuid, date_trunc('month', m.dt)     
), 
account_history as (
   select a.msrscenariouid, ad.externalid as accountuid, 
          a.as_of_year, a.as_of_month,
          map_agg(a.field_name, a.month_value) as account_value_map
   from {{ ref('msrscenario_account_dse_history') }} a 
   inner join {{ ref('account_dse_v') }} ad on a.accountid = ad.accountid
   group by a.msrscenariouid, ad.externalid, ad.configcountrycode, a.as_of_year, a.as_of_month  
),
country_map as (
   select ad.externalid as accountuid, ad.configcountrycode as account_country_code
   from {{ ref('account_dse_v') }} ad
), msrscenario_all_monthly_interactions as (
    select msrscenariouid, accountuid, sales_interaction_match_date,
  repuid, max(repname) as repname, 
sum(coalesce(visit_interaction_count,0)) as visit_interaction_count, 
sum(coalesce(remote_interaction_count,0)) as remote_interaction_count, 
sum(coalesce(email_interaction_count,0)) as email_interaction_count, 
sum(coalesce(open_count,0)) as open_count, sum(click_count) as click_count,
sum(coalesce(total_suggestion_count,0)) as total_suggestion_count,
sum(coalesce(total_unique_suggestion_count,0)) as total_unique_suggestion_count,
sum(coalesce(total_suggestion_accept_completed,0)) as total_suggestion_accept_completed,
sum(coalesce(total_suggestion_accept_incomplete,0)) as total_suggestion_accept_incomplete,
sum(coalesce(total_suggestion_dismissed,0)) as total_suggestion_dismissed,
sum(coalesce(total_suggestion_inferred_accepted,0)) as total_suggestion_inferred_accepted,
sum(coalesce(total_suggestion_direct_accepted,0)) as total_suggestion_direct_accepted
from {{ ref('msrscenario_all_monthly_interactions') }}
where msrscenariouid = 'default'
group by msrscenariouid, accountuid, sales_interaction_match_date, repuid
),
data_without_sales as
(
    select a.msrscenariouid, 
       a.accountuid,  a.accountid,
       a.sales_interaction_match_date as sale_date, a.repuid,
       b.alignmentcount,
       b.reptargetcount,
       b.repaccount_istarget_rpt,
       case when b.alignmentcount is not null then 1 else 0 end as repaccount_aligned_rpt,
       b.account_dim1_rpt,
       b.account_dim2_rpt,
       intr.visit_interaction_count,
       intr.remote_interaction_count,
       intr.email_interaction_count,
       intr.open_count,
       intr.click_count,
       intr.total_suggestion_count,
       intr.total_unique_suggestion_count,
       intr.total_suggestion_accept_completed,
       intr.total_suggestion_accept_incomplete,
       intr.total_suggestion_dismissed,
       intr.total_suggestion_inferred_accepted,
       intr.total_suggestion_direct_accepted,
       usr.pilotcontrol_rep,
       usr.repteamid,
       x.repteamname,
       aat.repteamid as m_repteamid,
       xx.repteamname as m_repteamname,
       eng.engagementsegment_median as engagementsegment,
       engm.engagementsegment as engagementsegment_monthly,
       engq.engagementsegment_median as engagementsegment_quarterly,
       engm.engagementrate as engagementrate_monthly,
       usr.is_user_active,
       ah.account_value_map['account_dim1_rpt'] as m_account_dim1_rpt,
       ah.account_value_map['account_dim2_rpt'] as m_account_dim2_rpt,
       cm.account_country_code,
       case when cpa_ra.accountuid is not null then 1 else 0 end as repaccount_target,
       case when cpa_ra.accountuid is not null then cpa_ra.targetsperiodid else cpa_ra.targetsperiodid end as targetsperiodid,
       case when cpa_ra.accountuid is not null then cpa_ra.callplanstartdate else cpa_ra.callplanstartdate end as callplanstartdate,
       case when cpa_ra.accountuid is not null then cpa_ra.callplanenddate else cpa_ra.callplanenddate end as callplanenddate,
       cpa_ra.visit_target as visit_target,
       cpa_ra.email_target as email_target,
       cpa_ra.remote_target as remote_target,
       cpa_ra.visit_proratedtarget as visit_proratedtarget,
       cpa_ra.email_proratedtarget as email_proratedtarget,
       cpa_ra.remote_proratedtarget as remote_proratedtarget,
       cpa_ra.cpa_visit_action as cpa_visit_action_count,
       cpa_ra.cpa_email_action as cpa_email_action_count,
       cpa_ra.cpa_remote_action as cpa_remote_action_count,
       cpa_ra.cpa_visit_proratedaction as cpa_visit_proratedaction_count,
       cpa_ra.cpa_email_proratedaction as cpa_email_proratedaction_count,
       cpa_ra.cpa_remote_proratedaction as cpa_remote_proratedaction_count,
       case when b.msrscenariouid is not null then 1 else 0 end as is_inalignment,
       case when intr.msrscenariouid is not null then 1 else 0 end as is_ininteraction,
       case when cpa_ra.accountuid is not null then 1 else 0 end as is_incpa_repacct,
       wdays.working_days
       from account_product_rep_date_scope a
       left join {{ ref('msrscenario_repaccountassignment_v') }} b on a.msrscenariouid = b.msrscenariouid and a.accountuid = b.accountuid and a.repuid = b.repuid
       join {{ ref('msrscenario_usersummary_v')  }} usr on  a.msrscenariouid = usr.msrscenariouid and usr.userid = a.repuid
       join {{ ref('param_msrscenario_period_definition_v')}} prd on a.msrscenariouid = prd.msrscenariouid and a.msrscenariouid = 'default' and prd.period_type = 'Analysis Period' and a.sales_interaction_match_date >= prd.computed_begin_dt and a.sales_interaction_match_date <= prd.computed_end_dt
       left join msrscenario_all_monthly_interactions intr on a.msrscenariouid = intr.msrscenariouid and a.accountuid = intr.accountuid and a.repuid = intr.repuid and a.sales_interaction_match_date = intr.sales_interaction_match_date
       left join {{ ref('imp_repengagementsegmentation_v') }} eng on a.repuid = eng.repuid 
       left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on a.repuid = engm.repuid and a.sales_interaction_match_date = engm.periodvalue
       left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on a.repuid = engq.repuid and date_trunc('quarter', a.sales_interaction_match_date) = engq.periodvalue
       left join {{ ref('actorteam_v') }} x on x.repteamid  = usr.repteamid
       left join account_history ah on a.msrscenariouid = ah.msrscenariouid and a.accountuid = ah.accountuid and  year(a.sales_interaction_match_date) = ah.as_of_year and month(a.sales_interaction_match_date) = ah.as_of_month
       left join {{ ref('actor_actorteam_engine_monthly') }} aat on a.repuid = aat.repuid and a.sales_interaction_match_date = aat.startdatelocal
       left join {{ ref('actorteam_v') }} xx on xx.repteamid  = aat.repteamid
       left join country_map cm on a.accountuid = cm.accountuid
       left join {{ ref('cpa_repaccount_monthly') }} cpa_ra on a.accountuid = cpa_ra.accountuid and a.repuid = cpa_ra.repuid and cast(a.sales_interaction_match_date as date) = cpa_ra.periodvalue
       left join {{ ref('rep_month_workingdays') }} wdays on wdays.repuid = a.repuid and 
{%- if var_multi_country == 'false' %}
      wdays.country = '{{ build_country() }}' 
{%- else %}
      wdays.country = cm.account_country_code
{%- endif %}
      and wdays.dt_month = a.sales_interaction_match_date 
)
select msrscenariouid, '{{ build_company() }}' as company, 
{%- if var_multi_country == 'false' %}
      '{{ build_country() }}' as country, 
{%- else %}
      account_country_code as country,
{%- endif %}
       accountuid, accountid,
    sale_date, repuid,
       alignmentcount, reptargetcount, repaccount_istarget_rpt, account_dim1_rpt, account_dim2_rpt, visit_interaction_count, remote_interaction_count, email_interaction_count, 
       open_count, click_count,
       total_suggestion_count,total_unique_suggestion_count,
       total_suggestion_accept_completed,total_suggestion_accept_incomplete,total_suggestion_dismissed,
       total_suggestion_inferred_accepted,total_suggestion_direct_accepted,
       pilotcontrol_rep,
       repteamid, repteamname, engagementsegment, engagementsegment_monthly,
       engagementsegment_quarterly, engagementrate_monthly, is_user_active,
       m_account_dim1_rpt,m_account_dim2_rpt, m_repteamid, m_repteamname,
       repaccount_aligned_rpt, repaccount_target,targetsperiodid, callplanstartdate, callplanenddate,
       visit_target, email_target, remote_target,
       visit_proratedtarget, email_proratedtarget, remote_proratedtarget,
       cpa_visit_action_count, cpa_email_action_count, cpa_remote_action_count,
       cpa_visit_proratedaction_count, cpa_email_proratedaction_count, cpa_remote_proratedaction_count,
       is_inalignment, is_ininteraction, is_incpa_repacct, working_days
from
data_without_sales;
