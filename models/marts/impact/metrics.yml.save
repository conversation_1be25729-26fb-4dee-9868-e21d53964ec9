version: 2

metrics:
  - name: sales_msr_trx
    label: Sales TRX 
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Sales TRX"

    calculation_method: sum
    expression: sales_value_trx

    timestamp: sale_date
    time_grains: [month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - indication
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: sales_msr_nbrx
    label: Sales NBRX 
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Sales NBRX"

    calculation_method: sum
    expression: sales_value_nbrx

    timestamp: sale_date
    time_grains: [month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - indication
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: sales_msr_nrx
    label: Sales NRX 
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Sales NRX"

    calculation_method: sum
    expression: sales_value_nrx

    timestamp: sale_date
    time_grains: [month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - indication
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: sales_msr_revenue
    label: Sales Revenue
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Sales Revenue"

    calculation_method: sum
    expression: sales_value_revenue

    timestamp: sale_date
    time_grains: [month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - indication
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: sales_msr_unit
    label: Sales Unit
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Sales Unit"

    calculation_method: sum
    expression: sales_value_unit

    timestamp: sale_date
    time_grains: [month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - indication
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly


  - name: visit_detail_count
    label: Visit Detail Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Visit Detail Count"

    calculation_method: sum
    expression: product_visit_interaction_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: email_count
    label: Email Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Email"

    calculation_method: sum
    expression: product_email_interaction_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: email_open_count
    label: Email Open Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Email Open Count"

    calculation_method: sum
    expression: open_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: email_click_count
    label: Email Click Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Email Click Count"

    calculation_method: sum
    expression: click_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: remote_detail_count
    label: Remote Detail Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Remote Detail Count"

    calculation_method: sum
    expression: product_remote_interaction_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly


  - name: visit_interaction_count
    label: Visit Interaction Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Visit Interaction Count"

    calculation_method: sum
    expression: visits_interaction_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly


  - name: remote_interaction_count
    label: Remote Interaction Count
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Remote Interaction Count"

    calculation_method: sum
    expression: remote_interaction_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: suggestion_detail_count
    label: Suggestion Detail
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Count"

    calculation_method: sum
    expression: product_suggestion_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: suggestion_unique_detail_count
    label: Unique Suggestion Count 
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Unique Suggestion Detail Count"

    calculation_method: sum
    expression: product_unique_suggestion_count

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: suggestion_detail_accept_complete_count
    label: Suggestion Detail Accept Completed
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Accept Complete Count"

    calculation_method: sum
    expression: product_suggestion_accept_completed

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: suggestion_detail_accept_incomplete_count
    label: Suggestion Detail Accept InComplete
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Accept InComplete Count"

    calculation_method: sum
    expression: product_suggestion_accept_incomplete

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

  - name: suggestion_detail_dismissed_count
    label: Suggestion Detail Dismissed
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Dismissed Count"

    calculation_method: sum
    expression: product_suggestion_dismissed

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly


  - name: suggestion_detail_inferred_accepted_count
    label: Suggestion Detail Inferred Accepted
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Inferred Accepted Count"

    calculation_method: sum
    expression: product_suggestion_inferred_accepted

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly


  - name: suggestion_detail_direct_accepted_count
    label: Suggestion Detail Direct Accepted
    model: ref('msrscenario_sales_indacctprdrep')
    description: "Suggestion Detail Direct Accepted Count"

    calculation_method: sum
    expression: product_suggestion_direct_accepted

    timestamp: sale_date
    time_grains: [day, week, month, quarter, year]

    dimensions:
      - msrscenariouid
      - productuid
      - company
      - country
      - repteamname
      - pilotcontrol_rep
      - accountproduct_segment_rpt 
      - accountproduct_tier_rpt
      - accountproduct_dim1_rpt
      - accountproduct_dim2_rpt
      - account_dim1_rpt
      - account_dim2_rpt
      - engagementsegment 
      - engagementsegment_monthly
      - engagementsegment_quarterly

