{{ config(materialized='table') }}

{% set var_multi_country = build_get_is_multicountry_flag() %}

with interactions_without_sales as
(
  select a.msrscenariouid, a.accountuid, ad.accountid, a.productuid, p.productid, a.repuid, a.sales_interaction_match_date from
       {{ ref('msrscenario_all_monthly_interactions_dtl') }} a
   left join  {{ ref('msrscenario_sales_monthly_indicationaccountproduct') }} b  on a.msrscenariouid = b.msrscenariouid and a.accountuid = b.accountuid and a.productuid = b.productuid and a.sales_interaction_match_date = b.sale_date
   left join {{ ref('account_dse_v') }} ad on a.accountuid = ad.externalid
   left join {{ ref('product_v') }} p on a.productuid = p.externalid
    where b.msrscenariouid is null
),
accountproduct_history as (
   select a.msrscenariouid, ad.externalid as accountuid, p.externalid as productuid,
          a.as_of_year, a.as_of_month, map_agg(a.field_name, a.month_value) as accountproduct_value_map
  from {{ ref('msrscenario_accountproduct_history') }} a
   inner join {{ ref('account_dse_v') }} ad on a.accountid = ad.accountid
   inner join {{ ref('product_v') }} p on a.productid = p.productid
   group by a.msrscenariouid, ad.externalid, p.externalid, a.as_of_year, a.as_of_month
),
account_history as (
   select a.msrscenariouid, ad.externalid as accountuid,
          a.as_of_year, a.as_of_month,
          map_agg(a.field_name, a.month_value) as account_value_map
   from {{ ref('msrscenario_account_dse_history') }} a
   inner join {{ ref('account_dse_v') }} ad on a.accountid = ad.accountid
   group by a.msrscenariouid, ad.externalid, ad.configcountrycode, a.as_of_year, a.as_of_month
),
country_map as (
   select ad.externalid as accountuid, ad.configcountrycode as account_country_code
   from {{ ref('account_dse_v') }} ad
),
data_with_sales as
(
    select b.msrscenariouid, a.indication, a.diagnosis_group, a.product_group,
       a.accountuid, a.productuid, a.accountid, a.productid,
       a.account_ref_id, a.product_ref_id, a.sale_date, b.repuid, p.productname,
       (a.sales_value_trx/b.alignmentcount*1.0) as sales_value_trx,
       (a.sales_value_nrx/b.alignmentcount*1.0) as sales_value_nrx,
       (a.sales_value_nbrx/b.alignmentcount*1.0) as sales_value_nbrx,
       (a.sales_value_unit/b.alignmentcount*1.0) as sales_value_unit,
       (a.sales_value_revenue/b.alignmentcount*1.0) as sales_value_revenue,
       b.alignmentcount,
       b.reptargetcount,
       b.repaccount_istarget_rpt,
       case when b.alignmentcount is not null then 1 else 0 end as repaccount_aligned_rpt,
       b.account_dim1_rpt,
       b.account_dim2_rpt,
       intr.visit_interaction_count,
       intr.remote_interaction_count,
       intr.email_interaction_count,
       intr.product_visit_interaction_count,
       intr.product_remote_interaction_count,
       intr.product_email_interaction_count,
       intr.open_count,
       intr.click_count,
       intr.product_suggestion_count,
       intr.product_unique_suggestion_count,
       intr.product_suggestion_accept_completed,
       intr.product_suggestion_accept_incomplete,
       intr.product_suggestion_dismissed,
       intr.product_suggestion_inferred_accepted,
       intr.product_suggestion_direct_accepted,
       intr.total_suggestion_count,
       intr.total_unique_suggestion_count,
       intr.total_suggestion_accept_completed,
       intr.total_suggestion_accept_incomplete,
       intr.total_suggestion_dismissed,
       intr.total_suggestion_inferred_accepted,
       intr.total_suggestion_direct_accepted,
       usr.pilotcontrol_rep,
       usr.repteamid,
       x.repteamname,
       aat.repteamid as m_repteamid,
       xx.repteamname as m_repteamname,
       eng.engagementsegment_median as engagementsegment,
       engm.engagementsegment as engagementsegment_monthly,
       engq.engagementsegment_median as engagementsegment_quarterly,
       engm.engagementrate as engagementrate_monthly,
       usr.is_user_active,
       y.accountproduct_segment_rpt,
       y.accountproduct_tier_rpt,
       y.accountproduct_dim1_rpt,
       y.accountproduct_dim2_rpt,
       aph.accountproduct_value_map['accountproduct_segment_rpt'] as m_accountproduct_segment_rpt,
       aph.accountproduct_value_map['accountproduct_tier_rpt'] as m_accountproduct_tier_rpt,
       aph.accountproduct_value_map['accountproduct_dim1_rpt'] as m_accountproduct_dim1_rpt,
       aph.accountproduct_value_map['accountproduct_dim2_rpt'] as m_accountproduct_dim2_rpt,
       ah.account_value_map['account_dim1_rpt'] as m_account_dim1_rpt,
       ah.account_value_map['account_dim2_rpt'] as m_account_dim2_rpt,
       cm.account_country_code,
       case when cpa_rap.accountuid is not null then 1 else 0 end as repaccountproduct_target,
       case when cpa_ra.accountuid is not null then 1 else 0 end as repaccount_target,
       case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end as targetsperiodid,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanstartdate else cpa_ra.callplanstartdate end as callplanstartdate,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanenddate else cpa_ra.callplanenddate end as callplanenddate,
       coalesce(cpa_rap.visit_target, cpa_ra.visit_target) as visit_target,
       coalesce(cpa_rap.email_target, cpa_ra.email_target) as email_target,
       coalesce(cpa_rap.remote_target, cpa_ra.remote_target) as remote_target,
       coalesce(cpa_rap.visit_proratedtarget, cpa_ra.visit_proratedtarget) as visit_proratedtarget,
       coalesce(cpa_rap.email_proratedtarget, cpa_ra.email_proratedtarget) as email_proratedtarget,
       coalesce(cpa_rap.remote_proratedtarget, cpa_ra.remote_proratedtarget) as remote_proratedtarget,
       coalesce(cpa_rap.cpa_visit_action, cpa_ra.cpa_visit_action) as cpa_visit_action_count,
       coalesce(cpa_rap.cpa_email_action, cpa_ra.cpa_email_action) as cpa_email_action_count,
       coalesce(cpa_rap.cpa_remote_action, cpa_ra.cpa_remote_action) as cpa_remote_action_count,
       coalesce(cpa_rap.cpa_visit_proratedaction, cpa_ra.cpa_visit_proratedaction) as cpa_visit_proratedaction_count,
       coalesce(cpa_rap.cpa_email_proratedaction, cpa_ra.cpa_email_proratedaction) as cpa_email_proratedaction_count,
       coalesce(cpa_rap.cpa_remote_proratedaction, cpa_ra.cpa_remote_proratedaction) as cpa_remote_proratedaction_count,
       wdays.working_days,
       cj.segment as adoption_ladder_segment,
       cj.segmentrank as adoption_ladder_segmentrank,
       (cj.rankchange/b.alignmentcount*1.0) as adoption_ladder_rankchange,
       (cj.rankchange_binary/b.alignmentcount*1.0) as adoption_ladder_rankchange_binary
       from {{ ref('msrscenario_sales_monthly_indicationaccountproduct') }} a
       join {{ ref('msrscenario_repaccountassignment_v') }} b on a.msrscenariouid = b.msrscenariouid and a.accountuid = b.accountuid
       join {{ ref('msrscenario_user_v')  }} usr on  a.msrscenariouid = usr.msrscenariouid and b.repuid = usr.userid
       left join {{ ref('msrscenario_all_monthly_interactions_dtl') }} intr on a.msrscenariouid = intr.msrscenariouid and a.accountuid = intr.accountuid and a.productuid = intr.productuid and b.repuid = intr.repuid and a.sale_date = intr.sales_interaction_match_date
       left join {{ ref('imp_repengagementsegmentation_v') }} eng on b.repuid = eng.repuid
       left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on b.repuid = engm.repuid and a.sale_date = engm.periodvalue
       left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on b.repuid = engq.repuid and date_trunc('quarter', a.sale_date) = engq.periodvalue
       left join {{ ref('actorteam_v') }} x on x.repteamid  = usr.repteamid
       left join {{ ref('msrscenario_accountproduct') }} y on a.msrscenariouid = y.msrscenariouid and a.accountuid = y.accountuid and a.productuid = y.productuid
       left join accountproduct_history aph on a.msrscenariouid = aph.msrscenariouid and a.accountuid = aph.accountuid and a.productuid = aph.productuid and  year(a.sale_date) = aph.as_of_year and month(a.sale_date) = aph.as_of_month
       left join account_history ah on a.msrscenariouid = ah.msrscenariouid and a.accountuid = ah.accountuid and  year(a.sale_date) = ah.as_of_year and month(a.sale_date) = ah.as_of_month
       left join {{ ref('actor_actorteam_engine_monthly') }} aat on b.repuid = aat.repuid and a.sale_date = aat.startdatelocal
       left join {{ ref('actorteam_v') }} xx on xx.repteamid  = aat.repteamid
       left join country_map cm on a.accountuid = cm.accountuid
       left join {{ ref('cpa_repaccount_monthly')}} cpa_ra on a.accountuid = cpa_ra.accountuid and b.repuid = cpa_ra.repuid and cast(a.sale_date as date) = cpa_ra.periodvalue
       left join {{ ref('cpa_repaccountproduct_monthly')}} cpa_rap on a.accountuid = cpa_rap.accountuid and b.repuid = cpa_rap.repuid and cast(a.sale_date as date) = cpa_rap.periodvalue and a.productuid = cpa_rap.productuid
       left join {{ ref('product_v') }} p on a.productuid = p.externalid
       left join {{ ref('rep_month_workingdays') }} wdays on wdays.repuid = b.repuid and
{%- if var_multi_country == 'false' %}
      wdays.country = '{{ build_country() }}'
{%- else %}
      wdays.country = cm.account_country_code
{%- endif %}
      and wdays.dt_month = a.sale_date
      left join {{ ref('adoption_ladder_change') }} cj on a.accountuid = cj.accountuid and a.productuid = cj.productuid and a.sale_date = cj.yearmonth
), data_without_sales as
(
    select a.msrscenariouid, cast(null as varchar) indication, cast(null as varchar) diagnosis_group, cast(null as varchar) product_group,
       a.accountuid, a.productuid, a.accountid, a.productid, p.productname,
       cast(null as varchar) account_ref_id, cast(null as varchar) product_ref_id, a.sales_interaction_match_date as sale_date, a.repuid,
       (cast(null as double)) as sales_value_trx,
       (cast(null as double)) as sales_value_nrx,
       (cast(null as double)) as sales_value_nbrx,
       (cast(null as double)) as sales_value_unit,
       (cast(null as double)) as sales_value_revenue,
       b.alignmentcount,
       b.reptargetcount,
       b.repaccount_istarget_rpt,
       case when b.alignmentcount is not null then 1 else 0 end as repaccount_aligned_rpt,
       b.account_dim1_rpt,
       b.account_dim2_rpt,
       intr.visit_interaction_count,
       intr.remote_interaction_count,
       intr.email_interaction_count,
       intr.product_visit_interaction_count,
       intr.product_remote_interaction_count,
       intr.product_email_interaction_count,
       intr.open_count,
       intr.click_count,
       intr.product_suggestion_count,
       intr.product_unique_suggestion_count,
       intr.product_suggestion_accept_completed,
       intr.product_suggestion_accept_incomplete,
       intr.product_suggestion_dismissed,
       intr.product_suggestion_inferred_accepted,
       intr.product_suggestion_direct_accepted,
       intr.total_suggestion_count,
       intr.total_unique_suggestion_count,
       intr.total_suggestion_accept_completed,
       intr.total_suggestion_accept_incomplete,
       intr.total_suggestion_dismissed,
       intr.total_suggestion_inferred_accepted,
       intr.total_suggestion_direct_accepted,
       usr.pilotcontrol_rep,
       usr.repteamid,
       x.repteamname,
       aat.repteamid as m_repteamid,
       xx.repteamname as m_repteamname,
       eng.engagementsegment_median as engagementsegment,
       engm.engagementsegment as engagementsegment_monthly,
       engq.engagementsegment_median as engagementsegment_quarterly,
       engm.engagementrate as engagementrate_monthly,
       usr.is_user_active,
       y.accountproduct_segment_rpt,
       y.accountproduct_tier_rpt,
       y.accountproduct_dim1_rpt,
       y.accountproduct_dim2_rpt,
       aph.accountproduct_value_map['accountproduct_segment_rpt'] as m_accountproduct_segment_rpt,
       aph.accountproduct_value_map['accountproduct_tier_rpt'] as m_accountproduct_tier_rpt,
       aph.accountproduct_value_map['accountproduct_dim1_rpt'] as m_accountproduct_dim1_rpt,
       aph.accountproduct_value_map['accountproduct_dim2_rpt'] as m_accountproduct_dim2_rpt,
       ah.account_value_map['account_dim1_rpt'] as m_account_dim1_rpt,
       ah.account_value_map['account_dim2_rpt'] as m_account_dim2_rpt,
       cm.account_country_code,
       case when cpa_rap.accountuid is not null then 1 else 0 end as repaccountproduct_target,
       case when cpa_ra.accountuid is not null then 1 else 0 end as repaccount_target,
       case when cpa_rap.accountuid is not null then cpa_rap.targetsperiodid else cpa_ra.targetsperiodid end as targetsperiodid,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanstartdate else cpa_ra.callplanstartdate end as callplanstartdate,
       case when cpa_rap.accountuid is not null then cpa_rap.callplanenddate else cpa_ra.callplanenddate end as callplanenddate,
       coalesce(cpa_rap.visit_target, cpa_ra.visit_target) as visit_target,
       coalesce(cpa_rap.email_target, cpa_ra.email_target) as email_target,
       coalesce(cpa_rap.remote_target, cpa_ra.remote_target) as remote_target,
       coalesce(cpa_rap.visit_proratedtarget, cpa_ra.visit_proratedtarget) as visit_proratedtarget,
       coalesce(cpa_rap.email_proratedtarget, cpa_ra.email_proratedtarget) as email_proratedtarget,
       coalesce(cpa_rap.remote_proratedtarget, cpa_ra.remote_proratedtarget) as remote_proratedtarget,
       coalesce(cpa_rap.cpa_visit_action, cpa_ra.cpa_visit_action) as cpa_visit_action_count,
       coalesce(cpa_rap.cpa_email_action, cpa_ra.cpa_email_action) as cpa_email_action_count,
       coalesce(cpa_rap.cpa_remote_action, cpa_ra.cpa_remote_action) as cpa_remote_action_count,
       coalesce(cpa_rap.cpa_visit_proratedaction, cpa_ra.cpa_visit_proratedaction) as cpa_visit_proratedaction_count,
       coalesce(cpa_rap.cpa_email_proratedaction, cpa_ra.cpa_email_proratedaction) as cpa_email_proratedaction_count,
       coalesce(cpa_rap.cpa_remote_proratedaction, cpa_ra.cpa_remote_proratedaction) as cpa_remote_proratedaction_count,
       wdays.working_days,
       cj.segment as adoption_ladder_segment,
       cj.segmentrank as adoption_ladder_segmentrank,
       (cj.rankchange/b.alignmentcount*1.0) as adoption_ladder_rankchange,
       (cj.rankchange_binary/b.alignmentcount*1.0) as adoption_ladder_rankchange_binary
       from interactions_without_sales a
       left join {{ ref('msrscenario_repaccountassignment_v') }} b on a.msrscenariouid = b.msrscenariouid and a.accountuid = b.accountuid and a.repuid = b.repuid
       join {{ ref('msrscenario_user_v')  }} usr on  a.msrscenariouid = usr.msrscenariouid and usr.userid = a.repuid
       left join {{ ref('msrscenario_all_monthly_interactions_dtl') }} intr on a.msrscenariouid = intr.msrscenariouid and a.accountuid = intr.accountuid and a.productuid = intr.productuid and a.repuid = intr.repuid and a.sales_interaction_match_date = intr.sales_interaction_match_date
       left join {{ ref('imp_repengagementsegmentation_v') }} eng on a.repuid = eng.repuid
       left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on a.repuid = engm.repuid and a.sales_interaction_match_date = engm.periodvalue
       left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on a.repuid = engq.repuid and date_trunc('quarter', a.sales_interaction_match_date) = engq.periodvalue
       left join {{ ref('actorteam_v') }} x on x.repteamid  = usr.repteamid
       left join {{ ref('msrscenario_accountproduct') }} y on a.msrscenariouid = y.msrscenariouid and a.accountuid = y.accountuid and a.productuid = y.productuid
       left join accountproduct_history aph on a.msrscenariouid = aph.msrscenariouid and a.accountuid = aph.accountuid and a.productuid = aph.productuid and  year(a.sales_interaction_match_date) = aph.as_of_year and month(a.sales_interaction_match_date) = aph.as_of_month
       left join account_history ah on a.msrscenariouid = ah.msrscenariouid and a.accountuid = ah.accountuid and  year(a.sales_interaction_match_date) = ah.as_of_year and month(a.sales_interaction_match_date) = ah.as_of_month
       left join {{ ref('actor_actorteam_engine_monthly') }} aat on a.repuid = aat.repuid and a.sales_interaction_match_date = aat.startdatelocal
       left join {{ ref('actorteam_v') }} xx on xx.repteamid  = aat.repteamid
       left join country_map cm on a.accountuid = cm.accountuid
       left join {{ ref('cpa_repaccount_monthly')}} cpa_ra on a.accountuid = cpa_ra.accountuid and a.repuid = cpa_ra.repuid and cast(a.sales_interaction_match_date as date) = cpa_ra.periodvalue
       left join {{ ref('cpa_repaccountproduct_monthly')}} cpa_rap on a.accountuid = cpa_rap.accountuid and a.repuid = cpa_rap.repuid and cast(a.sales_interaction_match_date as date) = cpa_rap.periodvalue and a.productuid = cpa_rap.productuid
       left join {{ ref('product_v') }} p on a.productuid = p.externalid
       left join {{ ref('rep_month_workingdays') }} wdays on wdays.repuid = a.repuid and
{%- if var_multi_country == 'false' %}
      wdays.country = '{{ build_country() }}'
{%- else %}
      wdays.country = cm.account_country_code
{%- endif %}
      and wdays.dt_month = a.sales_interaction_match_date
      left join {{ ref('adoption_ladder_change') }} cj on a.accountuid = cj.accountuid and a.productuid = cj.productuid and a.sales_interaction_match_date = cj.yearmonth
)
select msrscenariouid, '{{ build_company() }}' as company,
{%- if var_multi_country == 'false' %}
      '{{ build_country() }}' as country,
{%- else %}
      account_country_code as country,
{%- endif %}
indication, diagnosis_group, product_group, accountuid, productuid, accountid, productid, productname,
       account_ref_id, product_ref_id, sale_date, repuid, sales_value_trx, sales_value_nrx, sales_value_nbrx, sales_value_unit, sales_value_revenue,
       alignmentcount, reptargetcount, repaccount_istarget_rpt, account_dim1_rpt, account_dim2_rpt, visit_interaction_count, remote_interaction_count, email_interaction_count,
       product_visit_interaction_count, product_remote_interaction_count, product_email_interaction_count, open_count, click_count,
       product_suggestion_count,product_unique_suggestion_count,product_suggestion_accept_completed,
       product_suggestion_accept_incomplete,product_suggestion_dismissed,product_suggestion_inferred_accepted,
       product_suggestion_direct_accepted,total_suggestion_count,total_unique_suggestion_count,
       total_suggestion_accept_completed,total_suggestion_accept_incomplete,total_suggestion_dismissed,
       total_suggestion_inferred_accepted,total_suggestion_direct_accepted,
       pilotcontrol_rep,
       repteamid, repteamname, engagementsegment, engagementsegment_monthly,
       engagementsegment_quarterly, engagementrate_monthly, is_user_active,
       accountproduct_segment_rpt, accountproduct_tier_rpt,accountproduct_dim1_rpt,accountproduct_dim2_rpt,
       m_accountproduct_segment_rpt, m_accountproduct_tier_rpt, m_accountproduct_dim1_rpt, m_accountproduct_dim2_rpt,
       m_account_dim1_rpt,m_account_dim2_rpt, m_repteamid, m_repteamname,
       repaccount_aligned_rpt, repaccountproduct_target,repaccount_target,targetsperiodid, callplanstartdate, callplanenddate,
       visit_target, email_target, remote_target,
       visit_proratedtarget, email_proratedtarget, remote_proratedtarget,
       cpa_visit_action_count, cpa_email_action_count, cpa_remote_action_count,
       cpa_visit_proratedaction_count, cpa_email_proratedaction_count, cpa_remote_proratedaction_count,
       working_days, adoption_ladder_segment, adoption_ladder_segmentrank, adoption_ladder_rankchange, adoption_ladder_rankchange_binary
from
data_with_sales
union
select msrscenariouid, '{{ build_company() }}' as company,
{%- if var_multi_country == 'false' %}
      '{{ build_country() }}' as country,
{%- else %}
      account_country_code as country,
{%- endif %}
      indication, diagnosis_group, product_group, accountuid, productuid, accountid, productid, productname,
       account_ref_id, product_ref_id, sale_date, repuid, sales_value_trx, sales_value_nrx, sales_value_nbrx, sales_value_unit, sales_value_revenue,
       alignmentcount, reptargetcount, repaccount_istarget_rpt, account_dim1_rpt, account_dim2_rpt, visit_interaction_count, remote_interaction_count, email_interaction_count,
       product_visit_interaction_count, product_remote_interaction_count, product_email_interaction_count, open_count, click_count,
       product_suggestion_count,product_unique_suggestion_count,product_suggestion_accept_completed,
       product_suggestion_accept_incomplete,product_suggestion_dismissed,product_suggestion_inferred_accepted,
       product_suggestion_direct_accepted,total_suggestion_count,total_unique_suggestion_count,
       total_suggestion_accept_completed,total_suggestion_accept_incomplete,total_suggestion_dismissed,
       total_suggestion_inferred_accepted,total_suggestion_direct_accepted,
       pilotcontrol_rep,
       repteamid, repteamname, engagementsegment, engagementsegment_monthly,
       engagementsegment_quarterly, engagementrate_monthly, is_user_active,
       accountproduct_segment_rpt, accountproduct_tier_rpt,accountproduct_dim1_rpt,accountproduct_dim2_rpt,
       m_accountproduct_segment_rpt, m_accountproduct_tier_rpt, m_accountproduct_dim1_rpt, m_accountproduct_dim2_rpt,
       m_account_dim1_rpt,m_account_dim2_rpt, m_repteamid, m_repteamname,
       repaccount_aligned_rpt, repaccountproduct_target,repaccount_target,targetsperiodid, callplanstartdate, callplanenddate,
       visit_target, email_target, remote_target,
       visit_proratedtarget, email_proratedtarget, remote_proratedtarget,
       cpa_visit_action_count, cpa_email_action_count, cpa_remote_action_count,
       cpa_visit_proratedaction_count, cpa_email_proratedaction_count, cpa_remote_proratedaction_count,
       working_days, adoption_ladder_segment, adoption_ladder_segmentrank, adoption_ladder_rankchange, adoption_ladder_rankchange_binary
from
data_without_sales;
