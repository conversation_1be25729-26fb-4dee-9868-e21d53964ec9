{{ config(materialized='view') }}

with avg_engagement as
(select repuid, date_trunc('quarter', periodvalue) as periodvalue, avg(engagementrate) avg_engagementrate, STDDEV(engagementrate) stddev_engagementrate, approx_percentile(engagementrate, 0.5) median_engagementrate 
    from {{ ref('imp_repengagementsegmentation_monthly_v') }} 
 group by repuid, date_trunc('quarter', periodvalue)
), engagement_bucket as
(
select repuid, periodvalue, avg_engagementrate, stddev_engagementrate, median_engagementrate, ntile(3) over (partition by periodvalue order by avg_engagementrate) avg_bucket, ntile(3) over (partition by periodvalue order by median_engagementrate) median_bucket from avg_engagement
),
rep_characteristics as
(select repuid, min(repname) repname, min(configname) configname, count(*) active_months
    from {{ ref('imp_repengagementsegmentation_monthly_v') }} 
 group by repuid
),
eng_bucket_metrics as
(select periodvalue, median_bucket, avg(median_engagementrate) avg_bucket_engagementrate, min(median_engagementrate) min_bucket_engagementrate, max(median_engagementrate) max_bucket_engagementrate, approx_percentile(median_engagementrate, 0.5) median_bucket_engagementrate 
    from engagement_bucket
 group by periodvalue, median_bucket
) 
select a.repuid,b.periodvalue,  a.repname, a.configname, a.active_months, round(avg_engagementrate,2) as avg_engagementrate,  round(stddev_engagementrate, 2) stddev_engagementrate, round(median_engagementrate,2) as median_engagementrate,
case when b.avg_bucket = 1 then 'Low' when b.avg_bucket = 2 then 'Medium' when b.avg_bucket = 3 then 'High' end engagementsegment_avg,
case when b.median_bucket = 1 then 'Low' when b.median_bucket = 2 then 'Medium' when b.median_bucket = 3 then 'High' end engagementsegment_median,
round(ebm.avg_bucket_engagementrate,2) as avg_bucket_engagementrate, round(median_bucket_engagementrate,2) as median_bucket_engagementrate,
round(ebm.min_bucket_engagementrate,2) as min_bucket_engagementrate, round(max_bucket_engagementrate,2) as max_bucket_engagementrate
from 
rep_characteristics a inner join engagement_bucket b on a.repuid = b.repuid
inner join eng_bucket_metrics ebm on ebm.median_bucket = b.median_bucket and ebm.periodvalue = b.periodvalue;

