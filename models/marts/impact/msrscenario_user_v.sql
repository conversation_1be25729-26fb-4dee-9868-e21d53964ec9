 -- depends_on: {{ ref('actor_v') }}
 -- depends_on: {{ ref('user_v') }}

{{ config(materialized='view') }}

with all_user as (

{% if not build_check_is_veeva_customer() %}
select a.externalid as userid, profile_name_vod__c, companyname, division, department,  country, id, a.repname as username, lastname, firstname, name, title, email, userroleid, localesidkey, usertype,languagelocalekey, lastlogindate, createddate, a.configcountrycode as country_code_vod__c, user_type_vod__c, backupcreateddate, backupmodifieddate, functional_role_rpt, active_rpt, campaign_member_rpt, bu_grouping_rpt, training_completed_rpt, a.isactivated as isactive
FROM
 {{ ref('actor_v') }} a left join {{ ref('user_v') }} b on a.externalid = b.id
{%- else %}
SELECT id as userid, profile_name_vod__c, companyname, division, department, country, id, username, lastname, firstname, name, title, email, userroleid, localesidkey, usertype,languagelocalekey, lastlogindate, createddate,  country_code_vod__c, user_type_vod__c, backupcreateddate, backupmodifieddate, functional_role_rpt, active_rpt, campaign_member_rpt, bu_grouping_rpt, training_completed_rpt, isactive
FROM
 {{ ref('user_v') }}
{%- endif %}
),
all_user_view as (
select a.*, z.msrscenariouid,
        /*p.pilot_call_activity_count, p.pilot_call_activity_avg_monthly_count, pp.before_pilot_call_activity_count, l.lastyear_pilot_call_activity_count,
        q.pilot_email_activity_count, q.pilot_email_activity_avg_monthly_count, qq.before_pilot_email_activity_count,ll.lastyear_pilot_email_activity_count,
        n.analysis_call_activity_count, nn.analysis_email_activity_count,
        t.pilot_period_territory_count, */
        case when rr.repuid is not null then cast(1 as boolean) else cast(0 as boolean) end is_aktana_user,
        firstdate as suggestion_firstdate, lastdate as suggestion_lastdate, active_month_count as suggestion_active_month_count,
         nbrdays as suggestion_nbrdays, suggestion_avg_monthly_count,
        suggestion_count, config_count as suggestion_config_count, seconfigid, seconfigname, repteamid,
        eng.engagementsegment_median as engagementsegment_median, a.isactive as is_user_active
from all_user a
     cross join {{ ref('param_msrscenario_events_v') }} z
     left join {{ ref('imp_repsummary_v') }} rr on a.userid = rr.repuid
     left join {{ ref('imp_repengagementsegmentation_v') }} eng on a.userid = eng.repuid
)
select *
      {{ build_rep_expr()  }}
from
all_user_view usr;

