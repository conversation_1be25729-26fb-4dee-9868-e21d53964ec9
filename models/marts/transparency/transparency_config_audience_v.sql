{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by= {
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with config_audience as (
    select esr.rundate, esr.configid, raa.accountid, raa.repid
    from {{ ref('engine_rep_account_assignment_v') }} raa
    -- join {{ ref('account_dse_v') }} a on raa.accountid = a.accountid
    join {{ ref('engine_suggestible_reps_v') }} esr on raa.rundate = esr.rundate
        and raa.configid = esr.configid
        and raa.repid = esr.repid
    {% if is_incremental() %}
    where esr.rundate > (select max(rundate) from {{ this }})
    {% endif %}
)

select rundate, configid, count(distinct repId) repCount, count(distinct accountId) accountCount
    from config_audience
    group by rundate, configid