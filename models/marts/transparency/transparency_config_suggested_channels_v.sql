{{ config(
    materialized='incremental',
    unique_key=['rundate', 'seconfigid', 'channelname'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

select rundate, seconfigid, c.channelname, count(1) suggestionCount
from {{ ref('scored_candidates_v') }} ca inner join {{ ref('channel_v') }} c on ca.suggestedchannelid = c.channelid
where recommended = True
{% if is_incremental() %}
and ca.rundate > (select max(rundate) from {{ this }})
{% endif %}
group by rundate, seconfigid, c.channelname
;
