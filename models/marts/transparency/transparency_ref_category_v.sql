{{ config(materialized='view') }}

select 1 as categoryRank, 'Incomplete setup' categoryName, 'IncompleteSetupAccountCount' categoryColumn, 'The tactics are not producing suggestions because they lack rules, have an inactive channel, or use placeholder products' categoryDescription
union
select 2 as categoryRank, 'Tactic criteria not met' categoryName, 'TacticCriteriaNotMetAccountCount' categoryColumn, 'No triggering conditions, audience filters, or eligible users qualify for a valid action' categoryDescription
union
select 3 as categoryRank, 'Suppression policies' categoryName, 'SuppressionPoliciesAccountCount' categoryColumn, 'Target HCP is not eligible to receive this kind of suggestion due to business policies' categoryDescription
union
select 4 as categoryRank, 'User un-availability' categoryName, 'UserUnavailableAccountCount' categoryColumn, 'User is unavailable as indicated in CRM' categoryDescription
union
select 5 as categoryRank, 'User feedback' categoryName, 'UserFeedbackAccountCount' categoryColumn, 'User has dismissed/snoozed this in the recent past' categoryDescription
union
select 6 as categoryRank, 'Rejected during optimization' categoryName, 'OptimizerRejectedAccountCount' categoryColumn, 'Rejected due to multiple potential reasons - unlikely success, low value, capacity issue, etc.' categoryDescription
union
select 7 as categoryRank, 'Replaced by an alternate' categoryName, 'ReplacedByAlternateAccountCount' categoryColumn, 'Replaced by an action due to multiple reasons - higher likelihood of success, value, capacity, etc.' categoryDescription
union
select 8 as categoryRank, 'Unknown' categoryName, 'UnknownAccountCount' categoryColumn, 'Coming soon - The system couldn''t crack the code for this one yet' categoryDescription
union
select 9 as categoryRank, 'Recommended' categoryName, 'RecommendedAccountCount' categoryColumn, 'Suggestion Recommended' categoryDescription
