{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'suggCount'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with suggCounts as (
select reps.rundate, reps.configid, reps.repid, sum(case when recommended is null then 0 else 1 end) suggCount
from {{ ref('engine_suggestible_reps_v') }} reps
left join {{ ref('scored_candidates_v') }} c
    on reps.repid = c.repid
    and reps.rundate = c.rundate
    and cast(reps.configid as integer) = c.seconfigid
    and recommended = True
    {% if is_incremental() %}
    where reps.rundate > (select max(rundate) from {{ this }}) and c.rundate > (select max(rundate) from {{ this }})
    {% endif %}
group by reps.rundate, reps.configid, reps.repid
)
select rundate, configid, suggCount, count(1) repCount
from suggCounts
group by rundate, configid, suggCount
