{{ config(
    materialized='incremental',
    unique_key=['rundate', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with summary as (
select rundate, factoruid, max(factorname) factorname, max(configid) configid, status_reason, count(distinct accountid) accountCount, count(1) evalCount, count(distinct suggestionCandidateUid) candidateCount
from {{ ref('transparency_detail_reason_passed_v') }}
{% if is_incremental() %}
where rundate > (select max(rundate) from {{ this }})
{% endif %}
group by rundate, factoruid, status_reason
)

select rundate, max(factorname) factorname, factoruid, max(configid) configid,
coalesce(max(case when status_reason = '00. Recommended' then accountCount else 0 end), 0) as C00_RecommendedAccountCount,
coalesce(max(case when status_reason = '00. Recommended' then evalCount else 0 end), 0) as C00_RecommendedEvalCount,
coalesce(max(case when status_reason = '01. Success Unlikely*' then accountCount else 0 end), 0) as C01_SuccessUnlikelyAccountCount,
coalesce(max(case when status_reason = '01. Success Unlikely*' then evalCount else 0 end), 0) as C01_SuccessUnlikelyEvalCount,
coalesce(max(case when status_reason = '02. Low Expected Value*' then accountCount else 0 end), 0) as C02_LowExpectedValueNoAlternateAccountCount,
coalesce(max(case when status_reason = '02. Low Expected Value*' then evalCount else 0 end), 0) as C02_LowExpectedValueNoAlternateEvalCount,
coalesce(max(case when status_reason = '03. Actor Capacity*' then accountCount else 0 end), 0) as C03_ActorCapacityNoAlternateAccountCount,
coalesce(max(case when status_reason = '03. Actor Capacity*' then evalCount else 0 end), 0) as C03_ActorCapacityNoAlternateEvalCount,
coalesce(max(case when status_reason = '04. Channel Capacity*' then accountCount else 0 end), 0) as C04_ChannelCapacityNoAlternateAccountCount,
coalesce(max(case when status_reason = '04. Channel Capacity*' then evalCount else 0 end), 0) as C04_ChannelCapacityNoAlternateEvalCount,
coalesce(max(case when status_reason = '05. Horizon Capacity*' then accountCount else 0 end), 0) as C05_HorizonCapacityNoAlternateAccountCount,
coalesce(max(case when status_reason = '05. Horizon Capacity*' then evalCount else 0 end), 0) as C05_HorizonCapacityNoAlternateEvalCount,
coalesce(max(case when status_reason = '06. Low Expected Value' then accountCount else 0 end), 0) as C06_LowExpectedValueAccountCount,
coalesce(max(case when status_reason = '06. Low Expected Value' then evalCount else 0 end), 0) as C06_LowExpectedValueEvalCount,
coalesce(max(case when status_reason = '07. Actor Capacity' then accountCount else 0 end), 0) as C07_ActorCapacityAccountCount,
coalesce(max(case when status_reason = '07. Actor Capacity' then evalCount else 0 end), 0) as C07_ActorCapacityEvalCount,
coalesce(max(case when status_reason = '08. Channel Capacity' then accountCount else 0 end), 0) as C08_ChannelCapacityAccountCount,
coalesce(max(case when status_reason = '08. Channel Capacity' then evalCount else 0 end), 0) as C08_ChannelCapacityEvalCount,
coalesce(max(case when status_reason = '09. Horizon Capacity' then accountCount else 0 end), 0) as C09_HorizonCapacityAccountCount,
coalesce(max(case when status_reason = '09. Horizon Capacity' then evalCount else 0 end), 0) as C09_HorizonCapacityEvalCount,
coalesce(max(case when status_reason = '51. Suppression' then accountCount else 0 end), 0) as C51_SuppressionAccountCount,
coalesce(max(case when status_reason = '51. Suppression' then evalCount else 0 end), 0) as C51_SuppressionEvalCount,
coalesce(max(case when status_reason = '52. Rep Not Available' then accountCount else 0 end), 0) as C52_RepUnavailableAccountCount,
coalesce(max(case when status_reason = '52. Rep Not Available' then evalCount else 0 end), 0) as C52_RepUnavailableEvalCount,
coalesce(max(case when status_reason = '53. Rep Account on Hold' then accountCount else 0 end), 0) as C53_RepAccountOnHoldAccountCount,
coalesce(max(case when status_reason = '53. Rep Account on Hold' then evalCount else 0 end), 0) as C53_RepAccountOnHoldEvalCount,
coalesce(max(case when status_reason = '54. Rep Product Not Authorized' then accountCount else 0 end), 0) as C54_RepProductNotAuthorizedAccountCount,
coalesce(max(case when status_reason = '54. Rep Product Not Authorized' then evalCount else 0 end), 0) as C54_RepProductNotAuthorizedEvalCount,
coalesce(max(case when status_reason = '55. RepType Not Enabled' then accountCount else 0 end), 0) as C55_RepTypeNotEnabledAccountCount,
coalesce(max(case when status_reason = '55. RepType Not Enabled' then evalCount else 0 end), 0) as C55_RepTypeNotEnabledEvalCount,
coalesce(max(case when status_reason = '56. AccountType Not Enabled' then accountCount else 0 end), 0) as C56_AccountTypeNotEnabledAccountCount,
coalesce(max(case when status_reason = '56. AccountType Not Enabled' then evalCount else 0 end), 0) as C56_AccountTypeNotEnabledEvalCount,
coalesce(max(case when status_reason = '99. Unknown' then accountCount else 0 end), 0) as C99_UnknownAccountCount,
coalesce(max(case when status_reason = '99. Unknown' then evalCount else 0 end), 0) as C99_UnknownEvalCount,
coalesce(max(case when status_reason = '00. Recommended' then candidateCount else 0 end), 0) as candidateCount

from summary
group by rundate, factoruid