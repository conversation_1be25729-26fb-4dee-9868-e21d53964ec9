{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'status_reason'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}


select rundate, configid, status_reason, count(distinct accountid) accountCount, count(1) evalCount,
count(distinct concat(cast(accountid as varchar), '_', factoruid)) actionCount, count(distinct suggestionCandidateUid) candidateCount
from {{ ref('transparency_detail_reason_passed_v') }}
{% if is_incremental() %}
where rundate > (select max(rundate) from {{ this }})
{% endif %}
group by rundate, configid, status_reason
