{{ config(
    materialized='incremental',
    unique_key=['rundate', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with summary as (
select rundate, factoruid, max(factorname) factorname, max(configid) configid, failureReason, count(distinct accountid) accountCount, count(1) evalCount --, count(distinct accountid, factoruid) actionCount
from {{ ref('failed_evaluation_v') }}
{% if is_incremental() %}
where rundate > (select max(rundate) from {{ this }})
{% endif %}
group by rundate, factoruid, failureReason
)

select rundate, max(factorname) factorname, factoruid, max(configid) configid,
coalesce(max(case when failureReason = 'Account Type mismatch with factor''s account type' then accountCount else 0 end), 0) as C71_AccountTypeMismatchAccountCount,
coalesce(max(case when failureReason = 'Account Type mismatch with factor''s account type' then evalCount else 0 end), 0) as C71_AccountTypeMismatchEvalCount,
coalesce(max(case when failureReason = 'Used custom attributes not matching with account type' then accountCount else 0 end), 0) as C72_AttributesNotMatchAccountCount,
coalesce(max(case when failureReason = 'Used custom attributes not matching with account type' then evalCount else 0 end), 0) as C72_AttributesNotMatchEvalCount,
coalesce(max(case when failureReason = 'Factor has no rules' then accountCount else 0 end), 0) as C73_FactorNoRuleAccountCount,
coalesce(max(case when failureReason = 'Factor has no rules' then evalCount else 0 end), 0) as C73_FactorNoRuleEvalCount,
coalesce(max(case when failureReason = 'Factor segments do not pass evaluation condition' then accountCount else 0 end), 0) as C74_SegmentNotPassAccountCount,
coalesce(max(case when failureReason = 'Factor segments do not pass evaluation condition' then evalCount else 0 end), 0) as C74_SegmentNotPassEvalCount,
coalesce(max(case when failureReason = 'Account''s account type not matching with factor''s actorType' then accountCount else 0 end), 0) as C75_AccountActorTypeMismatchAccountCount,
coalesce(max(case when failureReason = 'Account''s account type not matching with factor''s actorType' then evalCount else 0 end), 0) as C75_AccountActorTypeMismatchEvalCount,
coalesce(max(case when failureReason = 'Factor is defined for non-enabled channel' then accountCount else 0 end), 0) as C76_ChannelDisabledAccountCount,
coalesce(max(case when failureReason = 'Factor is defined for non-enabled channel' then evalCount else 0 end), 0) as C76_ChannelDisabledEvalCount,
coalesce(max(case when failureReason = 'Use of artificial product: AKT_ALL_PRODUCTS' then accountCount else 0 end), 0) as C77_DummyProductAccountCount,
coalesce(max(case when failureReason = 'Use of artificial product: AKT_ALL_PRODUCTS' then evalCount else 0 end), 0) as C77_DummyProductEvalCount,
coalesce(max(case when failureReason = 'Use of non-suggestible product' then accountCount else 0 end), 0) as C78_ProductNotSuggestibleAccountCount,
coalesce(max(case when failureReason = 'Use of non-suggestible product' then evalCount else 0 end), 0) as C78_ProductNotSuggestibleEvalCount,
coalesce(max(case when failureReason = 'No assigned / suggestible rep authorized for product' then accountCount else 0 end), 0) as C79_NoAuthorizedRepAccountCount,
coalesce(max(case when failureReason = 'No assigned / suggestible rep authorized for product' then evalCount else 0 end), 0) as C79_NoAuthorizedRepEvalCount,
coalesce(max(case when failureReason = 'AssignedRep is not a suggestibleRep' then accountCount else 0 end), 0) as C80_RepNotSuggestibleAccountCount,
coalesce(max(case when failureReason = 'AssignedRep is not a suggestibleRep' then evalCount else 0 end), 0) as C80_RepNotSuggestibleEvalCount,
coalesce(max(case when failureReason = 'AssignedRep is not authorized for this product' then accountCount else 0 end), 0) as C81_RepNotAuthorizedAccountCount,
coalesce(max(case when failureReason = 'AssignedRep is not authorized for this product' then evalCount else 0 end), 0) as C81_RepNotAuthorizedEvalCount,
coalesce(max(case when failureReason = 'AssignedRep''s rep type is not matching with factor''s actor type' then accountCount else 0 end), 0) as C82_RepTypeMismatchAccountCount,
coalesce(max(case when failureReason = 'AssignedRep''s rep type is not matching with factor''s actor type' then evalCount else 0 end), 0) as C82_RepTypeMismatchEvalCount,
coalesce(max(case when failureReason = 'Parent factor''s rules not satisfied' then accountCount else 0 end), 0) as C83_ParentRuleNotSatisfiedAccountCount,
coalesce(max(case when failureReason = 'Parent factor''s rules not satisfied' then evalCount else 0 end), 0) as C83_ParentRuleNotSatisfiedEvalCount,
coalesce(max(case when failureReason = 'Factor rules not satisfied' then accountCount else 0 end), 0) as C84_RuleNotSatisfiedAccountCount,
coalesce(max(case when failureReason = 'Factor rules not satisfied' then evalCount else 0 end), 0) as C84_RuleNotSatisfiedEvalCount

from summary
group by rundate, factoruid