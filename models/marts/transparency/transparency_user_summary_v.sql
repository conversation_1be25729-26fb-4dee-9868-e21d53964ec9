{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'repId'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with accountAssignment as (
    select rundate, configid, repId, count(distinct accountid) accountCount
    from {{ ref('engine_rep_account_assignment_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, repId
),
productsAuthorized as (
    select rundate, configid, repId, count(distinct productid) authorizedProductsCount
    from {{ ref('engine_rep_product_authorization_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, repId
),
accountHold as (
    select rundate, configid, repId, count(distinct accountid) onHoldAccountCount
    from {{ ref('engine_rep_account_hold_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, repId
),
unavailability as (
    select rundate, configid, repId, count(1) unavailabilityCount
    from {{ ref('engine_rep_unavailable_period_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, repId
)
select r.rundate, r.configid, r.repId, r.repName, dc.repTeamid, rt.repTeamName, r.repTypeUid, a.accountCount, p.authorizedProductsCount, h.onHoldAccountCount, u.unavailabilityCount
from {{ ref('engine_suggestible_reps_v') }} r
-- Rep can belong to multiple rep-teams.  But each config has only one repteam.  So, get repteam from the config
-- left join {{ ref('engine_rep_team_rep_v') }} rtr on r.rundate = rtr.rundate and r.configid = rtr.configid and rtr.repid = r.repid
left join {{ ref('engine_dseconfig_v') }} dc on r.rundate = dc.rundate and r.configid = dc.configid
left join {{ ref('engine_rep_team_v') }} rt on rt.rundate = dc.rundate and rt.configid = dc.configid and dc.repTeamid = rt.repTeamid
left join accountAssignment a on r.rundate = a.rundate and r.configid = a.configid and a.repid = r.repid
left join productsAuthorized p on a.rundate = p.rundate and a.configid = p.configid and a.repId = p.repId
left join accountHold h on a.rundate = h.rundate and a.configid = h.configid and a.repId = h.repId
left join unavailability u on a.rundate = u.rundate and a.configid = u.configid and a.repId = u.repId
{% if is_incremental() %}
where r.rundate > (select max(rundate) from {{ this }})
{% endif %}
