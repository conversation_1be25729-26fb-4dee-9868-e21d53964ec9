{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'factorUid', 'accountId', 'repId', 'productid', 'suggestionCandidateUid', 'suggestedDate'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

-- with group_rank as (
--     select dco_run_uid, groupId, min(rank) as min_rank
--     from {{ ref('scored_candidates_v') }} r
--     where recommended = True
--     group by dco_run_uid, groupId
-- ),

-- factor_group_rank as (
--     select dco_run_uid, groupIdFactor, min(rankFactor) min_factor_rank
--     from {{ ref('scored_candidates_v') }} r
--     where recommended = True
--     group by dco_run_uid, groupIdFactor)

with detail_without_category as (
select reps.runDate, reps.configid, a.factorType, a.factorUid, a.factorname, a.accountId, c.repId, a.productid,
c.accountName, reps.repName,
b.suggestionCandidateUid, b.suggestedDate, b.recommended, b.channel, b.finalScore expectedValue,
case when f.accountid is null then 0 else 1 end as isSuppressed ,
case when rup.repid is null then 1 else 0 end as isRepAvailable,
case when rah.repid is null then 0 else 1 end as isRepAccountOnHold,
case when d.repid is null then 0 else 1 end as isRepProductAuthorized,
case when b.recommended = True then 'Suggested' else 'Failed' end as status,
case when b.status_reason is not null then b.status_reason
     when f.accountid is not null then '51. Suppression'
     when rup.repid is not null then '52. Rep Not Available'
     when rah.repid is not null then '53. Rep Account on Hold'
     when d.repid is null then '54. Rep Product Not Authorized'
     when dfc.channelOptionActorTypeUIds is not null and dfc.channelOptionActorTypeUIds != '' and position(reps.repTypeUid in dfc.channelOptionActorTypeUIDs) = 0 then '55. RepType Not Enabled'
     when dfc.accountTypeIds != 'all' and position(c.accounttypeuid in dfc.accounttypeids) = 0 then '56. AccountType Not Enabled'
     when b.recommended is null and a.factorType = 'triggerFactor' then '99. Unknown'
     when a.factorType = 'suppressionFactor' then '51. Suppression'
    --  when recommended = False and dcoReasonText like 'Account/channel has been filtered based on auto-snooze list%' then '01. Auto-expired'
    --  when recommended = False and dcoReasonText like 'Rejected: Action value low. Non-optimal action candidate%' then '02. Account-level conflict'
    --  when recommended = False and dcoReasonText like 'Rejected: Action value low. Non-optimal action candidate in the Factor group%' then '03. Factor-level conflict'
    --  when recommended = False and g.groupId is not null and b.rank != g.min_rank then '02. Account-level conflict'
    --  when recommended = False and g.groupId is null and b.rank != 1 then '02. Account-level conflict'
    --  when recommended = False and fg.groupIdFactor is not null and b.rankFactor != fg.min_factor_rank then '03. Factor-level conflict'
    --  when recommended = False and fg.groupIdFactor is null and b.rankFactor != 1 then '03. Factor-level conflict'
    --  when recommended = False and repAvailableCapAdditionalRun < 1 and dcoReasonText like '%. --- Rejected: Maximum rep capacity reached%' then '04. End user capacity'
    --  when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableRequiredCap = 0 and channelAvailableAdditionalCap > 0 and dcoReasonText like '%Rejected: Maximum channel capacity reached%' then '05. Channel capacity'
    --  when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableAdditionalCap = 0 then '05. Channel capacity'
    --  when recommended = False and dcoReasonText like '%Rejected: Number of recommended suggestions exceeded the average Horizon maximum for the rep%' then '06. Horizon planning max exceeded'
    --  when recommended = False and dcoReasonText like '%Rejected: Action value is less than threshold%' then '07. Action value below threshold'
    --  when recommended = False or recommended is null then '12. Unknown'
    --  when recommended = True and repAvailableCapRequiredRun > 0 and channelAvailableRequiredCap > 0 then '00. Recommended'
    --  when recommended = True and repAvailableCapAdditionalRun > 0 and channelAvailableRequiredCap = 0 and channelAvailableAdditionalCap > 0 then '00. Recommended'
     end as status_reason

from {{ ref('engine_suggestible_reps_v') }} reps
inner join {{ ref('passed_factor_evaluation_v') }} a
    on reps.rundate = a.rundate
    and reps.configid = a.configid
    and reps.repid = coalesce(a.repid, reps.repid)
join {{ ref('engine_rep_account_assignment_v') }} c
    on a.accountid = c.accountid
    and a.rundate between c.startdate and c.enddate
    and a.rundate = c.rundate
    and a.configid = c.configid
    and reps.repid = c.repid
left join {{ ref('engine_dseconfig_factor_channelOption_v') }} dfc
   on a.configid = dfc.configid
   and a.factoruid = dfc.factoruid
   and a.rundate = dfc.rundate
left join {{ ref('engine_rep_product_authorization_v') }} d
    on c.repid = d.repid
    and d.productid = coalesce(a.productid, d.productid)
    and a.rundate = d.rundate
left join {{ ref('scored_candidates_factors_v') }} b
    on a.factoruid = b.reason_factoruid
    and a.rundate = b.rundate
    and a.accountid = b.accountid
    and b.repid = reps.repid
left join {{ ref('passed_factor_evaluation_v') }} f
    on a.rundate = f.rundate
    and a.factoruid = f.factoruid
    and a.accountid = f.accountid
    and f.status = 2
left join {{ ref('engine_rep_unavailable_period_v') }} rup
    on rup.repid = coalesce(a.repid, c.repid)
    and a.rundate between rup.startdate and rup.enddate
    and a.rundate = rup.rundate
left join {{ ref('engine_rep_account_hold_v') }} rah
    on rah.repid = coalesce(a.repid, c.repid)
    and rah.accountid = a.accountid
    and a.rundate between rah.startdate and rah.enddate
    and a.rundate = rah.rundate
-- left join group_rank g
--     on b.dco_run_uid = g.dco_run_uid
--     and b.groupId = g.groupId
-- left join factor_group_rank fg
--     on b.dco_run_uid = fg.dco_run_uid
--     and b.groupIdFactor = fg.groupIdFactor

where a.status = 1
-- and a.rundate > date_format(date_add('month', -1, current_date), '%Y-%m-%d')
)

select runDate, configid, factorUid, accountId, repId, productid, suggestionCandidateUid, suggestedDate, status_reason, max(factorType) factorType, max(factorname) factorname, max(accountName) accountName, max(repName) repName, max(recommended) recommended, max(channel) channel, max(expectedValue) expectedValue, max(isSuppressed) isSuppressed, max(isRepAvailable) isRepAvailable, max(isRepAccountOnHold) isRepAccountOnHold, max(isRepProductAuthorized) isRepProductAuthorized, max(status) status,
case when status_reason = '00. Recommended' then 'Recommended'
     when status_reason < '06.' and status_reason > '01.' then 'Rejected during optimization'
     when status_reason < '10.' and status_reason > '06.' then 'Replaced by an alternate'
     when status_reason = '51. Suppression' then 'Suppression policies'
     when status_reason = '52. Rep Not Available' then 'User un-availability'
     when status_reason = '53. Rep Account on Hold' then 'User feedback'
     when status_reason = '54. Rep Product Not Authorized' then 'Tactic criteria not met'
     when status_reason in  ('55. RepType Not Enabled', '56. AccountType Not Enabled') then 'Incomplete setup'
     else 'Unknown'
end as status_category
from detail_without_category d
group by runDate, configid, factorUid, accountId, repId, productid, suggestionCandidateUid, suggestedDate, status_reason
