{{ config(
    materialized='incremental',
    unique_key=['rundate', 'factorType', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}


with eval_with_category as (
    select f.*,
    case when failureReason in ('Use of artificial product: AKT_ALL_PRODUCTS',
                                'Factor has no rules', 'Factor is defined for non-enabled channel'
                                )
         then 'Incomplete setup'
         when failureReason in ('Account Type mismatch with factor''s account type', 'Used custom attributes not matching with account type',
                                'Factor segments do not pass evaluation condition',
                                'Account''s account type not matching with factor''s actorType',
                                'No assigned / suggestible rep authorized for product',
                                'AssignedRep is not a suggestibleRep', 'AssignedRep is not authorized for this product',
                                'AssignedRep''s rep type is not matching with factor''s actor type',
                                'Parent factor''s rules not satisfied', 'Factor rules not satisfied', 'Use of non-suggestible product'
                                )
         then 'Tactic criteria not met'
         else 'Unknown'
    end as status_category
    from {{ ref('failed_evaluation_v') }} f
    {% if is_incremental() %}
    where f.rundate > (select max(rundate) from {{ this }})
    {% endif %}
),

summary as (
select rundate, factorType, factoruid, max(factorname) factorname, max(configid) configid, status_category, count(distinct accountid) accountCount, count(1) evalCount --, count(distinct accountid, factoruid) actionCount
from eval_with_category
-- where rundate > now - 1 month
group by rundate, factorType, factoruid, status_category
)

select rundate, factorType, max(factorname) factorname, factoruid, max(configid) configid,
coalesce(max(case when status_category = 'Incomplete setup' then accountCount else 0 end), 0) as IncompleteSetupAccountCount,
coalesce(max(case when status_category = 'Incomplete setup' then evalCount else 0 end), 0) as IncompleteSetupEvalCount,
coalesce(max(case when status_category = 'Tactic criteria not met' then accountCount else 0 end), 0) as TacticCriteriaNotMetAccountCount,
coalesce(max(case when status_category = 'Tactic criteria not met' then evalCount else 0 end), 0) as TacticCriteriaNotMetEvalCount

from summary
group by rundate, factorType, factoruid