{{ config(
    materialized='incremental',
    unique_key=['rundate', 'factorUID', 'configId'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by=['rundate']
) }}

with factors as (
  select
    dc.seConfigId,
    reason.factorUID as factorUID,
    rundate
  from {{ref('scored_candidates_v')}} dc
  cross join unnest(dc.reasons) as t (reason)
  where dc.recommended = True
  {% if is_incremental() %}
  and dc.rundate > (select max(rundate) from {{ this }})
  {% endif %}
  group by dc.rundate, dc.seConfigId, reason.factorUID
  UNION
  select
    dc.seConfigId,
    dc.factorUID as factorUID,
    rundate
  from {{ref('scored_candidates_v')}} dc
  where dc.recommended = True
  {% if is_incremental() %}
  and dc.rundate > (select max(rundate) from {{ this }})
  {% endif %}
  group by dc.rundate, dc.seConfigId, dc.factorUID
  UNION
  select
    dc.seConfigId,
    product.factorUID as factorUID,
    rundate
  from {{ref('scored_candidates_v')}} dc
  cross join unnest(dc.products) as t (product)
  where dc.recommended = True
  {% if is_incremental() %}
  and dc.rundate > (select max(rundate) from {{ this }})
  {% endif %}
  group by dc.rundate, dc.seConfigId, product.factorUID
),
factors_collapsed as (
  select
    seConfigId,
    factorUID,
    rundate
  from factors
  group by rundate, seConfigId, factorUID
)
select
  cast(seConfigId as varchar) configId,
  factorUID, rundate
from factors_collapsed fc
{% if is_incremental() %}
where fc.rundate > (select max(rundate) from {{ this }})
{% endif %}
