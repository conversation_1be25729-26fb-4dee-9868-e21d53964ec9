{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with factors as (
select rundate, seconfigid, factoruid, recommended
from {{ ref('scored_candidates_factors_v') }} c
{% if is_incremental() %}
where c.rundate > (select max(rundate) from {{ this }})
{% endif %}
group by  rundate, seconfigid, factoruid, recommended
),
total_factors as (
select rundate, configid, count(distinct factoruid) totalFactorCount, count(distinct (case when factorType = 'triggerFactor' then factoruid else null end)) triggerFactorCount from {{ ref('transparency_factor_category_v') }} group by rundate, configid
), recommended_factors as (
select rundate, seconfigid, count(distinct factoruid) recommendedFactorCount from factors where recommended = True group by rundate, seconfigid
)
select t.rundate, t.configid, totalFactorCount, triggerFactorCount, recommendedFactorCount from total_factors t,  recommended_factors r where t.rundate = r.rundate and cast(t.configid as integer) = r.seconfigid
