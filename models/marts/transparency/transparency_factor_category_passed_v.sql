{{ config(
    materialized='incremental',
    unique_key=['rundate', 'factorType', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with summary as (
select rundate, configid, factorType, factoruid, max(factorname) factorname, status_category, count(distinct accountid) accountCount, count(1) evalCount, count(distinct suggestionCandidateUid) candidateCount
from {{ ref('transparency_detail_reason_passed_v') }}
{% if is_incremental() %}
where rundate > (select max(rundate) from {{ this }})
{% endif %}
group by rundate, configid, factorType, factoruid, status_category
)

select rundate, configid, factorType, max(factorname) factorname, factoruid,
coalesce(max(case when status_category = 'Recommended' then accountCount else 0 end), 0) as RecommendedAccountCount,
coalesce(max(case when status_category = 'Recommended' then evalCount else 0 end), 0) as RecommendedEvalCount,
coalesce(max(case when status_category = 'Rejected during optimization' then accountCount else 0 end), 0) as OptimizerRejectedAccountCount,
coalesce(max(case when status_category = 'Rejected during optimization' then evalCount else 0 end), 0) as OptimizerRejectedEvalCount,
coalesce(max(case when status_category = 'Replaced by an alternate' then accountCount else 0 end), 0) as ReplacedByAlternateAccountCount,
coalesce(max(case when status_category = 'Replaced by an alternate' then evalCount else 0 end), 0) as ReplacedByAlternateEvalCount,
coalesce(max(case when status_category = 'Suppression policies' then accountCount else 0 end), 0) as SuppressionPoliciesAccountCount,
coalesce(max(case when status_category = 'Suppression policies' then evalCount else 0 end), 0) as SuppressionPoliciesEvalCount,
coalesce(max(case when status_category = 'User un-availability' then accountCount else 0 end), 0) as UserUnavailableAccountCount,
coalesce(max(case when status_category = 'User un-availability' then evalCount else 0 end), 0) as UserUnavailableEvalCount,
coalesce(max(case when status_category = 'User feedback' then accountCount else 0 end), 0) as UserFeedbackAccountCount,
coalesce(max(case when status_category = 'User feedback' then evalCount else 0 end), 0) as UserFeedbackEvalCount,
coalesce(max(case when status_category = 'Incomplete setup' then accountCount else 0 end), 0) as IncompleteSetupAccountCount,
coalesce(max(case when status_category = 'Incomplete setup' then evalCount else 0 end), 0) as IncompleteSetupEvalCount,
coalesce(max(case when status_category = 'Tactic criteria not met' then accountCount else 0 end), 0) as TacticCriteriaNotMetAccountCount,
coalesce(max(case when status_category = 'Tactic criteria not met' then evalCount else 0 end), 0) as TacticCriteriaNotMetEvalCount,
coalesce(max(case when status_category = 'Unknown' then accountCount else 0 end), 0) as UnknownAccountCount,
coalesce(max(case when status_category = 'Unknown' then evalCount else 0 end), 0) as UnknownEvalCount,
coalesce(max(case when status_category = 'Recommended' then candidateCount else 0 end), 0) as candidateCount
from summary
group by rundate, configid, factorType, factoruid