{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'repId'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with rep_account_counts as (
    select reps.rundate, reps.configid, reps.repId,
    max(reps.repName) repName,
    count(distinct accountid) accountCount
    from {{ ref('engine_suggestible_reps_v') }} reps
    left join {{ ref('engine_rep_account_assignment_v') }} c
        on reps.rundate = c.rundate
        and reps.configid = c.configid
        and reps.repid = c.repid
        and reps.rundate between c.startdate and c.enddate
    {% if is_incremental() %}
    where reps.rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by reps.rundate, reps.configid, reps.repId
),
passed_summary as (
    select rundate,
    configid, repId,
    status_category,
    grouping(status_category) as is_status_grouping,
    count(distinct accountid) accountCount, count(1) evalCount,
    count(distinct concat(cast(accountid as varchar), '_', factoruid)) actionCount,
    count(distinct suggestionCandidateUid) candidateCount
    from {{ ref('transparency_detail_reason_passed_v') }}
    where factorType != 'suppressionFactor' -- ACE-5756: Don't include suppression factor in summary stats
    {% if is_incremental() %}
    and rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by grouping sets(
               (rundate, configid, repId, status_category),
               (rundate, configid, repId))
),
summary as (
    select r.rundate, r.configid, r.repid, r.repName,
    case p.is_status_grouping when 0 then p.status_category else 'Incomplete setup' end as status_category,
    case p.is_status_grouping when 0 then p.evalCount else (r.accountCount - coalesce(p.accountCount, 0)) end as evalCount,
    case p.is_status_grouping when 0 then p.actionCount else (r.accountCount - coalesce(p.accountCount, 0)) end as actionCount,
    case p.is_status_grouping when 0 then p.accountCount else (r.accountCount - coalesce(p.accountCount, 0)) end as accountCount,
    case p.is_status_grouping when 0 then p.candidateCount else 0 end as candidateCount,
    r.accountCount as repAccountCountTotal
    from rep_account_counts r
    left join passed_summary p
        on r.rundate = p.rundate
        and r.configid = p.configid
        and r.repid = p.repid
),
pivoted_summary as (
    select rundate, max(repName) repName, repId, configid,
    coalesce(max(case when status_category = 'Recommended' then accountCount else 0 end), 0) as RecommendedAccountCount,
    coalesce(max(case when status_category = 'Recommended' then actionCount else 0 end), 0) as RecommendedActionCount,
    coalesce(max(case when status_category = 'Recommended' then evalCount else 0 end), 0) as RecommendedEvalCount,
    coalesce(max(case when status_category = 'Rejected during optimization' then accountCount else 0 end), 0) as OptimizerRejectedAccountCount,
    coalesce(max(case when status_category = 'Rejected during optimization' then actionCount else 0 end), 0) as OptimizerRejectedActionCount,
    coalesce(max(case when status_category = 'Rejected during optimization' then evalCount else 0 end), 0) as OptimizerRejectedEvalCount,
    coalesce(max(case when status_category = 'Replaced by an alternate' then accountCount else 0 end), 0) as ReplacedByAlternateAccountCount,
    coalesce(max(case when status_category = 'Replaced by an alternate' then actionCount else 0 end), 0) as ReplacedByAlternateActionCount,
    coalesce(max(case when status_category = 'Replaced by an alternate' then evalCount else 0 end), 0) as ReplacedByAlternateEvalCount,
    coalesce(max(case when status_category = 'Suppression policies' then accountCount else 0 end), 0) as SuppressionPoliciesAccountCount,
    coalesce(max(case when status_category = 'Suppression policies' then actionCount else 0 end), 0) as SuppressionPoliciesActionCount,
    coalesce(max(case when status_category = 'Suppression policies' then evalCount else 0 end), 0) as SuppressionPoliciesEvalCount,
    coalesce(max(case when status_category = 'User un-availability' then accountCount else 0 end), 0) as UserUnavailableAccountCount,
    coalesce(max(case when status_category = 'User un-availability' then actionCount else 0 end), 0) as UserUnavailableActionCount,
    coalesce(max(case when status_category = 'User un-availability' then evalCount else 0 end), 0) as UserUnavailableEvalCount,
    coalesce(max(case when status_category = 'User feedback' then accountCount else 0 end), 0) as UserFeedbackAccountCount,
    coalesce(max(case when status_category = 'User feedback' then actionCount else 0 end), 0) as UserFeedbackActionCount,
    coalesce(max(case when status_category = 'User feedback' then evalCount else 0 end), 0) as UserFeedbackEvalCount,
    coalesce(max(case when status_category = 'Incomplete setup' then accountCount else 0 end), 0) as IncompleteSetupAccountCount,
    coalesce(max(case when status_category = 'Incomplete setup' then actionCount else 0 end), 0) as IncompleteSetupActionCount,
    coalesce(max(case when status_category = 'Incomplete setup' then evalCount else 0 end), 0) as IncompleteSetupEvalCount,
    coalesce(max(case when status_category = 'Tactic criteria not met' then accountCount else 0 end), 0) as TacticCriteriaNotMetAccountCount,
    coalesce(max(case when status_category = 'Tactic criteria not met' then actionCount else 0 end), 0) as TacticCriteriaNotMetActionCount,
    coalesce(max(case when status_category = 'Tactic criteria not met' then evalCount else 0 end), 0) as TacticCriteriaNotMetEvalCount,
    coalesce(max(case when status_category = 'Unknown' then accountCount else 0 end), 0) as UnknownAccountCount,
    coalesce(max(case when status_category = 'Unknown' then actionCount else 0 end), 0) as UnknownActionCount,
    coalesce(max(case when status_category = 'Unknown' then evalCount else 0 end), 0) as UnknownEvalCount,
    coalesce(max(case when status_category = 'Recommended' then candidateCount else 0 end), 0) as candidateCount,
    max(repAccountCountTotal) as repAccountCountTotal
    from summary
    group by rundate, repId, configid
),
totalCounts as (
    select rundate, configid, repId,
    (RecommendedAccountCount + OptimizerRejectedAccountCount + ReplacedByAlternateAccountCount +
    SuppressionPoliciesAccountCount + UserUnavailableAccountCount + UserFeedbackAccountCount +  TacticCriteriaNotMetAccountCount +
    IncompleteSetupAccountCount + UnknownAccountCount) accountCountTotal,
    (RecommendedActionCount + OptimizerRejectedActionCount + ReplacedByAlternateActionCount +
    SuppressionPoliciesActionCount + UserUnavailableActionCount + UserFeedbackActionCount +  TacticCriteriaNotMetActionCount +
    IncompleteSetupActionCount + UnknownActionCount) ActionCountTotal,
    (RecommendedEvalCount + OptimizerRejectedEvalCount + ReplacedByAlternateEvalCount +
    SuppressionPoliciesEvalCount + UserUnavailableEvalCount + UserFeedbackEvalCount +  TacticCriteriaNotMetEvalCount +
    IncompleteSetupEvalCount + UnknownEvalCount) evalCountTotal,
    RecommendedAccountCount accountCountSuggested,
    candidateCount suggestionCount
    from pivoted_summary r
)
select r.*, t.accountCountTotal accountCountTotal, t.actionCountTotal, t.evalCountTotal, t.accountCountSuggested, t.suggestionCount, cas.repCount as configRepCountTotal, cas.accountCount as configAccountCountTotal
from engine_suggestible_reps_v reps
left join pivoted_summary r on reps.rundate = r.rundate and reps.configid = r.configid and reps.repId = r.repId
inner join totalCounts t
    on r.rundate = t.rundate
    and r.configid = t.configid
    and r.repId = t.repId
left join {{ ref('transparency_config_audience_v') }} cas
    on r.configid = cas.configid
    and r.rundate = cas.rundate
{% if is_incremental() %}
where reps.rundate > (select max(rundate) from {{ this }})
{% endif %}

