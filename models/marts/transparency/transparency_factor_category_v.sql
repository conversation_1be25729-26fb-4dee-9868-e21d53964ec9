{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'factorType', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with passed_summary as (
    select rundate, configid, factorType, factoruid, max(factorname) factorname,
    sum(coalesce(RecommendedAccountCount, 0)) RecommendedAccountCount,
    sum(coalesce(OptimizerRejectedAccountCount, 0)) OptimizerRejectedAccountCount,
    sum(coalesce(ReplacedByAlternateAccountCount, 0)) ReplacedByAlternateAccountCount,
    sum(coalesce(SuppressionPoliciesAccountCount, 0)) SuppressionPoliciesAccountCount,
    sum(coalesce(UserUnavailableAccountCount, 0)) UserUnavailableAccountCount,
    sum(coalesce(UserFeedbackAccountCount, 0)) UserFeedbackAccountCount,
    sum(coalesce(UnknownAccountCount, 0)) UnknownAccountCount,
    sum(coalesce(RecommendedEvalCount, 0)) RecommendedEvalCount,
    sum(coalesce(OptimizerRejectedEvalCount, 0)) OptimizerRejectedEvalCount,
    sum(coalesce(ReplacedByAlternateEvalCount, 0)) ReplacedByAlternateEvalCount,
    sum(coalesce(SuppressionPoliciesEvalCount, 0)) SuppressionPoliciesEvalCount,
    sum(coalesce(UserUnavailableEvalCount, 0)) UserUnavailableEvalCount,
    sum(coalesce(UserFeedbackEvalCount, 0)) UserFeedbackEvalCount,
    sum(coalesce(UnknownEvalCount, 0)) UnknownEvalCount,
    sum(coalesce(candidateCount, 0)) candidateCount
    from {{ ref('transparency_factor_category_passed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, factorType, factoruid
),
failed_summary as (
    select rundate, configid, factorType, factoruid, max(factorname) factorname,
    sum(coalesce(IncompleteSetupAccountCount, 0)) IncompleteSetupAccountCount,
    sum(coalesce(TacticCriteriaNotMetAccountCount, 0)) TacticCriteriaNotMetAccountCount,
    sum(coalesce(IncompleteSetupEvalCount, 0)) IncompleteSetupEvalCount,
    sum(coalesce(TacticCriteriaNotMetEvalCount, 0)) TacticCriteriaNotMetEvalCount
    from {{ ref('transparency_factor_category_failed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, factorType, factoruid
),
consolidated_part1 as (
    select p.rundate,
    p.configid,
    p.factorType,
    p.factoruid,
    p.factorname,
    coalesce(RecommendedAccountCount, 0) RecommendedAccountCount,
    coalesce(OptimizerRejectedAccountCount, 0) OptimizerRejectedAccountCount,
    coalesce(ReplacedByAlternateAccountCount, 0) ReplacedByAlternateAccountCount,
    coalesce(SuppressionPoliciesAccountCount, 0) SuppressionPoliciesAccountCount,
    coalesce(UserUnavailableAccountCount, 0) UserUnavailableAccountCount,
    coalesce(UserFeedbackAccountCount, 0) UserFeedbackAccountCount,
    coalesce(IncompleteSetupAccountCount, 0) IncompleteSetupAccountCount,
    coalesce(TacticCriteriaNotMetAccountCount, 0) TacticCriteriaNotMetAccountCount,
    coalesce(UnknownAccountCount, 0) UnknownAccountCount,
    coalesce(RecommendedEvalCount, 0) RecommendedEvalCount,
    coalesce(OptimizerRejectedEvalCount, 0) OptimizerRejectedEvalCount,
    coalesce(ReplacedByAlternateEvalCount, 0) ReplacedByAlternateEvalCount,
    coalesce(SuppressionPoliciesEvalCount, 0) SuppressionPoliciesEvalCount,
    coalesce(UserUnavailableEvalCount, 0) UserUnavailableEvalCount,
    coalesce(UserFeedbackEvalCount, 0) UserFeedbackEvalCount,
    coalesce(IncompleteSetupEvalCount, 0) IncompleteSetupEvalCount,
    coalesce(TacticCriteriaNotMetEvalCount, 0) TacticCriteriaNotMetEvalCount,
    coalesce(UnknownEvalCount, 0) UnknownEvalCount,
    coalesce(candidateCount, 0) candidateCount
    from passed_summary p
    left join failed_summary f on p.rundate = f.rundate and p.configid = f.configid and p.factorType = f.factorType and p.factoruid = f.factoruid
),
consolidated_part2 as (
    select f.rundate,
    f.configid,
    f.factorType,
    f.factoruid,
    f.factorname,
    coalesce(RecommendedAccountCount, 0) RecommendedAccountCount,
    coalesce(OptimizerRejectedAccountCount, 0) OptimizerRejectedAccountCount,
    coalesce(ReplacedByAlternateAccountCount, 0) ReplacedByAlternateAccountCount,
    coalesce(SuppressionPoliciesAccountCount, 0) SuppressionPoliciesAccountCount,
    coalesce(UserUnavailableAccountCount, 0) UserUnavailableAccountCount,
    coalesce(UserFeedbackAccountCount, 0) UserFeedbackAccountCount,
    coalesce(IncompleteSetupAccountCount, 0) IncompleteSetupAccountCount,
    coalesce(TacticCriteriaNotMetAccountCount, 0) TacticCriteriaNotMetAccountCount,
    coalesce(UnknownAccountCount, 0) UnknownAccountCount,
    coalesce(RecommendedEvalCount, 0) RecommendedEvalCount,
    coalesce(OptimizerRejectedEvalCount, 0) OptimizerRejectedEvalCount,
    coalesce(ReplacedByAlternateEvalCount, 0) ReplacedByAlternateEvalCount,
    coalesce(SuppressionPoliciesEvalCount, 0) SuppressionPoliciesEvalCount,
    coalesce(UserUnavailableEvalCount, 0) UserUnavailableEvalCount,
    coalesce(UserFeedbackEvalCount, 0) UserFeedbackEvalCount,
    coalesce(IncompleteSetupEvalCount, 0) IncompleteSetupEvalCount,
    coalesce(TacticCriteriaNotMetEvalCount, 0) TacticCriteriaNotMetEvalCount,
    coalesce(UnknownEvalCount, 0) UnknownEvalCount,
    coalesce(candidateCount, 0) candidateCount
    from failed_summary f
    left join passed_summary p on p.rundate = f.rundate and p.configid = f.configid and p.factorType = f.factorType and p.factoruid = f.factoruid
    where p.rundate is null
),
consolidated_summary as (
    select * from consolidated_part1
    union
    select * from consolidated_part2
),
totalCounts as (
    select rundate, configid, factoruid,
    (RecommendedAccountCount + OptimizerRejectedAccountCount + ReplacedByAlternateAccountCount +
    SuppressionPoliciesAccountCount + UserUnavailableAccountCount + UserFeedbackAccountCount + UnknownAccountCount + IncompleteSetupAccountCount + TacticCriteriaNotMetAccountCount) accountCountTotal,
    (RecommendedEvalCount + OptimizerRejectedEvalCount + ReplacedByAlternateEvalCount +
    SuppressionPoliciesEvalCount + UserUnavailableEvalCount + UserFeedbackEvalCount + UnknownEvalCount + IncompleteSetupEvalCount + TacticCriteriaNotMetEvalCount) evalCountTotal,
    RecommendedAccountCount accountCountSuggested,
    candidateCount suggestionCount
    from consolidated_summary r
),
factor_last_suggested_date as (
    select r.rundate, r.configid, r.factoruid,
    date_parse(max(f.rundate),'%Y-%m-%d') last_suggested_date
    from consolidated_summary r
    left join {{ ref('factors_suggested_by_rundate') }} f
    on f.rundate <= r.rundate and r.factoruid = f.factoruid and r.configid = f.configid
    {% if is_incremental() %}
    where r.rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by r.rundate, r.configid, r.factoruid
)
select r.rundate, r.configid, r.factoruid, r.factorType, r.factorName,
r.RecommendedAccountCount, r.OptimizerRejectedAccountCount, r.ReplacedByAlternateAccountCount, r.SuppressionPoliciesAccountCount, r.UserUnavailableAccountCount,
r.UserFeedbackAccountCount, r.UnknownAccountCount, r.IncompleteSetupAccountCount, r.TacticCriteriaNotMetAccountCount,
r.RecommendedEvalCount, r.OptimizerRejectedEvalCount, r.ReplacedByAlternateEvalCount, r.SuppressionPoliciesEvalCount, r.UserUnavailableEvalCount,
r.UserFeedbackEvalCount, r.UnknownEvalCount, r.candidateCount, r.IncompleteSetupEvalCount, r.TacticCriteriaNotMetEvalCount ,
f.last_suggested_date, date_diff('day', f.last_suggested_date, date_parse(r.rundate,'%Y-%m-%d')) days_since_last_suggested,
greatest(cas.accountCount, t.accountCountTotal) accountCountTotal, t.evalCountTotal, t.accountCountSuggested, t.suggestionCount, cas.repCount as configRepCountTotal, cas.accountCount as configAccountCountTotal
from consolidated_summary r
inner join totalCounts t
    on r.rundate = t.rundate
    and r.configid = t.configid
    and r.factoruid = t.factoruid
left join {{ ref('transparency_config_audience_v') }} cas
    on r.configid = cas.configid
    and r.rundate = cas.rundate
    {% if is_incremental() %}
    and cas.rundate > (select max(rundate) from {{ this }})
    {% endif %}
left join factor_last_suggested_date f
    on f.rundate = r.rundate and r.factoruid = f.factoruid and r.configid = f.configid
