{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'status_category'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with eval_with_category as (
    select f.*,
    case when failureReason in ('Use of artificial product: AKT_ALL_PRODUCTS',
                                'Factor has no rules', 'Factor is defined for non-enabled channel'
                                )
         then 'Incomplete setup'
         when failureReason in ('Account Type mismatch with factor''s account type', 'Used custom attributes not matching with account type',
                                'Factor segments do not pass evaluation condition',
                                'Account''s account type not matching with factor''s actorType',
                                'No assigned / suggestible rep authorized for product',
                                'AssignedRep is not a suggestibleRep', 'AssignedRep is not authorized for this product',
                                'AssignedRep''s rep type is not matching with factor''s actor type',
                                'Parent factor''s rules not satisfied', 'Factor rules not satisfied', 'Use of non-suggestible product'
                                )
         then 'Tactic criteria not met'
         else 'Unknown'
    end as status_category
    from {{ ref('failed_evaluation_v') }} f
    {% if is_incremental() %}
    where f.rundate > (select max(rundate) from {{ this }})
    {% endif %}
)
select rundate, configid, status_category, count(distinct accountid) accountCount, count(1) evalCount, count(distinct concat(cast(accountid as varchar), '_', factoruid)) actionCount
from eval_with_category
-- where rundate > now - 1 month
group by rundate, configid, status_category
