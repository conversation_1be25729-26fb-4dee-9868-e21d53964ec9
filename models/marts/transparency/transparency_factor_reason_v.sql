{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'factoruid'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with passed_summary as (
    select rundate, configid, factoruid, max(factorname) factorname,
        sum(coalesce(C00_RecommendedAccountCount, 0)) as C00_RecommendedAccountCount,
        sum(coalesce(C01_SuccessUnlikelyAccountCount, 0)) as C01_SuccessUnlikelyAccountCount,
        sum(coalesce(C02_LowExpectedValueNoAlternateAccountCount, 0)) as C02_LowExpectedValueNoAlternateAccountCount,
        sum(coalesce(C03_ActorCapacityNoAlternateAccountCount, 0)) as C03_ActorCapacityNoAlternateAccountCount,
        sum(coalesce(C04_ChannelCapacityNoAlternateAccountCount, 0)) as C04_ChannelCapacityNoAlternateAccountCount,
        sum(coalesce(C05_HorizonCapacityNoAlternateAccountCount, 0)) as C05_HorizonCapacityNoAlternateAccountCount,
        sum(coalesce(C06_LowExpectedValueAccountCount, 0)) as C06_LowExpectedValueAccountCount,
        sum(coalesce(C07_ActorCapacityAccountCount, 0)) as C07_ActorCapacityAccountCount,
        sum(coalesce(C08_ChannelCapacityAccountCount, 0)) as C08_ChannelCapacityAccountCount,
        sum(coalesce(C09_HorizonCapacityAccountCount, 0)) as C09_HorizonCapacityAccountCount,
        sum(coalesce(C51_SuppressionAccountCount, 0)) as C51_SuppressionAccountCount,
        sum(coalesce(C52_RepUnavailableAccountCount, 0)) as C52_RepUnavailableAccountCount,
        sum(coalesce(C53_RepAccountOnHoldAccountCount, 0)) as C53_RepAccountOnHoldAccountCount,
        sum(coalesce(C54_RepProductNotAuthorizedAccountCount, 0)) as C54_RepProductNotAuthorizedAccountCount,
        sum(coalesce(C55_RepTypeNotEnabledAccountCount, 0)) as C55_RepTypeNotEnabledAccountCount,
        sum(coalesce(C56_AccountTypeNotEnabledAccountCount, 0)) as C56_AccountTypeNotEnabledAccountCount,
        sum(coalesce(C99_UnknownAccountCount, 0)) as C99_UnknownAccountCount,
        0 as C71_AccountTypeMismatchAccountCount,
        0 as C72_AttributesNotMatchAccountCount,
        0 as C73_FactorNoRuleAccountCount,
        0 as C74_SegmentNotPassAccountCount,
        0 as C75_AccountActorTypeMismatchAccountCount,
        0 as C76_ChannelDisabledAccountCount,
        0 as C77_DummyProductAccountCount,
        0 as C78_ProductNotSuggestibleAccountCount,
        0 as C79_NoAuthorizedRepAccountCount,
        0 as C80_RepNotSuggestibleAccountCount,
        0 as C81_RepNotAuthorizedAccountCount,
        0 as C82_RepTypeMismatchAccountCount,
        0 as C83_ParentRuleNotSatisfiedAccountCount,
        0 as C84_RuleNotSatisfiedAccountCount,
        sum(coalesce(C00_RecommendedEvalCount, 0)) as C00_RecommendedEvalCount,
        sum(coalesce(C01_SuccessUnlikelyEvalCount, 0)) as C01_SuccessUnlikelyEvalCount,
        sum(coalesce(C02_LowExpectedValueNoAlternateEvalCount, 0)) as C02_LowExpectedValueNoAlternateEvalCount,
        sum(coalesce(C03_ActorCapacityNoAlternateEvalCount, 0)) as C03_ActorCapacityNoAlternateEvalCount,
        sum(coalesce(C04_ChannelCapacityNoAlternateEvalCount, 0)) as C04_ChannelCapacityNoAlternateEvalCount,
        sum(coalesce(C05_HorizonCapacityNoAlternateEvalCount, 0)) as C05_HorizonCapacityNoAlternateEvalCount,
        sum(coalesce(C06_LowExpectedValueEvalCount, 0)) as C06_LowExpectedValueEvalCount,
        sum(coalesce(C07_ActorCapacityEvalCount, 0)) as C07_ActorCapacityEvalCount,
        sum(coalesce(C08_ChannelCapacityEvalCount, 0)) as C08_ChannelCapacityEvalCount,
        sum(coalesce(C09_HorizonCapacityEvalCount, 0)) as C09_HorizonCapacityEvalCount,
        sum(coalesce(C51_SuppressionEvalCount, 0)) as C51_SuppressionEvalCount,
        sum(coalesce(C52_RepUnavailableEvalCount, 0)) as C52_RepUnavailableEvalCount,
        sum(coalesce(C53_RepAccountOnHoldEvalCount, 0)) as C53_RepAccountOnHoldEvalCount,
        sum(coalesce(C54_RepProductNotAuthorizedEvalCount, 0)) as C54_RepProductNotAuthorizedEvalCount,
        sum(coalesce(C55_RepTypeNotEnabledEvalCount, 0)) as C55_RepTypeNotEnabledEvalCount,
        sum(coalesce(C56_AccountTypeNotEnabledEvalCount, 0)) as C56_AccountTypeNotEnabledEvalCount,
        sum(coalesce(C99_UnknownEvalCount, 0)) as C99_UnknownEvalCount,
        0 as C71_AccountTypeMismatchEvalCount,
        0 as C72_AttributesNotMatchEvalCount,
        0 as C73_FactorNoRuleEvalCount,
        0 as C74_SegmentNotPassEvalCount,
        0 as C75_AccountActorTypeMismatchEvalCount,
        0 as C76_ChannelDisabledEvalCount,
        0 as C77_DummyProductEvalCount,
        0 as C78_ProductNotSuggestibleEvalCount,
        0 as C79_NoAuthorizedRepEvalCount,
        0 as C80_RepNotSuggestibleEvalCount,
        0 as C81_RepNotAuthorizedEvalCount,
        0 as C82_RepTypeMismatchEvalCount,
        0 as C83_ParentRuleNotSatisfiedEvalCount,
        0 as C84_RuleNotSatisfiedEvalCount,
        0 as candidateCount
    from {{ ref('transparency_factor_reason_passed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, factoruid
),
failed_summary as (
    select rundate, configid, factoruid, max(factorname) factorname,
        0 as C00_RecommendedAccountCount,
        0 as C01_SuccessUnlikelyAccountCount,
        0 as C02_LowExpectedValueNoAlternateAccountCount,
        0 as C03_ActorCapacityNoAlternateAccountCount,
        0 as C04_ChannelCapacityNoAlternateAccountCount,
        0 as C05_HorizonCapacityNoAlternateAccountCount,
        0 as C06_LowExpectedValueAccountCount,
        0 as C07_ActorCapacityAccountCount,
        0 as C08_ChannelCapacityAccountCount,
        0 as C09_HorizonCapacityAccountCount,
        0 as C51_SuppressionAccountCount,
        0 as C52_RepUnavailableAccountCount,
        0 as C53_RepAccountOnHoldAccountCount,
        0 as C54_RepProductNotAuthorizedAccountCount,
        0 as C55_RepTypeNotEnabledAccountCount,
        0 as C56_AccountTypeNotEnabledAccountCount,
        0 as C99_UnknownAccountCount,
        sum(coalesce(C71_AccountTypeMismatchAccountCount, 0)) as C71_AccountTypeMismatchAccountCount,
        sum(coalesce(C72_AttributesNotMatchAccountCount, 0)) as C72_AttributesNotMatchAccountCount,
        sum(coalesce(C73_FactorNoRuleAccountCount, 0)) as C73_FactorNoRuleAccountCount,
        sum(coalesce(C74_SegmentNotPassAccountCount, 0)) as C74_SegmentNotPassAccountCount,
        sum(coalesce(C75_AccountActorTypeMismatchAccountCount, 0)) as C75_AccountActorTypeMismatchAccountCount,
        sum(coalesce(C76_ChannelDisabledAccountCount, 0)) as C76_ChannelDisabledAccountCount,
        sum(coalesce(C77_DummyProductAccountCount, 0)) as C77_DummyProductAccountCount,
        sum(coalesce(C78_ProductNotSuggestibleAccountCount, 0)) as C78_ProductNotSuggestibleAccountCount,
        sum(coalesce(C79_NoAuthorizedRepAccountCount, 0)) as C79_NoAuthorizedRepAccountCount,
        sum(coalesce(C80_RepNotSuggestibleAccountCount, 0)) as C80_RepNotSuggestibleAccountCount,
        sum(coalesce(C81_RepNotAuthorizedAccountCount, 0)) as C81_RepNotAuthorizedAccountCount,
        sum(coalesce(C82_RepTypeMismatchAccountCount, 0)) as C82_RepTypeMismatchAccountCount,
        sum(coalesce(C83_ParentRuleNotSatisfiedAccountCount, 0)) as C83_ParentRuleNotSatisfiedAccountCount,
        sum(coalesce(C84_RuleNotSatisfiedAccountCount, 0)) as C84_RuleNotSatisfiedAccountCount,
        0 as C00_RecommendedEvalCount,
        0 as C01_SuccessUnlikelyEvalCount,
        0 as C02_LowExpectedValueNoAlternateEvalCount,
        0 as C03_ActorCapacityNoAlternateEvalCount,
        0 as C04_ChannelCapacityNoAlternateEvalCount,
        0 as C05_HorizonCapacityNoAlternateEvalCount,
        0 as C06_LowExpectedValueEvalCount,
        0 as C07_ActorCapacityEvalCount,
        0 as C08_ChannelCapacityEvalCount,
        0 as C09_HorizonCapacityEvalCount,
        0 as C51_SuppressionEvalCount,
        0 as C52_RepUnavailableEvalCount,
        0 as C53_RepAccountOnHoldEvalCount,
        0 as C54_RepProductNotAuthorizedEvalCount,
        0 as C55_RepTypeNotEnabledEvalCount,
        0 as C56_AccountTypeNotEnabledEvalCount,
        0 as C99_UnknownEvalCount,
        sum(coalesce(C71_AccountTypeMismatchEvalCount, 0)) as C71_AccountTypeMismatchEvalCount,
        sum(coalesce(C72_AttributesNotMatchEvalCount, 0)) as C72_AttributesNotMatchEvalCount,
        sum(coalesce(C73_FactorNoRuleEvalCount, 0)) as C73_FactorNoRuleEvalCount,
        sum(coalesce(C74_SegmentNotPassEvalCount, 0)) as C74_SegmentNotPassEvalCount,
        sum(coalesce(C75_AccountActorTypeMismatchEvalCount, 0)) as C75_AccountActorTypeMismatchEvalCount,
        sum(coalesce(C76_ChannelDisabledEvalCount, 0)) as C76_ChannelDisabledEvalCount,
        sum(coalesce(C77_DummyProductEvalCount, 0)) as C77_DummyProductEvalCount,
        sum(coalesce(C78_ProductNotSuggestibleEvalCount, 0)) as C78_ProductNotSuggestibleEvalCount,
        sum(coalesce(C79_NoAuthorizedRepEvalCount, 0)) as C79_NoAuthorizedRepEvalCount,
        sum(coalesce(C80_RepNotSuggestibleEvalCount, 0)) as C80_RepNotSuggestibleEvalCount,
        sum(coalesce(C81_RepNotAuthorizedEvalCount, 0)) as C81_RepNotAuthorizedEvalCount,
        sum(coalesce(C82_RepTypeMismatchEvalCount, 0)) as C82_RepTypeMismatchEvalCount,
        sum(coalesce(C83_ParentRuleNotSatisfiedEvalCount, 0)) as C83_ParentRuleNotSatisfiedEvalCount,
        sum(coalesce(C84_RuleNotSatisfiedEvalCount, 0)) as C84_RuleNotSatisfiedEvalCount,
         0 candidateCount
    from {{ ref('transparency_factor_reason_failed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, factoruid
),
both_summary as (
select * from passed_summary
union
select * from failed_summary
),
consolidated_summary as (
    select rundate, configid, factoruid, max(factorname) factorname,
        sum(C00_RecommendedAccountCount) as C00_RecommendedAccountCount,
        sum(C01_SuccessUnlikelyAccountCount) as C01_SuccessUnlikelyAccountCount,
        sum(C02_LowExpectedValueNoAlternateAccountCount) as C02_LowExpectedValueNoAlternateAccountCount,
        sum(C03_ActorCapacityNoAlternateAccountCount) as C03_ActorCapacityNoAlternateAccountCount,
        sum(C04_ChannelCapacityNoAlternateAccountCount) as C04_ChannelCapacityNoAlternateAccountCount,
        sum(C05_HorizonCapacityNoAlternateAccountCount) as C05_HorizonCapacityNoAlternateAccountCount,
        sum(C06_LowExpectedValueAccountCount) as C06_LowExpectedValueAccountCount,
        sum(C07_ActorCapacityAccountCount) as C07_ActorCapacityAccountCount,
        sum(C08_ChannelCapacityAccountCount) as C08_ChannelCapacityAccountCount,
        sum(C09_HorizonCapacityAccountCount) as C09_HorizonCapacityAccountCount,
        sum(C51_SuppressionAccountCount) as C51_SuppressionAccountCount,
        sum(C52_RepUnavailableAccountCount) as C52_RepUnavailableAccountCount,
        sum(C53_RepAccountOnHoldAccountCount) as C53_RepAccountOnHoldAccountCount,
        sum(C54_RepProductNotAuthorizedAccountCount) as C54_RepProductNotAuthorizedAccountCount,
        sum(C55_RepTypeNotEnabledAccountCount) as C55_RepTypeNotEnabledAccountCount,
        sum(C56_AccountTypeNotEnabledAccountCount) as C56_AccountTypeNotEnabledAccountCount,
        sum(C99_UnknownAccountCount) as C99_UnknownAccountCount,
        sum(C71_AccountTypeMismatchAccountCount) as C71_AccountTypeMismatchAccountCount,
        sum(C72_AttributesNotMatchAccountCount) as C72_AttributesNotMatchAccountCount,
        sum(C73_FactorNoRuleAccountCount) as C73_FactorNoRuleAccountCount,
        sum(C74_SegmentNotPassAccountCount) as C74_SegmentNotPassAccountCount,
        sum(C75_AccountActorTypeMismatchAccountCount) as C75_AccountActorTypeMismatchAccountCount,
        sum(C76_ChannelDisabledAccountCount) as C76_ChannelDisabledAccountCount,
        sum(C77_DummyProductAccountCount) as C77_DummyProductAccountCount,
        sum(C78_ProductNotSuggestibleAccountCount) as C78_ProductNotSuggestibleAccountCount,
        sum(C79_NoAuthorizedRepAccountCount) as C79_NoAuthorizedRepAccountCount,
        sum(C80_RepNotSuggestibleAccountCount) as C80_RepNotSuggestibleAccountCount,
        sum(C81_RepNotAuthorizedAccountCount) as C81_RepNotAuthorizedAccountCount,
        sum(C82_RepTypeMismatchAccountCount) as C82_RepTypeMismatchAccountCount,
        sum(C83_ParentRuleNotSatisfiedAccountCount) as C83_ParentRuleNotSatisfiedAccountCount,
        sum(C84_RuleNotSatisfiedAccountCount) as C84_RuleNotSatisfiedAccountCount,
        sum(C00_RecommendedEvalCount) as C00_RecommendedEvalCount,
        sum(C01_SuccessUnlikelyEvalCount) as C01_SuccessUnlikelyEvalCount,
        sum(C02_LowExpectedValueNoAlternateEvalCount) as C02_LowExpectedValueNoAlternateEvalCount,
        sum(C03_ActorCapacityNoAlternateEvalCount) as C03_ActorCapacityNoAlternateEvalCount,
        sum(C04_ChannelCapacityNoAlternateEvalCount) as C04_ChannelCapacityNoAlternateEvalCount,
        sum(C05_HorizonCapacityNoAlternateEvalCount) as C05_HorizonCapacityNoAlternateEvalCount,
        sum(C06_LowExpectedValueEvalCount) as C06_LowExpectedValueEvalCount,
        sum(C07_ActorCapacityEvalCount) as C07_ActorCapacityEvalCount,
        sum(C08_ChannelCapacityEvalCount) as C08_ChannelCapacityEvalCount,
        sum(C09_HorizonCapacityEvalCount) as C09_HorizonCapacityEvalCount,
        sum(C51_SuppressionEvalCount) as C51_SuppressionEvalCount,
        sum(C52_RepUnavailableEvalCount) as C52_RepUnavailableEvalCount,
        sum(C53_RepAccountOnHoldEvalCount) as C53_RepAccountOnHoldEvalCount,
        sum(C54_RepProductNotAuthorizedEvalCount) as C54_RepProductNotAuthorizedEvalCount,
        sum(C55_RepTypeNotEnabledEvalCount) as C55_RepTypeNotEnabledEvalCount,
        sum(C56_AccountTypeNotEnabledEvalCount) as C56_AccountTypeNotEnabledEvalCount,
        sum(C99_UnknownEvalCount) as C99_UnknownEvalCount,
        sum(C71_AccountTypeMismatchEvalCount) as C71_AccountTypeMismatchEvalCount,
        sum(C72_AttributesNotMatchEvalCount) as C72_AttributesNotMatchEvalCount,
        sum(C73_FactorNoRuleEvalCount) as C73_FactorNoRuleEvalCount,
        sum(C74_SegmentNotPassEvalCount) as C74_SegmentNotPassEvalCount,
        sum(C75_AccountActorTypeMismatchEvalCount) as C75_AccountActorTypeMismatchEvalCount,
        sum(C76_ChannelDisabledEvalCount) as C76_ChannelDisabledEvalCount,
        sum(C77_DummyProductEvalCount) as C77_DummyProductEvalCount,
        sum(C78_ProductNotSuggestibleEvalCount) as C78_ProductNotSuggestibleEvalCount,
        sum(C79_NoAuthorizedRepEvalCount) as C79_NoAuthorizedRepEvalCount,
        sum(C80_RepNotSuggestibleEvalCount) as C80_RepNotSuggestibleEvalCount,
        sum(C81_RepNotAuthorizedEvalCount) as C81_RepNotAuthorizedEvalCount,
        sum(C82_RepTypeMismatchEvalCount) as C82_RepTypeMismatchEvalCount,
        sum(C83_ParentRuleNotSatisfiedEvalCount) as C83_ParentRuleNotSatisfiedEvalCount,
        sum(C84_RuleNotSatisfiedEvalCount) as C84_RuleNotSatisfiedEvalCount,
        sum(candidateCount) candidateCount
    from both_summary
    group by rundate, configid, factoruid
),
totalCounts as (
    select rundate, configid, factoruid,
    (C00_RecommendedAccountCount + C01_SuccessUnlikelyAccountCount + C02_LowExpectedValueNoAlternateAccountCount +
    C03_ActorCapacityNoAlternateAccountCount + C04_ChannelCapacityNoAlternateAccountCount + C05_HorizonCapacityNoAlternateAccountCount +
    C06_LowExpectedValueAccountCount + C07_ActorCapacityAccountCount + C08_ChannelCapacityAccountCount + C09_HorizonCapacityAccountCount +
    C51_SuppressionAccountCount + C52_RepUnavailableAccountCount + C53_RepAccountOnHoldAccountCount + C54_RepProductNotAuthorizedAccountCount +
    C55_RepTypeNotEnabledAccountCount + C56_AccountTypeNotEnabledAccountCount + C99_UnknownAccountCount +
    C71_AccountTypeMismatchAccountCount + C72_AttributesNotMatchAccountCount + C73_FactorNoRuleAccountCount + C74_SegmentNotPassAccountCount +
    C75_AccountActorTypeMismatchAccountCount + C76_ChannelDisabledAccountCount + C77_DummyProductAccountCount + C78_ProductNotSuggestibleAccountCount +
    C79_NoAuthorizedRepAccountCount + C80_RepNotSuggestibleAccountCount + C81_RepNotAuthorizedAccountCount + C82_RepTypeMismatchAccountCount +
    C83_ParentRuleNotSatisfiedAccountCount + C84_RuleNotSatisfiedAccountCount) accountCountTotal,
    (C00_RecommendedEvalCount + C01_SuccessUnlikelyEvalCount + C02_LowExpectedValueNoAlternateEvalCount +
    C03_ActorCapacityNoAlternateEvalCount + C04_ChannelCapacityNoAlternateEvalCount + C05_HorizonCapacityNoAlternateEvalCount +
    C06_LowExpectedValueEvalCount + C07_ActorCapacityEvalCount + C08_ChannelCapacityEvalCount + C09_HorizonCapacityEvalCount +
    C51_SuppressionEvalCount + C52_RepUnavailableEvalCount + C53_RepAccountOnHoldEvalCount + C54_RepProductNotAuthorizedEvalCount +
    C55_RepTypeNotEnabledEvalCount + C56_AccountTypeNotEnabledEvalCount + C99_UnknownEvalCount +
    C71_AccountTypeMismatchEvalCount + C72_AttributesNotMatchEvalCount + C73_FactorNoRuleEvalCount + C74_SegmentNotPassEvalCount +
    C75_AccountActorTypeMismatchEvalCount + C76_ChannelDisabledEvalCount + C77_DummyProductEvalCount + C78_ProductNotSuggestibleEvalCount +
    C79_NoAuthorizedRepEvalCount + C80_RepNotSuggestibleEvalCount + C81_RepNotAuthorizedEvalCount + C82_RepTypeMismatchEvalCount +
    C83_ParentRuleNotSatisfiedEvalCount + C84_RuleNotSatisfiedEvalCount) evalCountTotal,
    C00_RecommendedAccountCount accountCountSuggested,
    candidateCount suggestionCount
    from consolidated_summary r
),
factor_last_suggested_date as (
    select r.rundate, r.configid, r.factoruid,
    date_parse(max(f.rundate),'%Y-%m-%d') last_suggested_date
    from consolidated_summary r
    left join {{ ref('factors_suggested_by_rundate') }} f
    on f.rundate <= r.rundate and r.factoruid = f.factoruid and r.configid = f.configid
    {% if is_incremental() %}
    where r.rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by r.rundate, r.configid, r.factoruid
)
select r.*, f.last_suggested_date, date_diff('day', f.last_suggested_date, date_parse(r.rundate,'%Y-%m-%d')) days_since_last_suggested, t.accountCountTotal, t.evalCountTotal, t.accountCountSuggested, t.suggestionCount, cas.repCount as configRepCountTotal, cas.accountCount as configAccountCountTotal
from consolidated_summary r
inner join totalCounts t
    on r.rundate = t.rundate
    and r.configid = t.configid
    and r.factoruid = t.factoruid
left join {{ ref('transparency_config_audience_v') }} cas
    on r.rundate = cas.rundate and r.configid = cas.configid
left join factor_last_suggested_date f
    on f.rundate = r.rundate and r.factoruid = f.factoruid and r.configid = f.configid
{% if is_incremental() %}
where r.rundate > (select max(rundate) from {{ this }})
{% endif %}