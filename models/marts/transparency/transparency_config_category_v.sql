{{ config(
    materialized='incremental',
    unique_key=['rundate', 'configid', 'status_category'],
    is_external=false,
    s3_data_naming='table_unique',
    format='parquet',
    write_compression='GZIP',
    table_type='iceberg',
    incremental_strategy = 'merge',
    on_schema_change = 'sync_all_columns',
    partition_by={
        "field": "rundate",
        "data_type": "date",
        "granularity": "day"
    }
) }}

with passed_summary as (
    select rundate,
        configid,
        status_category,
        sum(coalesce(accountCount, 0)) accountCount,
        sum(coalesce(actionCount, 0)) actionCount,
        sum(coalesce(evalCount, 0)) evalCount,
        sum(coalesce(candidateCount, 0)) candidateCount
    from {{ ref('transparency_config_category_passed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, status_category
),
failed_summary as (
    select rundate,
        configid,
        status_category,
        sum(coalesce(accountCount, 0)) accountCount,
        sum(coalesce(actionCount, 0)) actionCount,
        sum(coalesce(evalCount, 0)) evalCount,
        0 candidateCount
    from {{ ref('transparency_config_category_failed_v') }}
    {% if is_incremental() %}
    where rundate > (select max(rundate) from {{ this }})
    {% endif %}
    group by rundate, configid, status_category
),
both_summary as (
select * from passed_summary
union
select * from failed_summary
),
consolidated_summary as (
    select rundate, configid, status_category,
        sum(accountCount) accountCount,
        sum(actionCount) actionCount,
        sum(evalCount) evalCount,
        sum(candidateCount) candidateCount
    from both_summary
    group by rundate, configid, status_category
),
totalCounts as (
    select rundate, configid, sum(accountCount) accountCountTotal, sum(actionCount) actionCountTotal, sum(evalCount) evalCountTotal,
    sum(case when status_category = 'Recommended' then accountCount else 0 end) accountCountSuggested,
    sum(case when status_category = 'Recommended' then candidateCount else 0 end) suggestionCount
    from consolidated_summary r
    group by rundate, configid
)
select r.*, t.accountCountTotal, t.actionCountTotal, t.evalCountTotal, t.accountCountSuggested, t.suggestionCount, cas.repCount as configRepCountTotal, cas.accountCount as configAccountCountTotal
from consolidated_summary r
inner join totalCounts t
    on r.configid = t.configid
    and r.rundate = t.rundate
left join {{ ref('transparency_config_audience_v') }} cas
    on r.configid = cas.configid
    and r.rundate = cas.rundate

