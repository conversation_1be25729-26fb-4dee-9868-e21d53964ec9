{{ config(materialized='table') }}

SELECT
  a.msrscenariouid, 
  'first_touch' AS attribution_model,
  coalesce(a.first_interaction, 'NO INTERACTION') AS channel,
  round(count(*) *1.0 / (
     SELECT  COUNT(*)
     FROM {{ ref('msrscenario_event_sequence') }}
     WHERE conversion = true and msrscenariouid = a.msrscenariouid),
     2) AS attribution_percent
FROM {{ ref('msrscenario_event_sequence') }} a
WHERE a.conversion = true
GROUP BY a.msrscenariouid, coalesce(a.first_interaction, 'NO INTERACTION')
UNION
SELECT
  a.msrscenariouid,
  'last_touch' AS attribution_model,
  coalesce(a.last_interaction, 'NO INTERACTION') AS channel,
  round(count(*) * 1.0 /(
      SELECT COUNT(*)
      FROM {{ ref('msrscenario_event_sequence') }} 
      WHERE conversion = true and msrscenariouid = a.msrscenariouid),2) AS attribution_percent
FROM {{ ref('msrscenario_event_sequence') }} a 
WHERE conversion = true
GROUP BY a.msrscenariouid, coalesce(a.last_interaction, 'NO INTERACTION')
union
SELECT
  a.msrscenariouid,
  'first_suggestion' AS attribution_model,
  coalesce(a.first_suggestion, 'NO SUGGESTION') AS channel,
  round(count(*) *1.0 / (
     SELECT COUNT(*)
     FROM {{ ref('msrscenario_event_sequence') }} 
     WHERE conversion = true and msrscenariouid = a.msrscenariouid),
     2) AS attribution_percent
FROM {{ ref('msrscenario_event_sequence') }} a
WHERE conversion = true
GROUP BY a.msrscenariouid, coalesce(a.first_suggestion, 'NO SUGGESTION') 
union
SELECT
  a.msrscenariouid,
  'last_suggestion' AS attribution_model,
  coalesce(a.last_suggestion, 'NO SUGGESTION') AS channel,
  round(count(*) *1.0 / (
     SELECT COUNT(*)
     FROM {{ ref('msrscenario_event_sequence') }} 
     WHERE conversion = true and msrscenariouid = a.msrscenariouid),
     2) AS attribution_percent
FROM {{ ref('msrscenario_event_sequence') }} a
WHERE conversion = true
GROUP BY a.msrscenariouid, coalesce(a.last_suggestion, 'NO SUGGESTION');


