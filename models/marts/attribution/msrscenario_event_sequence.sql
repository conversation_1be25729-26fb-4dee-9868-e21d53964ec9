{{ config(materialized='table') }}

with all_msrscenario_daterange as
(
   select p.period_type, min(p.dt_begin) as dt_begin, max(p.dt_end) as dt_end from {{ ref('param_msrscenario_period_definition_v') }} p where p.period_type = 'Analysis Period' group by p.period_type
),
suggestion_candidate_usecases as
(
select accountid.externalid accountuid, i.productid.externalid as productuid,
  concat(channelinfo.actiontype.externalId, '-CANDIDATE') eventtypename,
  concat('KPI', channelinfo.actiontype.externalId, '-COMPLETED') intmatcheventtypename,
  primaryfactor.tags[1].tagname as usecasename,
  primaryfactor.name as factorname,
  cast(null as VARCHAR) messagetopic, 
  date_parse(suggesteddate, '%Y-%m-%d') as eventdatetimeutc,
  source.type as suggestiondriver,
  row_number() OVER (
      PARTITION BY accountid.externalid, i.productid.externalid,  channelinfo.actiontype.externalId,
                   date_parse(suggesteddate, '%Y-%m-%d')
      ORDER BY accountid.externalid, i.productid.externalid,  channelinfo.actiontype.externalId,
                   date_parse(suggesteddate, '%Y-%m-%d') desc, rundate desc 
  ) as candidate_order
  from  {{ ref('suggestion_candidates_v') }} 
      CROSS JOIN UNNEST(products) AS t(i) 
      inner join all_msrscenario_daterange p on p.period_type = 'Analysis Period'
      WHERE (p.period_type = 'Analysis Period' and date_parse(suggesteddate, '%Y-%m-%d') >= p.dt_begin and date_parse(suggesteddate, '%Y-%m-%d') <= p.dt_end) 
),
event_interaction_with_candidate as
(
  select a.accountuid,  i.productuid as productuid, a.eventtypename, a.factorname, coalesce(a.usecasename, s.usecasename) as usecasename, a.messagetopic, a.eventdatetimeutc, a.suggestiondriver,
          a.isconversionevent, a.conversionvalue, case when a.usecasename is null and s.usecasename is not null then 1 else 0 end is_candidate_usecase, date_diff('day', a.eventdatetimeutc, s.eventdatetimeutc) match_closeness,
  row_number() OVER (
      PARTITION BY a.accountuid, i.productuid, a.eventtypename, a.eventdatetimeutc  
      ORDER BY  a.accountuid, i.productuid, a.eventtypename, a.eventdatetimeutc, date_diff('day', a.eventdatetimeutc, s.eventdatetimeutc) desc
    ) as match_order
             from
            {{ ref('kpi_event_interaction') }} a CROSS JOIN UNNEST(product_details) AS t(i) 
            left join suggestion_candidate_usecases s on a.accountuid = s.accountuid and i.productuid = s.productuid and  a.eventtypename = s.intmatcheventtypename and s.candidate_order = 1 and (a.eventdatetimeutc between s.eventdatetimeutc - interval '1' day and s.eventdatetimeutc + interval '3' day)
            where a.usecasename is null OR s.usecasename is null
),
kpi_event_product_interaction as
(
  select accountuid, i.productuid as productuid, eventtypename, factorname, usecasename, i.messagetopic, eventdatetimeutc, suggestiondriver,
          isconversionevent, conversionvalue, 0 is_candidate_usecase from 
            {{ ref('kpi_event_interaction') }} CROSS JOIN UNNEST(product_details) AS t(i)           
          where usecasename is not null 
  union
  select accountuid, productuid, eventtypename, factorname, usecasename, messagetopic, eventdatetimeutc, suggestiondriver, isconversionevent, conversionvalue, is_candidate_usecase from 
        event_interaction_with_candidate  where match_order = 1
  union
  select accountuid, productuid, eventtypename, factorname, usecasename, messagetopic, eventdatetimeutc, suggestiondriver,
          isconversionevent, conversionvalue, 0 is_candidate_usecase from 
            {{ ref('kpi_event_interaction') }}  where eventtypename like '%STATECHANGE%'            
)
    SELECT
    sub2.msrscenariouid,
    sub2.accountuid,
    sub2.productuid,
    CASE
        WHEN sub2.conversion = true then concat('Start > ', sub2.path, ' > Conversion')
        ELSE concat('Start > ', sub2.path, ' > Null')
    END AS path,
    sub2.first_interaction AS first_interaction,
    sub2.last_interaction AS last_interaction,
    sub2.first_suggestion AS first_suggestion,
    sub2.last_suggestion AS last_suggestion,
    sub2.conversion AS conversion,
    sub2.event_order AS event_order,
    sub2.interaction_count,
    sub2.suggestion_count 
    from
    (
      SELECT
      sub.msrscenariouid,
      sub.accountuid,
      sub.productuid,
      array_agg(sub.channel) AS path_arr,
      array_join(filter(array_agg(sub.channel), q -> q not like '%STATECHANGE%'), ' > ') as path,
      element_at(filter(array_agg(sub.channel), q -> q not like '%SUGGESTED%' and q not like '%STATECHANGE%' ), 1) AS first_interaction,
      element_at(filter(array_agg(sub.channel), q -> q not like '%SUGGESTED%' and q not like '%STATECHANGE%' ), -1) AS last_interaction,
      element_at(filter(array_agg(sub.channel), q -> q like '%SUGGESTED%' ), 1) AS first_suggestion,
      element_at(filter(array_agg(sub.channel), q -> q like '%SUGGESTED%' ), -1) AS last_suggestion,
      cardinality(filter(array_agg(sub.channel), q -> q not like '%SUGGESTED%' and q not like '%STATECHANGE%' )) AS interaction_count,
      cardinality(filter(array_agg(sub.channel), q -> q like '%SUGGESTED%')) AS suggestion_count,
      CONTAINS(array_agg(sub.conversion), 1) AS conversion,
      array_agg(sub.event_order) AS event_order from
     (
          SELECT
          p.msrscenariouid,
          accountuid,
          productuid,
               case when (contains(t.usecase_level_interactions, eventtypename) and contains(t.topic_level_interactions, eventtypename) and ( (t.analyze_aligned_events  and is_candidate_usecase = 1) OR (t.analyze_driven_events  and is_candidate_usecase = 0))) and
               (coalesce(fm.usecase_name, usecasename) is not null) and (messagetopic is not null) then
                 concat(eventtypename,'(',coalesce(fm.usecase_name, usecasename),')','(',messagetopic,')')
               when (contains(t.usecase_level_interactions, eventtypename) and contains(t.topic_level_interactions, eventtypename) and ( (t.analyze_aligned_events  and is_candidate_usecase = 1) OR (t.analyze_driven_events and is_candidate_usecase = 0))) and 
               (coalesce(fm.usecase_name, usecasename) is not null) and (messagetopic is null) then
                 concat(eventtypename,'(',coalesce(fm.usecase_name, usecasename),')')
               when (contains(t.usecase_level_interactions, eventtypename) and contains(t.topic_level_interactions, eventtypename) and ( (t.analyze_aligned_events and is_candidate_usecase = 1) OR (t.analyze_driven_events and is_candidate_usecase = 0)) ) and
               (coalesce(fm.usecase_name, usecasename) is null) and (messagetopic is not null) then
                 concat(eventtypename,'(',messagetopic,')')
               when (contains(t.usecase_level_interactions, eventtypename) and not contains(t.topic_level_interactions, eventtypename) and ( (t.analyze_aligned_events  and is_candidate_usecase = 1) OR (t.analyze_driven_events and is_candidate_usecase = 0))) and
               (coalesce(fm.usecase_name, usecasename) is not null) then
                 concat(eventtypename,'(',coalesce(fm.usecase_name, usecasename),')')
               when ( not contains(t.usecase_level_interactions, eventtypename) and contains(t.topic_level_interactions, eventtypename) and ( (t.analyze_aligned_events and is_candidate_usecase = 1) OR (t.analyze_driven_events  and is_candidate_usecase = 0))) and
               (messagetopic is not null) then
                 concat(eventtypename,'(',messagetopic,')')
               else
                 eventtypename
               end as channel,
          eventdatetimeutc,
          isconversionevent as conversion,
          conversionvalue,
          row_number() OVER (
            PARTITION BY accountuid, productuid
            ORDER BY eventdatetimeutc asc
          ) as event_order
          FROM
            kpi_event_product_interaction inner join {{ ref('param_msrscenario_period_definition_v') }} p on p.period_type = 'Analysis Period'
            inner join {{  ref('param_msrscenario_period_definition_v') }} q on q.msrscenariouid = p.msrscenariouid and q.period_type = 'Conversion Period'
            inner join {{  ref('param_msrscenario_events_v') }} t on t.msrscenariouid = p.msrscenariouid
            left join {{  ref('param_msrscenario_factorusecasename_map_override_v') }} fm on p.msrscenariouid = fm.msrscenariouid and 
                          kpi_event_product_interaction.factorname = fm.factor_name
          WHERE (p.period_type = 'Analysis Period' and eventdatetimeutc >= p.dt_begin and eventdatetimeutc <= p.dt_end and contains(t.interaction_events, eventtypename) ) OR
                (q.period_type = 'Conversion Period' and eventdatetimeutc >= q.dt_begin and eventdatetimeutc <= q.dt_end and contains(t.conversion_events, eventtypename))
      ) AS sub
    GROUP BY sub.msrscenariouid, sub.accountuid, sub.productuid
    HAVING cardinality(filter(array_agg(sub.channel), q -> q not like '%STATECHANGE%')) > 0
    ) AS sub2
