{{ config(materialized='table') }}

with path_as_array as (
  SELECT
        msrscenariouid,
        accountuid,
        productuid,
        first_interaction,
        last_interaction,
        first_suggestion,
        last_suggestion,
        conversion,
        interaction_count,
        suggestion_count,
        split(path, ' > ') path_arr
     FROM {{ ref('msrscenario_event_sequence') }}
), path_unnest as
(
        select
        msrscenariouid,
        accountuid,
        productuid,
        first_interaction,
        last_interaction,
        first_suggestion,
        last_suggestion,
        conversion,
        interaction_count,
        suggestion_count,
        idx,
        channel
        from path_as_array
        cross join
        UNNEST(path_arr) WITH ORDINALITY t(channel, idx)
), 
path_temp_segment as
(
select msrscenariouid, accountuid, productuid, idx, first_interaction, last_interaction, first_suggestion, last_suggestion, conversion, interaction_count, suggestion_count, channel, LEAD(channel) OVER( PARTITION BY msrscenariouid, accountuid, productuid  order by idx ) as next_channel, 1 AS cnt
from path_unnest
order by msrscenariouid, accountuid, productuid, idx
), path_segment as
(
select path_temp_segment.msrscenariouid, accountuid, productuid, idx, first_interaction, last_interaction, first_suggestion, last_suggestion, conversion, interaction_count, suggestion_count, concat(channel, ' > ', next_channel) as transition, 1 AS cnt
from path_temp_segment  inner join {{ ref("param_msrscenario_events_v") }} e on path_temp_segment.msrscenariouid = e.msrscenariouid
where (e.remove_same_state_transitions and channel <> next_channel) or (NOT e.remove_same_state_transitions)
order by path_temp_segment.msrscenariouid, accountuid, productuid, idx
)
select msrscenariouid, accountuid, productuid, idx, first_interaction, last_interaction, first_suggestion, last_suggestion, conversion, interaction_count, suggestion_count, transition, cnt
from path_segment
where transition is not null;
