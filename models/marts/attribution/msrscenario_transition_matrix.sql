{{ config(materialized='table') }}

SELECT
  left_table.msrscenariouid,
  left_table.start_state,
  left_table.end_state,
  left_table.total_transitions * 1.0 / right_table.total_state_transitions_initiated_from_start_state AS transition_probability
FROM
  (
    SELECT
      msrscenariouid,
      transition,
      sum(cnt) total_transitions,
      trim(SPLIT(transition, ' > ') [1]) start_state,
      trim(SPLIT(transition, ' > ') [2]) end_state
    FROM
      {{ ref('msrscenario_markov_transitions') }}
    GROUP BY
      msrscenariouid, transition
    ORDER BY
      msrscenariouid, transition
  ) left_table
  JOIN (
    SELECT
      a.msrscenariouid,
      a.start_state,
      sum(a.cnt) total_state_transitions_initiated_from_start_state
    FROM
      (
        SELECT
          msrscenariouid,
          trim(SPLIT(transition, ' > ') [1]) start_state,
          cnt
        FROM
          {{ ref('msrscenario_markov_transitions') }}
      ) AS a
    GROUP BY
      a.msrscenariouid, a.start_state
  ) right_table ON left_table.msrscenariouid = right_table.msrscenariouid and left_table.start_state = right_table.start_state
ORDER BY
  msrscenariouid, end_state DESC;

