{{ config(materialized = 'view') }}

-- with strategic_coverage as (SELECT
--     e.recordType,
--     e.msrscenariouid,
--     e.eventdatetimeutc,
--     e.interactionuid,
--     e.accountuid,
--     e.productuid,
--     p.productname,
--     e.segmentname,
--     e.actoruid,
--     at.seConfigId,
--     at.configCountryCode,
--     at.configCountryCode configCountryName,
--     at.repteamid,
--     at.repteamname,
--     aty.repTypeId,
--     aty.repTypeName,
--     e.eventtypename,
--     e.factoruid,
--     e.factorname,
--     e.usecasename,
--     e.suggestiondriver,
--     e.suggestionreferenceid,
--     e.internalsuggestionreferenceid,
--     e.mergeid,
--     acm_i.channelid interaction_channelid,
--     acm_i.channelname interaction_channelname,
--     acm_s.channelid suggestion_channelid,
--     acm_s.channelname suggestion_channelname,
--     e.actiontaken,
--     e.issuggestioncompleteddirect,
--     e.issuggestioncompletedinfer,
--     e.is_candidate_usecase,
--     e.is_candidate_same_product,
--     e.is_candidate_same_event_type,
--     e.insight_matchclosenessdays,
--     e.insight_viewcount,
--     e.event_order,
--     e.repActionTypeId,
--     e.detailrepactiontypeid,
--     case
--         when e.issuggestioncompleteddirect = 1 then 101
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'Suggestions Completed' then 102
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'Suggestions Dismissed' then 202
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'No Action Taken' then 203
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and e.insight_viewcount > 0 then 301
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 1
--           and e.is_candidate_same_event_type = 1 then 401
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 1 then 402
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 1
--           and e.is_candidate_same_event_type = 0 then 403
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is not null then 404
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 0 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is null then 501
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is null then 502
--         else 0
--     end as strategicActionCategoryId,
--     case
--         when e.issuggestioncompleteddirect = 1 then 'DIRECT-ACCEPTED-COMPLETED'
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'Suggestions Completed' then 'DIRECT-MARKASCOMPLETE-COMPLETED'
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'Suggestions Dismissed' then 'INDIRECT-DISMISS-COMPLETED'
--         when e.issuggestioncompletedinfer = 1
--           and e.actiontaken = 'No Action Taken' then 'INDIRECT-NO-ACTION-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and e.insight_viewcount > 0 then 'INSIGHTREFERRED-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 1
--           and e.is_candidate_same_event_type = 1 then 'CANDIDATE_ACCOUNT_REP_PRODUCT_ACTION_MATCH-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 1 then 'CANDIDATE_ACCOUNT_REP_ACTION_MATCH-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 1
--           and e.is_candidate_same_event_type = 0 then 'CANDIDATE_ACCOUNT_REP_PRODUCT_MATCH-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is not null then 'CANDIDATE_ACCOUNT_REP_MATCH-COMPLETED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 0
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is null then 'SUGGESTION_NOT_REACHED'
--         when coalesce(e.issuggestioncompleteddirect,0) = 0
--           and coalesce(e.issuggestioncompletedinfer,0) = 0
--           and coalesce(e.insight_viewcount, 0) = 0
--           and e.is_candidate_usecase = 1 
--           and e.is_candidate_same_product = 0
--           and e.is_candidate_same_event_type = 0 
--           and e.interactionuid is null then 'CANDIDATE_NOT_REACHED'
--         else 'NOT_STRATEGIC'
--     end as strategicActionCategoryUID,
--     eng.engagementsegment_median as engagementsegment,
--     engm.engagementsegment as engagementsegment_monthly,
--     engq.engagementsegment_median as engagementsegment_quarterly,
--     eng.median_engagementrate as engagementrate,
--     engm.engagementrate as engagementrate_monthly,
--     engq.median_engagementrate as engagementrate_quarterly,
--     eng.median_bucket_engagementrate as median_bucket_engagementrate,
--     engm.median_bucket_engagementrate as median_bucket_engagementrate_monthly,
--     engq.median_bucket_engagementrate as median_bucket_engagementrate_quarterly,
--     params.driven_category_ids,
--     params.partially_driven_category_ids,
--     params.aligned_category_ids,
--     e.call_type,
--     e.call_channel_raw,
--     e.call_channel_category
-- from
--     {{ ref('msrscenario_events') }} e
--     left join {{ ref('actor_v') }} a on a.externalid = e.actoruid
--     left join {{ ref('imp_repengagementsegmentation_v') }} eng on e.actoruid = eng.repuid 
--     left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on e.actoruid = engm.repuid and date_trunc('month', e.eventdatetimeutc)  = engm.periodvalue
--     left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on e.actoruid = engq.repuid and date_trunc('quarter', e.eventdatetimeutc)  = engq.periodvalue
--     left join {{ ref('actorteamactor_v') }} aa on aa.repid = a.repid
--     left join {{ ref('actorteam_v') }} at on aa.repteamid = at.repteamid
--     left join {{ ref('actortype_v') }} aty on a.reptypeid = aty.reptypeid
--     left join {{ ref('actionchannelmap_v') }} acm_i on acm_i.actortypeid = a.reptypeid and acm_i.actiontypeid = e.repActionTypeId
--     left join {{ ref('actionchannelmap_v') }} acm_s on acm_s.actortypeid = a.reptypeid and acm_s.actiontypeid = e.detailRepActionTypeId
--     left join {{ ref('product_v') }} p on p.externalid = e.productuid
--     left join {{ ref('param_msrscenario_driven_conditions_v') }} params on params.msrscenariouid = e.msrscenariouid 
-- where
--   params.msrscenariouid = 'default'
--   and e.recordType = params.interaction_level_of_detail 
-- )
-- select c.*, 
-- case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then 1 else 0 end driven,
-- case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then 1 else 0 end partially_driven,
-- case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then 1 else 0 end aligned,
-- case when strategicActionCategoryId = 0 then 1 else 0 end not_strategic,
-- case when strategicActionCategoryId = 501 then 1 else 0 end not_reached,
-- case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then '1' 
--   when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then '2' 
--   when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then '3'
--   when strategicActionCategoryId in (501, 502) then '4'
--   else '5' end strategicCategoryGroupUid,
-- case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then '1.Driven' 
--   when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then '2.Partially-Driven' 
--   when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then '3.Aligned'
--   when strategicActionCategoryId in (501, 502) then '4.Not-reached'
--   else '5.Not-Strategic' end strategicCategoryGroup
-- from strategic_coverage c
-- ;

select
    recordType,
    msrscenariouid,
    eventdatetimeutc,
    interactionuid,
    accountuid,
    productuid,
    productname,
    segmentname,
    actoruid,
    seConfigId,
    configCountryCode,
    configCountryName,
    repteamid,
    repteamname,
    repTypeId,
    repTypeName,
    eventtypename,
    factoruid,
    factorname,
    usecasename,
    suggestiondriver,
    suggestionreferenceid,
    internalsuggestionreferenceid,
    mergeid,
    interaction_channelid,
    interaction_channelname,
    suggestion_channelid,
    suggestion_channelname,
    actiontaken,
    issuggestioncompleteddirect,
    issuggestioncompletedinfer,
    is_candidate_usecase,
    is_candidate_same_product,
    is_candidate_same_event_type,
    insight_matchclosenessdays,
    insight_viewcount,
    event_order,
    repActionTypeId,
    detailrepactiontypeid,
    strategicActionCategoryId,
    strategicActionCategoryUID,
    engagementsegment,
    engagementsegment_monthly,
    engagementsegment_quarterly,
    engagementrate,
    engagementrate_monthly,
    engagementrate_quarterly,
    median_bucket_engagementrate,
    median_bucket_engagementrate_monthly,
    median_bucket_engagementrate_quarterly,
    driven_category_ids,
    partially_driven_category_ids,
    aligned_category_ids,
    call_type,
    call_channel_raw,
    call_channel_category,
    driven,
    partially_driven,
    aligned,
    not_strategic,
    not_reached,
    strategicCategoryGroupUid,
    strategicCategoryGroup

from {{ ref('msrscenario_events') }}