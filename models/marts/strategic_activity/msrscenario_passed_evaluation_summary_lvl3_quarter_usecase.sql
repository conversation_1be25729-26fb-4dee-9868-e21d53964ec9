{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_quarter, tagName),
        (factorname, event_quarter, tagName, channel),
        (factorname, event_quarter, tagName, configCountryCode),
        (factorname, event_quarter, tagName, productName),
        (factorname, event_quarter, tagName, repTeamName),
        (factorname, event_quarter, tagName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, configCountryCode),
        (factorname, event_quarter, tagName, channel, productName)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_quarter, tagName, channel, repTeamName),
        (factorname, event_quarter, tagName, channel, strategicActionCategoryId),
        (factorname, event_quarter, tagName, configCountryCode, productName),
        (factorname, event_quarter, tagName, configCountryCode, repTeamName),
        (factorname, event_quarter, tagName, configCountryCode, strategicActionCategoryId),
        (factorname, event_quarter, tagName, productName, repTeamName),
        (factorname, event_quarter, tagName, productName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, repTeamName, strategicActionCategoryId)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_quarter, tagName, channel, configCountryCode, productName),
        (factorname, event_quarter, tagName, channel, configCountryCode, repTeamName),
        (factorname, event_quarter, tagName, channel, configCountryCode, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, productName, repTeamName),
        (factorname, event_quarter, tagName, channel, productName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, configCountryCode, productName, repTeamName),
        (factorname, event_quarter, tagName, configCountryCode, productName, strategicActionCategoryId)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_quarter, tagName, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, configCountryCode, productName, repTeamName),
        (factorname, event_quarter, tagName, channel, configCountryCode, productName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, configCountryCode, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, tagName, channel, configCountryCode, productName, repTeamName, strategicActionCategoryId)
    )
