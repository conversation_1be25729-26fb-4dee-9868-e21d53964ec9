{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    'Not used' as event_quarter,
    '604' as strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    case when GROUPING(tagName) = 0 then tagName else 'All usecases' end as tagName,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    0 as account_cnt,
    0 as row_cnt
from
    {{ ref('msrscenario_all_evaluations_lvl3') }} a
where a.status = 0
group by
    grouping sets ((channel, configCountryCode, repTeamName, factorName, tagname, event_yearmonth),
                (configCountryCode, repTeamName, factorName, tagname, event_yearmonth),
                (channel, repTeamName, factorName, tagname, event_yearmonth),
                (channel, configCountryCode, factorName, tagname, event_yearmonth),
                (repTeamName, factorName, tagname, event_yearmonth),
                (channel, factorName, tagname, event_yearmonth),
                (configCountryCode, factorName, tagname, event_yearmonth),
                (factorName, tagname, event_yearmonth),
                (productName, channel, configCountryCode, repTeamName, factorName, tagname, event_yearmonth),
                (productName, configCountryCode, repTeamName, factorName, tagname, event_yearmonth),
                (productName, channel, repTeamName, factorName, tagname, event_yearmonth),
                (productName, channel, configCountryCode, factorName, tagname, event_yearmonth),
                (productName, repTeamName, factorName, tagname, event_yearmonth),
                (productName, channel, factorName, tagname, event_yearmonth),
                (productName, configCountryCode, factorName, tagname, event_yearmonth),
                (productName, factorName, tagname, event_yearmonth),
                -- strategy VS usecase
                (channel, configCountryCode, repTeamName, factorName, strategyname, event_yearmonth),
                (configCountryCode, repTeamName, factorName, strategyname, event_yearmonth),
                (channel, repTeamName, factorName, strategyname, event_yearmonth),
                (channel, configCountryCode, factorName, strategyname, event_yearmonth),
                (repTeamName, factorName, strategyname, event_yearmonth),
                (channel, factorName, strategyname, event_yearmonth),
                (configCountryCode, factorName, strategyname, event_yearmonth),
                (factorName, strategyname, event_yearmonth),
                (productName, channel, configCountryCode, repTeamName, factorName, strategyname, event_yearmonth),
                (productName, configCountryCode, repTeamName, factorName, strategyname, event_yearmonth),
                (productName, channel, repTeamName, factorName, strategyname, event_yearmonth),
                (productName, channel, configCountryCode, factorName, strategyname, event_yearmonth),
                (productName, repTeamName, factorName, strategyname, event_yearmonth),
                (productName, channel, factorName, strategyname, event_yearmonth),
                (productName, configCountryCode, factorName, strategyname, event_yearmonth),
                (productName, factorName, strategyname, event_yearmonth)
    )
having count(1) > 0

