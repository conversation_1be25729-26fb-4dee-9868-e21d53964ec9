{{ config(materialized='table') }}

with apptracking_insight_viewed as 
(
    select
        accountuid,
        repuid,
        date_trunc('day', datetime) as eventdatetimeutc,
        count(*) insight_viewcount
    from {{ ref('akt_apptracking_v') }}
    inner join {{ ref('all_msrscenario_daterange_v') }} p on p.period_type = 'Analysis Period'
    where (p.period_type = 'Analysis Period'
        and datetime >= p.dt_begin and datetime <= p.dt_end) 
    group by
        accountuid,
        repuid,
        date_trunc('day', datetime)
),
candidate_insight_matching_days as
(
select 
coalesce(candidate_matching_pre_days, -1) candidate_matching_pre_days,
coalesce(candidate_matching_post_days, 3) candidate_matching_post_days,
coalesce(insight_matching_pre_days, -1) insight_matching_pre_days,
coalesce(insight_matching_post_days, 3) insight_matching_post_days
from  {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),

kpi_event_product_interaction as
(
    select
        recordType,
        accountuid,
        productuid as productuid,
        segmentname,
        repuid as actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        strategyname,
        eventdatetimeutc,
        suggestiondriver,
        isconversionevent,
        conversionvalue,
        0 is_candidate_usecase,
        suggestionreferenceid,
        internalsuggestionreferenceid,
        actiontaken,
        issuggestioncompleteddirect,
        issuggestioncompletedinfer,
        interactionuid,
        mergeid,
        repActionTypeId,
        detailrepactiontypeid,
        0 is_candidate_same_product,
        0 is_candidate_same_event_type,
        call_type,
        call_channel_raw,
        call_channel_category
    from {{ ref('kpi_event_interaction') }}           
    where suggestionreferenceid is not null
      and recordType in ('Interaction level', 'Interaction-factor level', 'Interaction-factor-product level')
    union all
    select
        recordType,
        accountuid,
        productuid,
        segmentname,
        actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        strategyname,
        eventdatetimeutc,
        suggestiondriver,
        isconversionevent,
        conversionvalue,
        is_candidate_usecase, 
        suggestionreferenceid,
        internalsuggestionreferenceid,
        actiontaken,
        issuggestioncompleteddirect,
        issuggestioncompletedinfer,
        interactionuid,
        mergeid,
        repActionTypeId,
        detailrepactiontypeid,
        is_candidate_same_product,
        is_candidate_same_event_type,
        call_type,
        call_channel_raw,
        call_channel_category
    from  {{ ref('msrscenario_event_interaction_with_candidate') }} 
    
),
kpi_apptracking_insight_viewed as
(
    select
        b.interactionuid,
        b.productuid,
        b.accountuid,
        b.actoruid,
        b.eventdatetimeutc,
        date_diff('day', a.eventdatetimeutc, b.eventdatetimeutc) insight_matchclosenessdays,
        a.insight_viewcount
    from kpi_event_product_interaction b
    cross join candidate_insight_matching_days d
    left join apptracking_insight_viewed a
        on b.accountuid = a.accountuid
       and b.actoruid = a.repuid
       and  b.eventdatetimeutc between date_add('day', d.insight_matching_pre_days, a.eventdatetimeutc) and date_add('day', d.insight_matching_post_days, a.eventdatetimeutc)
),

strategic_coverage as (
select
    recordType,
    p.msrscenariouid,
    kpi_event_product_interaction.interactionuid,
    kpi_event_product_interaction.accountuid,
    kpi_event_product_interaction.productuid,
    kpi_event_product_interaction.segmentname,
    kpi_event_product_interaction.actoruid,
    kpi_event_product_interaction.mergeid,
    eventtypename,
    factoruid,
    factorname,
    coalesce(fm.usecase_name, usecasename) usecasename,
    kpi_event_product_interaction.strategyname,
    kpi_event_product_interaction.eventdatetimeutc,
    date_format(kpi_event_product_interaction.eventdatetimeutc, '%Y-%m') event_yearmonth,
    concat(date_format(kpi_event_product_interaction.eventdatetimeutc, '%Y'), '-Q', cast(quarter(kpi_event_product_interaction.eventdatetimeutc) as varchar)) event_quarter,
    suggestiondriver,
    suggestionreferenceid,
    internalsuggestionreferenceid,
    actiontaken,
    issuggestioncompleteddirect,
    issuggestioncompletedinfer,    
    is_candidate_usecase,
    is_candidate_same_product,
    is_candidate_same_event_type,
    row_number() OVER (
        PARTITION BY
            kpi_event_product_interaction.accountuid,
            kpi_event_product_interaction.productuid
        ORDER BY
            kpi_event_product_interaction.eventdatetimeutc asc) as event_order,
    v.insight_matchclosenessdays,
    v.insight_viewcount,
    repActionTypeId,
    detailrepactiontypeid,
    kpi_event_product_interaction.call_type,
    kpi_event_product_interaction.call_channel_raw,
    kpi_event_product_interaction.call_channel_category,
    coalesce(product.productname, 'Not product level') as productname,
    at.seConfigId,
    at.configCountryCode,
    at.configCountryCode configCountryName,
    at.repteamid,
    at.repteamname,
    aty.repTypeId,
    aty.repTypeName,
    case when ictv.call_channel_dse_id is not null then ictv.call_channel_dse_id 
     else acm_i.channelid end as interaction_channelid,
    case when ictv.call_channel_dse is not null then ictv.call_channel_dse
     else acm_i.channelname end as interaction_channelname,
    acm_s.channelid suggestion_channelid,
    acm_s.channelname suggestion_channelname,
    case
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompleteddirect = 1 then 101
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'Suggestions Completed' then 102
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'Suggestions Dismissed' then 202
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'No Action Taken' then 203
        when kpi_event_product_interaction.interactionuid is not null
          and coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and insight_viewcount > 0 then 301
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 1
          and is_candidate_same_event_type = 1 then 401
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 1 then 402
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 1
          and is_candidate_same_event_type = 0 then 403
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is not null then 404
        when kpi_event_product_interaction.suggestionreferenceid is not null
          and is_candidate_usecase = 0 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is null then 501
        when kpi_event_product_interaction.suggestionreferenceid is not null
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is null then 502
        else 0
    end as strategicActionCategoryId,
    case
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompleteddirect = 1 then 'DIRECT-ACCEPTED-COMPLETED'
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'Suggestions Completed' then 'DIRECT-MARKASCOMPLETE-COMPLETED'
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'Suggestions Dismissed' then 'INDIRECT-DISMISS-COMPLETED'
        when kpi_event_product_interaction.interactionuid is not null
        and issuggestioncompletedinfer = 1
          and actiontaken = 'No Action Taken' then 'INDIRECT-NO-ACTION-COMPLETED'
        when kpi_event_product_interaction.interactionuid is not null
          and coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and insight_viewcount > 0 then 'INSIGHTREFERRED-COMPLETED'
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 1
          and is_candidate_same_event_type = 1 then 'CANDIDATE_ACCOUNT_REP_PRODUCT_ACTION_MATCH-COMPLETED'
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 1 then 'CANDIDATE_ACCOUNT_REP_ACTION_MATCH-COMPLETED'
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 1
          and is_candidate_same_event_type = 0 then 'CANDIDATE_ACCOUNT_REP_PRODUCT_MATCH-COMPLETED'
        when coalesce(issuggestioncompleteddirect,0) = 0
          and coalesce(issuggestioncompletedinfer,0) = 0
          and coalesce(insight_viewcount, 0) = 0
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is not null then 'CANDIDATE_ACCOUNT_REP_MATCH-COMPLETED'
        when kpi_event_product_interaction.suggestionreferenceid is not null
          and is_candidate_usecase = 0
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is null then 'SUGGESTION_NOT_REACHED'
        when kpi_event_product_interaction.suggestionreferenceid is not null
          and is_candidate_usecase = 1 
          and is_candidate_same_product = 0
          and is_candidate_same_event_type = 0 
          and kpi_event_product_interaction.interactionuid is null then 'CANDIDATE_NOT_REACHED'
        else 'NOT_STRATEGIC'
    end as strategicActionCategoryUID,
    coalesce(eng.engagementsegment_median, 'Unknown') as engagementsegment,
    coalesce(engm.engagementsegment, 'Unknown') as engagementsegment_monthly,
    coalesce(engq.engagementsegment_median, 'Unknown') as engagementsegment_quarterly,
    coalesce(eng.median_engagementrate, 0) as engagementrate,
    coalesce(engm.engagementrate, 0) as engagementrate_monthly,
    coalesce(engq.median_engagementrate, 0) as engagementrate_quarterly,
    coalesce(eng.median_bucket_engagementrate, 0) as median_bucket_engagementrate,
    coalesce(engm.median_bucket_engagementrate, 0) as median_bucket_engagementrate_monthly,
    coalesce(engq.median_bucket_engagementrate, 0) as median_bucket_engagementrate_quarterly,
    params.driven_category_ids,
    params.partially_driven_category_ids,
    params.aligned_category_ids

from kpi_event_product_interaction
inner join {{ ref('param_msrscenario_period_definition_v') }} p
    on p.period_type = 'Analysis Period'
inner join {{ ref('param_msrscenario_period_definition_v') }} q
    on q.msrscenariouid = p.msrscenariouid
   and q.period_type = 'Conversion Period'
inner join {{ ref('param_msrscenario_events_v') }} t
    on t.msrscenariouid = p.msrscenariouid
left join kpi_apptracking_insight_viewed v
    on kpi_event_product_interaction.interactionuid = v.interactionuid
   and kpi_event_product_interaction.accountuid = v.accountuid
   and kpi_event_product_interaction.actoruid = v.actoruid 
   and kpi_event_product_interaction.productuid = v.productuid 
left join {{ ref('param_msrscenario_factorusecasename_map_override_v') }} fm
    on p.msrscenariouid = fm.msrscenariouid
   and kpi_event_product_interaction.factorname = fm.factor_name 

left join {{ ref('actor_v') }} a on a.externalid = kpi_event_product_interaction.actoruid
left join {{ ref('imp_repengagementsegmentation_v') }} eng on kpi_event_product_interaction.actoruid = eng.repuid 
left join {{ ref('imp_repengagementsegmentation_monthly_v') }} engm on kpi_event_product_interaction.actoruid = engm.repuid and date_trunc('month', kpi_event_product_interaction.eventdatetimeutc)  = engm.periodvalue
left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on kpi_event_product_interaction.actoruid = engq.repuid and date_trunc('quarter', kpi_event_product_interaction.eventdatetimeutc)  = engq.periodvalue
left join {{ ref('actorteamactor_v') }} aa on aa.repid = a.repid
left join {{ ref('actorteam_v') }} at on aa.repteamid = at.repteamid
left join {{ ref('actortype_v') }} aty on a.reptypeid = aty.reptypeid
left join {{ ref('actionchannelmap_v') }} acm_i on acm_i.actortypeid = a.reptypeid and acm_i.actiontypeid = kpi_event_product_interaction.repActionTypeId
left join {{ ref('actionchannelmap_v') }} acm_s on acm_s.actortypeid = a.reptypeid and acm_s.actiontypeid = kpi_event_product_interaction.detailRepActionTypeId
left join {{ ref('product_v') }} product on product.externalid = kpi_event_product_interaction.productuid
left join {{ ref('param_msrscenario_driven_conditions_v') }} params on params.msrscenariouid = p.msrscenariouid 
left join {{ ref('interaction_call_type_v') }} ictv on kpi_event_product_interaction.interactionuid = ictv.interactionuid 

where ((
        p.period_type = 'Analysis Period'
    and kpi_event_product_interaction.eventdatetimeutc >= p.dt_begin
    and kpi_event_product_interaction.eventdatetimeutc <= p.dt_end
    --and contains(t.interaction_events, eventtypename)
      ) 
   or (
        q.period_type = 'Conversion Period'
    and kpi_event_product_interaction.eventdatetimeutc >= q.dt_begin
    and kpi_event_product_interaction.eventdatetimeutc <= q.dt_end
    --and contains(t.conversion_events, eventtypename)
      ))
    and (
        params.msrscenariouid = 'default'
        and kpi_event_product_interaction.recordType = params.interaction_level_of_detail
    )
)

select c.*, f.factorid,
case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then 1 else 0 end driven,
case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then 1 else 0 end partially_driven,
case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then 1 else 0 end aligned,
case when strategicActionCategoryId = 0 then 1 else 0 end not_strategic,
case when strategicActionCategoryId = 501 then 1 else 0 end not_reached,
case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then '1' 
  when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then '2' 
  when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then '3'
  when strategicActionCategoryId in (501, 502) then '4'
  else '5' end strategicCategoryGroupUid,
case when position(',' || cast(strategicActionCategoryId as varchar) || ',' in driven_category_ids) > 0 then '1.Driven' 
  when position(',' || cast(strategicActionCategoryId as varchar) || ',' in partially_driven_category_ids) > 0 then '2.Partially-Driven' 
  when position(',' || cast(strategicActionCategoryId as varchar) || ',' in aligned_category_ids) > 0 then '3.Aligned'
  when strategicActionCategoryId in (501, 502) then '4.Not-reached'
  else '5.Not-Strategic' end strategicCategoryGroup,
case when strategicActionCategoryId < 500 then interaction_channelname else suggestion_channelname end as channel

from strategic_coverage c
join {{ ref('factor_id_name_map') }} f on c.factoruid = f.factoruid
