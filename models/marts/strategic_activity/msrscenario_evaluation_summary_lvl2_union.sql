{{ config(materialized='view') }}

with passed_eval_agg_groupuid as (
select a.*
from {{ ref('msrscenario_passed_evaluation_summary_lvl2') }} a
where a.strategicCategoryGroupUid = '600'
)


select a.*
from {{ ref('msrscenario_passed_evaluation_summary_lvl2') }} a

union all

select
    coalesce(a.factorName, b.factorName) factorName,
    coalesce(a.channel, b.channel) channel,
    coalesce(a.event_date, b.event_date) event_date,
    coalesce(a.event_quarter, b.event_quarter) event_quarter,
    '600' as strategicCategoryGroupUid,
    coalesce(a.configCountryCode, b.configCountryCode) configCountryCode,
    coalesce(a.repTeamName, b.repTeamName) repTeamName,
    coalesce(a.tagName, b.tagName) tagName,
    coalesce(a.strategyname, b.strategyname) strategyname,
    coalesce(b.account_cnt, 0) as account_cnt,
    coalesce(b.row_cnt, 0) as row_cnt

from {{ ref('msrscenario_failed_evaluation_summary_lvl2') }} b
left join passed_eval_agg_groupuid a
on a.factorName = b.factorName
and a.channel = b.channel
and a.event_date = b.event_date
and a.event_quarter = b.event_quarter
and a.configCountryCode = b.configCountryCode
and a.repTeamName = b.repTeamName
and a.tagName = b.tagName
and a.strategyname = b.strategyname
where a.factorName is null