{{ config(materialized='table') }}

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            (channel, configCountryCode, repTeamName, productName, strategyname, factorname, event_yearmonth),
            (configCountryCode, repTeamName, productName, strategyname, factorname, event_yearmonth),
            (channel, repTeamName, productName, strategyname, factorname, event_yearmonth),
            (channel, configCountryCode, productName, strategyname, factorname, event_yearmonth),
            (channel, configCountryCode, repTeamName, strategyname, factorname, event_yearmonth),
            (repTeamName, productName, strategyname, factorname, event_yearmonth),
            (configCountryCode, productName, strategyname, factorname, event_yearmonth),
            (configCountryCode, repTeamName, strategyname, factorname, event_yearmonth),
            (channel, productName, strategyname, factorname, event_yearmonth),
            (channel, repTeamName, strategyname, factorname, event_yearmonth),
            (channel, configCountryCode, strategyname, factorname, event_yearmonth),
            (productName, strategyname, factorname, event_yearmonth),
            (repTeamName, strategyname, factorname, event_yearmonth),
            (configCountryCode, strategyname, factorname, event_yearmonth),
            (channel, strategyname, factorname, event_yearmonth),
            (strategyname, factorname, event_yearmonth),
            -- strategy VS usecase
            (channel, configCountryCode, repTeamName, productName, usecasename, factorname, event_yearmonth),
            (configCountryCode, repTeamName, productName, usecasename, factorname, event_yearmonth),
            (channel, repTeamName, productName, usecasename, factorname, event_yearmonth),
            (channel, configCountryCode, productName, usecasename, factorname, event_yearmonth),
            (channel, configCountryCode, repTeamName, usecasename, factorname, event_yearmonth),
            (repTeamName, productName, usecasename, factorname, event_yearmonth),
            (configCountryCode, productName, usecasename, factorname, event_yearmonth),
            (configCountryCode, repTeamName, usecasename, factorname, event_yearmonth),
            (channel, productName, usecasename, factorname, event_yearmonth),
            (channel, repTeamName, usecasename, factorname, event_yearmonth),
            (channel, configCountryCode, usecasename, factorname, event_yearmonth),
            (productName, usecasename, factorname, event_yearmonth),
            (repTeamName, usecasename, factorname, event_yearmonth),
            (configCountryCode, usecasename, factorname, event_yearmonth),
            (channel, usecasename, factorname, event_yearmonth),
            (usecasename, factorname, event_yearmonth),
            -- lines above are for bottom section
            (channel, configCountryCode, repTeamName, productName, strategyname, event_yearmonth),
            (configCountryCode, repTeamName, productName, strategyname, event_yearmonth),
            (channel, repTeamName, productName, strategyname, event_yearmonth),
            (channel, configCountryCode, productName, strategyname, event_yearmonth),
            (channel, configCountryCode, repTeamName, strategyname, event_yearmonth),
            (repTeamName, productName, strategyname, event_yearmonth),
            (configCountryCode, productName, strategyname, event_yearmonth),
            (configCountryCode, repTeamName, strategyname, event_yearmonth),
            (channel, productName, strategyname, event_yearmonth),
            (channel, repTeamName, strategyname, event_yearmonth),
            (channel, configCountryCode, strategyname, event_yearmonth),
            (productName, strategyname, event_yearmonth),
            (repTeamName, strategyname, event_yearmonth),
            (configCountryCode, strategyname, event_yearmonth),
            (channel, strategyname, event_yearmonth),
            (strategyname, event_yearmonth),
            -- strategy VS usecase
            (channel, configCountryCode, repTeamName, productName, usecasename, event_yearmonth),
            (configCountryCode, repTeamName, productName, usecasename, event_yearmonth),
            (channel, repTeamName, productName, usecasename, event_yearmonth),
            (channel, configCountryCode, productName, usecasename, event_yearmonth),
            (channel, configCountryCode, repTeamName, usecasename, event_yearmonth),
            (repTeamName, productName, usecasename, event_yearmonth),
            (configCountryCode, productName, usecasename, event_yearmonth),
            (configCountryCode, repTeamName, usecasename, event_yearmonth),
            (channel, productName, usecasename, event_yearmonth),
            (channel, repTeamName, usecasename, event_yearmonth),
            (channel, configCountryCode, usecasename, event_yearmonth),
            (productName, usecasename, event_yearmonth),
            (repTeamName, usecasename, event_yearmonth),
            (configCountryCode, usecasename, event_yearmonth),
            (channel, usecasename, event_yearmonth),
            (usecasename, event_yearmonth),
            -- lines above are for middle section
            (channel, configCountryCode, repTeamName, productName, event_yearmonth),
            (configCountryCode, repTeamName, productName, event_yearmonth),
            (channel, repTeamName, productName, event_yearmonth),
            (channel, configCountryCode, productName, event_yearmonth),
            (channel, configCountryCode, repTeamName, event_yearmonth),
            (repTeamName, productName, event_yearmonth),
            (configCountryCode, productName, event_yearmonth),
            (configCountryCode, repTeamName, event_yearmonth),
            (channel, productName, event_yearmonth),
            (channel, repTeamName, event_yearmonth),
            (channel, configCountryCode, event_yearmonth),
            (productName, event_yearmonth),
            (repTeamName, event_yearmonth),
            (configCountryCode, event_yearmonth),
            (channel, event_yearmonth),
            (event_yearmonth)
            -- lines above are for top section
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'
