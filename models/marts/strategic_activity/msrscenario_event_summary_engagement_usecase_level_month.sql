{{ config(materialized='table') }}

select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, channel, configCountryCode, repTeamName, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, configCountryCode, repTeamName, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, channel, repTeamName, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    'All rep teams' as repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, channel, configCountryCode, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, channel, configCountryCode, repTeamName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level' 
group by
    engagementsegment_quarterly, repTeamName, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    'All rep teams' as repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, configCountryCode, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, configCountryCode, repTeamName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    'All rep teams' as repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, channel, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, channel, repTeamName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    'All rep teams' as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, channel, configCountryCode, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    'All rep teams' as repTeamName,
    productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
      and productName != 'Not product level'
group by
    engagementsegment_quarterly, productName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, repTeamName, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    'All rep teams' as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, configCountryCode, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    'All rep teams' as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, channel, usecasename, event_yearmonth
union all
select
    usecasename,
    9999 as factorid,
    'All strategies' as strategyname,
    'All channels' as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    'All countries' as configCountryCode,
    'All rep teams' as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt, -- deno
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt, -- numer
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1', '3', '4')
group by
    engagementsegment_quarterly, usecasename, event_yearmonth