{{ config(materialized='view') }}

with all_summaries as (
    select
    'default' as msrscenariouid,
    factorName,
    strategyname,
    channel,
    event_date,
    event_quarter,
    strategicCategoryGroupUid,
    configCountryCode,
    repTeamName,
    tagName,
    cast('All products' as varchar) as productName,
    account_cnt,
    row_cnt,
    'factor level' as recordType,
    'FieldRep' as ActorName
    from {{ ref('msrscenario_passed_evaluation_summary_lvl2') }} a

    union all

    select
    'default' as msrscenariouid,
    factorName,
    strategyname,
    channel,
    event_date,
    event_quarter,
    strategicCategoryGroupUid,
    configCountryCode,
    repTeamName,
    tagName,
    productName,
    account_cnt,
    row_cnt,
    'factor-product level' as recordType,
    'FieldRep' as ActorName
    from {{ ref('msrscenario_passed_evaluation_summary_lvl3') }} a
)

select a.*
from all_summaries a
left join {{ ref('param_msrscenario_driven_conditions_v') }} params 
on params.msrscenariouid = a.msrscenariouid 
where
  params.msrscenariouid = 'default'
  and a.recordType = params.evaluation_level_of_detail