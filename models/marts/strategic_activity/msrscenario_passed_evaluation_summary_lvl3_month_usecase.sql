{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_yearmonth, tagName),
        (factorname, event_yearmonth, tagName, channel),
        (factorname, event_yearmonth, tagName, configCountryCode),
        (factorname, event_yearmonth, tagName, productName),
        (factorname, event_yearmonth, tagName, repTeamName),
        (factorname, event_yearmonth, tagName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode),
        (factorname, event_yearmonth, tagName, channel, productName)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_yearmonth, tagName, channel, repTeamName),
        (factorname, event_yearmonth, tagName, channel, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, configCountryCode, productName),
        (factorname, event_yearmonth, tagName, configCountryCode, repTeamName),
        (factorname, event_yearmonth, tagName, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, productName, repTeamName),
        (factorname, event_yearmonth, tagName, productName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, repTeamName, strategicActionCategoryId)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_yearmonth, tagName, channel, configCountryCode, productName),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, repTeamName),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, productName, repTeamName),
        (factorname, event_yearmonth, tagName, channel, productName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, configCountryCode, productName, repTeamName),
        (factorname, event_yearmonth, tagName, configCountryCode, productName, strategicActionCategoryId)
    )

union all

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') as event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl3') }}
group by
    grouping sets (
        (factorname, event_yearmonth, tagName, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, productName, repTeamName),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, productName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, configCountryCode, productName, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, productName, repTeamName, strategicActionCategoryId)
    )
