{{ config(materialized='view') }}

with candidates as
(
    select
        'Interaction level' as recordType,
        s.accountuid,
        null as productuid,
        s.segmentname,
        s.repuid as actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        messagetopic,
        eventdatetimeutc,
        suggestiondriver,
        0 as isconversionevent,
        0 as conversionvalue,
        1 is_candidate_usecase,
        -1 match_closeness,
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        null as actiontaken,
        null as issuggestioncompleteddirect,
        null as issuggestioncompletedinfer,
        0 as is_candidate_same_product,
        s.intmatcheventtypename,
        s.actionTypeId
    from {{ ref('suggestion_candidate_usecases_v') }} s
    union
    select
        'Interaction-factor level' as recordType,
        s.accountuid,
        null as productuid,
        s.segmentname,
        s.repuid as actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        messagetopic,
        eventdatetimeutc,
        suggestiondriver,
        0 as isconversionevent,
        0 as conversionvalue,
        1 is_candidate_usecase,
        -1 match_closeness,
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        null as actiontaken,
        null as issuggestioncompleteddirect,
        null as issuggestioncompletedinfer,
        0 as is_candidate_same_product,
        s.intmatcheventtypename,
        s.actionTypeId
    from {{ ref('suggestion_candidate_factor_dtl_usecases_v') }} s
    union
    select
        'Interaction-factor-product level' as recordType,
        s.accountuid,
        s.productuid,
        s.segmentname,
        s.repuid as actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        messagetopic,
        eventdatetimeutc,
        suggestiondriver,
        0 as isconversionevent,
        0 as conversionvalue,
        1 is_candidate_usecase,
        -1 match_closeness,
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        null as actiontaken,
        null as issuggestioncompleteddirect,
        null as issuggestioncompletedinfer,
        0 as is_candidate_same_product,
        s.intmatcheventtypename,
        s.actionTypeId
    from {{ ref('suggestion_candidate_product_dtl_usecases_v') }} s
),
kpi_event_product_interaction as
(
    select
        recordType,
        accountuid,
        productuid,
        segmentname,
        actoruid,
        eventtypename,
        factoruid,
        factorname,
        usecasename,
        eventdatetimeutc,
        suggestiondriver,
        isconversionevent,
        conversionvalue,
        is_candidate_usecase, 
        suggestionreferenceid,
        internalsuggestionreferenceid,
        intmatcheventtypename,
        actionTypeId
    from candidates
)
select
    recordType,
    p.msrscenariouid,
    kpi_event_product_interaction.accountuid,
    kpi_event_product_interaction.productuid,
    kpi_event_product_interaction.segmentname,
    kpi_event_product_interaction.actoruid,
    eventtypename,
    factoruid,
    factorname,
    coalesce(fm.usecase_name, usecasename) usecasename,
    kpi_event_product_interaction.eventdatetimeutc,
    suggestiondriver,
    suggestionreferenceid,
    internalsuggestionreferenceid,
    intmatcheventtypename,
    actionTypeId,
    row_number() OVER (
        PARTITION BY
            kpi_event_product_interaction.accountuid,
            kpi_event_product_interaction.productuid
        ORDER BY
            kpi_event_product_interaction.eventdatetimeutc asc) as event_order
from kpi_event_product_interaction
inner join {{ ref('param_msrscenario_period_definition_v') }} p
    on p.period_type = 'Analysis Period'
inner join {{ ref('param_msrscenario_period_definition_v') }} q
    on q.msrscenariouid = p.msrscenariouid
   and q.period_type = 'Conversion Period'
inner join {{ ref('param_msrscenario_events_v') }} t
    on t.msrscenariouid = p.msrscenariouid
left join {{ ref('param_msrscenario_factorusecasename_map_override_v') }} fm
    on p.msrscenariouid = fm.msrscenariouid
   and kpi_event_product_interaction.factorname = fm.factor_name
;
