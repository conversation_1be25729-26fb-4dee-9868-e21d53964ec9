{{ config(materialized='table') }}

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, configCountryCode, repTeamName, productName, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, repTeamName, productName, event_quarter),
            (strategicCategoryGroupUid, channel, repTeamName, productName, event_quarter),
            (strategicCategoryGroupUid, channel, configCountryCode, productName, event_quarter)
            -- lines above are for top section
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, configCountryCode, repTeamName, event_quarter),
            (strategicCategoryGroupUid, repTeamName, productName, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, productName, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, repTeamName, event_quarter)
            -- lines above are for top section
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, productName, event_quarter),
            (strategicCategoryGroupUid, channel, repTeamName, event_quarter),
            (strategicCategoryGroupUid, channel, configCountryCode, event_quarter),
            (strategicCategoryGroupUid, productName, event_quarter)
            -- lines above are for top section
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, repTeamName, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, event_quarter),
            (strategicCategoryGroupUid, channel, event_quarter),
            (strategicCategoryGroupUid, event_quarter)
            -- lines above are for top section
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'
