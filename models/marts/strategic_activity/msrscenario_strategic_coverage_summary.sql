{{ config(materialized = 'view') }}

select recordType,
count(1) record_count,
case when interactionuid is null and suggestionreferenceid is not null and is_candidate_usecase = 1 then 'candidate' 
     when interactionuid is null and suggestionreferenceid is not null and is_candidate_usecase = 0 then 'suggestion' 
     when interactionuid is not null and suggestionreferenceid is not null and is_candidate_usecase = 0 then 'interaction-with-suggestion' 
     when interactionuid is not null and suggestionreferenceid is not null and is_candidate_usecase = 1 then 'interaction-with-candidate' 
     when interactionuid is not null and suggestionreferenceid is null then 'not-strategic'
    else 'unknown' end as recordSubType,
count(distinct date_format(eventdatetimeutc, '%Y-%m')) month_count,     
count(distinct factoruid) factor_count,
count(distinct interaction_channelname) channel_count, 
count(distinct repteamname) repteam_count, 
count(distinct configCountryName) country_count, 
count(distinct productname) product_count, 
count(distinct segmentname) segment_count, 
count(distinct repTypeName) actorType_count, 
count(distinct seConfigId) config_count, 
count(distinct interactionuid) interaction_count, 
count(distinct concat(interactionuid, productuid)) interaction_product_count,
count (distinct case when driven = 1 then concat(interactionuid, productuid) else null end) driven_interactions,
count (distinct case when aligned = 1 then concat(interactionuid, productuid) else null end) aligned_interactions,
count (distinct case when not_strategic = 1 then concat(interactionuid, productuid) else null end) not_strategic_interactions,
count(distinct accountuid) account_count,
count (distinct case when driven = 1 then accountuid else null end) driven_accounts,
count (distinct case when aligned = 1 then accountuid else null end) aligned_accounts,
count (distinct case when not_strategic = 1 then accountuid else null end) not_strategic_accounts,
count(driven) driven_count, count(aligned) aligned_count, count(not_strategic) not_strategic_count
from {{ ref('msrscenario_strategic_coverage_v') }}
-- where recordType = 'Interaction-factor-product level'
-- and eventdatetimeutc between date_parse('2022-10-01','%Y-%m-%d') and date_parse('2022-11-01','%Y-%m-%d')
-- and interaction_channelname in ('')
-- and repteamname in ('DIABETES')
-- and usecasename in ('Account Identification')
-- and configCountryName in ('')
-- and productname in ('')
-- and segmentname in ('')
-- and repTypeName in ('')
group by recordType, case when interactionuid is null and suggestionreferenceid is not null and is_candidate_usecase = 1 then 'candidate' 
     when interactionuid is null and suggestionreferenceid is not null and is_candidate_usecase = 0 then 'suggestion' 
     when interactionuid is not null and suggestionreferenceid is not null and is_candidate_usecase = 0 then 'interaction-with-suggestion' 
     when interactionuid is not null and suggestionreferenceid is not null and is_candidate_usecase = 1 then 'interaction-with-candidate' 
     when interactionuid is not null and suggestionreferenceid is null then 'not-strategic'
    else 'unknown' end
;
