{{ config(materialized='view') }}

select
    recordType,
    ActorName,
    'All' as engagementsegment_quarterly,
    factorName,
    strategyname,
    channel,
    event_date,
    event_quarter,
    strategicCategoryGroupUid,
    configCountryCode,
    repTeamName,
    tagName as usecasename,
    productName,
    account_cnt,
    0 as suggestion_cnt,
    0 as interaction_cnt,
    0 as strategic_account_cnt,
    row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    0 as actor_cnt
from {{ ref('audience_evaluation_v') }}

union all

select
    recordType,
    ActorName,
    engagementsegment_quarterly,
    factorName,
    strategyname,
    channel,
    event_date,
    event_quarter,
    strategicCategoryGroupUid,
    configCountryCode,
    repTeamName,
    usecasename,
    productName,
    account_cnt,
    suggestion_cnt,
    interaction_cnt,
    strategic_account_cnt,
    row_cnt,
    median_bucket_engagementrate_quarterly,
    actor_cnt
from {{ ref('all_events_summary_v') }}