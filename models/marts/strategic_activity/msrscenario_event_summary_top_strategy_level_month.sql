{{ config(materialized='table') }}

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, configCountryCode, repTeamName, productName, strategyname, event_yearmonth),
            (configCountryCode, repTeamName, productName, strategyname, event_yearmonth),
            (channel, repTeamName, productName, strategyname, event_yearmonth),
            (channel, configCountryCode, productName, strategyname, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, configCountryCode, repTeamName, strategyname, event_yearmonth),
            (repTeamName, productName, strategyname, event_yearmonth),
            (configCountryCode, productName, strategyname, event_yearmonth),
            (configCountryCode, repTeamName, strategyname, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, productName, strategyname, event_yearmonth),
            (channel, repTeamName, strategyname, event_yearmonth),
            (channel, configCountryCode, strategyname, event_yearmonth),
            (productName, strategyname, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (repTeamName, strategyname, event_yearmonth),
            (configCountryCode, strategyname, event_yearmonth),
            (channel, strategyname, event_yearmonth),
            (strategyname, event_yearmonth)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'
