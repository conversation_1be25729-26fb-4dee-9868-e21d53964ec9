{{ config(materialized='table') }}


select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, configCountryCode, repTeamName, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, configCountryCode, repTeamName, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, channel, repTeamName, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, channel, configCountryCode, productName, strategyname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, configCountryCode, repTeamName, strategyname, event_quarter),
            (engagementsegment_quarterly, repTeamName, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, configCountryCode, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, configCountryCode, repTeamName, strategyname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, productName, strategyname, event_quarter),
            (engagementsegment_quarterly, channel, repTeamName, strategyname, event_quarter),
            (engagementsegment_quarterly, channel, configCountryCode, strategyname, event_quarter),
            (engagementsegment_quarterly, productName, strategyname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, repTeamName, strategyname, event_quarter),
            (engagementsegment_quarterly, configCountryCode, strategyname, event_quarter),
            (engagementsegment_quarterly, channel, strategyname, event_quarter),
            (engagementsegment_quarterly, strategyname, event_quarter)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'