{{ config(materialized='table') }}

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, configCountryCode, repTeamName, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, repTeamName, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, channel, repTeamName, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, channel, configCountryCode, productName, usecasename, factorid, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, configCountryCode, repTeamName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, repTeamName, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, repTeamName, usecasename, factorid, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, channel, productName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, channel, repTeamName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, channel, configCountryCode, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, productName, usecasename, factorid, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501
group by
    grouping sets (
            (strategicCategoryGroupUid, repTeamName, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, configCountryCode, usecasename, factorid, event_quarter),
            (strategicCategoryGroupUid, channel, usecasename, factorid, event_quarter),
        (strategicCategoryGroupUid, usecasename, factorid, event_quarter)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'
