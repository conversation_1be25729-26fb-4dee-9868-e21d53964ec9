{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    case when GROUPING(event_quarter) = 0 then event_quarter else 'All quarters' end event_quarter,
    '604' as strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    case when GROUPING(tagName) = 0 then tagName else 'All usecases' end as tagName,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    0 as account_cnt,
    0 as row_cnt
from
    {{ ref('msrscenario_all_evaluations_lvl2') }} a
where a.status = 0
group by
    grouping sets ((channel, configCountryCode, repTeamName, factorName, tagname, event_quarter),
                (configCountryCode, repTeamName, factorName, tagname, event_quarter),
                (channel, repTeamName, factorName, tagname, event_quarter),
                (channel, configCountryCode, factorName, tagname, event_quarter),
                (repTeamName, factorName, tagname, event_quarter),
                (channel, factorName, tagname, event_quarter),
                (configCountryCode, factorName, tagname, event_quarter),
                (factorName, tagname, event_quarter),
                -- strategy VS usecase
                (channel, configCountryCode, repTeamName, factorName, strategyname, event_quarter),
                (configCountryCode, repTeamName, factorName, strategyname, event_quarter),
                (channel, repTeamName, factorName, strategyname, event_quarter),
                (channel, configCountryCode, factorName, strategyname, event_quarter),
                (repTeamName, factorName, strategyname, event_quarter),
                (channel, factorName, strategyname, event_quarter),
                (configCountryCode, factorName, strategyname, event_quarter),
                (factorName, strategyname, event_quarter)
    )
having count(1) > 0