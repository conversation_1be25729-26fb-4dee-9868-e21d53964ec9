{{ config(materialized='table') }}

select
    'All usecases' as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorN<PERSON>,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for bottom section
            (channel, configCountryCode, repTeamName, productName, strategyname, factorid, event_yearmonth),
            (configCountryCode, repTeamName, productName, strategyname, factorid, event_yearmonth),
            (channel, repTeamName, productName, strategyname, factorid, event_yearmonth),
            (channel, configCountryCode, productName, strategyname, factorid, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for bottom section
            (channel, configCountryCode, repTeamName, strategyname, factorid, event_yearmonth),
            (repTeamName, productName, strategyname, factorid, event_yearmonth),
            (configCountryCode, productName, strategyname, factorid, event_yearmonth),
            (configCountryCode, repTeamName, strategyname, factorid, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for bottom section
            (channel, productName, strategyname, factorid, event_yearmonth),
            (channel, repTeamName, strategyname, factorid, event_yearmonth),
            (channel, configCountryCode, strategyname, factorid, event_yearmonth),
            (productName, strategyname, factorid, event_yearmonth)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    'All usecases' as usecasename,
    -- case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    factorid,
    case when GROUPING(strategyname) = 0 then strategyname else 'All strategies' end as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    case when GROUPING(event_yearmonth) = 0 then concat(event_yearmonth, '-01') else 'All months' end as event_date,
    -- case when GROUPING(strategicCategoryGroupUid) = 0 then strategicCategoryGroupUid else 'All actions' end as strategicCategoryGroupUid,
    'Not used' as event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for bottom section
            (repTeamName, strategyname, factorid, event_yearmonth),
            (configCountryCode, strategyname, factorid, event_yearmonth),
            (channel, strategyname, factorid, event_yearmonth),
            (strategyname, factorid, event_yearmonth)
        )
-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'
