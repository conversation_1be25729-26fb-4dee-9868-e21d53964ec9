{{ config(materialized='table') }}

with candidate_insight_matching_days as
(
select
coalesce(candidate_matching_pre_days, -1) candidate_matching_pre_days,
coalesce(candidate_matching_post_days, 3) candidate_matching_post_days,
coalesce(insight_matching_pre_days, -1) insight_matching_pre_days,
coalesce(insight_matching_post_days, 3) insight_matching_post_days
from  {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
)

select 'Interaction level' as recordType,
        s.accountuid,
        cast(null as varchar) as productuid,
        coalesce(a.segmentname, s.segmentname) as segmentname,
        s.repuid as actoruid,
        s.eventtypename as eventtypename,
        s.factoruid as factoruid,
        s.factorname as factorname,
        s.usecasename as usecasename,
        s.strategyname as strategyname,
        s.messagetopic as messagetopic,
        date_format(s.eventdatetimeutc, '%Y-%m') event_yearmonth,
        concat(date_format(s.eventdatetimeutc, '%Y'), '-Q', cast(quarter(s.eventdatetimeutc) as varchar)) event_quarter,
        s.suggestiondriver as suggestiondriver,
        0 as isconversionevent,
        a.conversionvalue,
        case when a.suggestionreferenceid is null and s.suggestionreferenceid is not null then 1 else 0 end is_candidate_usecase,
        date_diff('day', a.eventdatetimeutc, s.eventdatetimeutc) match_closeness,
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        a.actiontaken,
        a.issuggestioncompleteddirect,
        a.issuggestioncompletedinfer,
        0 as is_candidate_same_product,
        case when a.eventtypename = s.intmatcheventtypename then 1 else 0 end is_candidate_same_event_type,
        a.interactionuid,
        a.mergeid,
        s.actionTypeId as repActionTypeId,
        s.actionTypeId as detailrepactiontypeid,
        count(1) candidateCount
        from {{ ref('suggestion_candidate_usecases_v') }} s
        cross join candidate_insight_matching_days d
        left join {{ ref('kpi_event_interaction') }} a
        on a.recordType = 'Interaction level'
        and (a.suggestionreferenceid is null and a.accountuid = s.accountuid
       and a.repuid = s.repuid
       and s.candidate_order = 1
       and (a.eventdatetimeutc between date_add('day', d.candidate_matching_pre_days, s.eventdatetimeutc) and date_add('day', d.candidate_matching_post_days, s.eventdatetimeutc)))
       where a.interactionuid is null
group by
        s.accountuid,
        coalesce(a.segmentname, s.segmentname),
        s.repuid,
        s.eventtypename,
        s.factoruid,
        s.factorname,
        s.usecasename,
        s.strategyname,
        s.messagetopic,
        date_format(s.eventdatetimeutc, '%Y-%m'),
        concat(date_format(s.eventdatetimeutc, '%Y'), '-Q', cast(quarter(s.eventdatetimeutc) as varchar)),
        s.suggestiondriver,
        a.conversionvalue,
        case when a.suggestionreferenceid is null and s.suggestionreferenceid is not null then 1 else 0 end,
        date_diff('day', a.eventdatetimeutc, s.eventdatetimeutc),
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        a.actiontaken,
        a.issuggestioncompleteddirect,
        a.issuggestioncompletedinfer,
        case when a.eventtypename = s.intmatcheventtypename then 1 else 0 end,
        a.interactionuid,
        a.mergeid,
        s.actionTypeId,
        s.actionTypeId