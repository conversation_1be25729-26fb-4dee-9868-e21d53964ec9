{{ config(materialized='view') }}

with summary_with_factorid as (
select * from {{ ref('msrscenario_event_summary_category_strategy_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_category_strategy_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_category_usecase_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_category_usecase_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_category_topsection_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_category_strategy_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_category_strategy_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_category_usecase_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_category_usecase_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_category_topsection_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_strategy_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_strategy_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_usecase_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_usecase_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_strategy_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_strategy_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_usecase_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_engagement_usecase_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_top_strategy_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_top_strategy_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_top_usecase_factor_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_top_usecase_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_top_topsection_level_month') }}

union all

select * from {{ ref('msrscenario_event_summary_top_strategy_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_top_strategy_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_top_usecase_factor_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_top_usecase_level_quarter') }}

union all

select * from {{ ref('msrscenario_event_summary_top_topsection_level_quarter') }}
)

select
usecasename,
f.factorname,
strategyname,
channel,
event_date,
event_quarter,
strategicCategoryGroupUid,
engagementsegment_quarterly,
configCountryCode,
repTeamName,
productName,
ActorName,
recordType,
account_cnt,
suggestion_cnt,
interaction_cnt,
strategic_account_cnt,
row_cnt,
median_bucket_engagementrate_quarterly,
actor_cnt
from summary_with_factorid s
join {{ ref('factor_id_name_map') }} f
on s.factorid = f.factorid