{{ config(materialized='table') }}

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, configCountryCode, repTeamName, productName, usecasename, event_quarter),
            (configCountryCode, repTeamName, productName, usecasename, event_quarter),
            (channel, repTeamName, productName, usecasename, event_quarter),
            (channel, configCountryCode, productName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, configCountryCode, repTeamName, usecasename, event_quarter),
            (repTeamName, productName, usecasename, event_quarter),
            (configCountryCode, productName, usecasename, event_quarter),
            (configCountryCode, repTeamName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (channel, productName, usecasename, event_quarter),
            (channel, repTeamName, usecasename, event_quarter),
            (channel, configCountryCode, usecasename, event_quarter),
            (productName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    0 as strategic_account_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicActionCategoryId != 501

group by
    grouping sets (
            -- lines below are for middle section
            (repTeamName, usecasename, event_quarter),
            (configCountryCode, usecasename, event_quarter),
            (channel, usecasename, event_quarter),
            (usecasename, event_quarter)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'