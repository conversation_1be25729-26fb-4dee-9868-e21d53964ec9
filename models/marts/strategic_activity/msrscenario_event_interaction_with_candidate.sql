{{ config(materialized='view') }}


with all_levels as (
    select * from {{ ref('msrscenario_event_interaction_with_candidate_lvl1') }}

    union all

    select * from {{ ref('msrscenario_event_interaction_with_candidate_lvl2') }}

    union all

    select * from {{ ref('msrscenario_event_interaction_with_candidate_lvl3') }}

)

select a.*
from all_levels a
left join {{ ref('param_msrscenario_driven_conditions_v') }} params on params.msrscenariouid = 'default' and a.recordType = params.interaction_level_of_detail 
where
    params.interaction_level_of_detail is not null