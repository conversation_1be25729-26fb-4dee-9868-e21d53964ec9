{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    'All usecases' as tagName,
    strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl2') }}
group by
    grouping sets (
        (factorname, event_quarter, strategyname),
        (factorname, event_quarter, strategyname, channel),
        (factorname, event_quarter, strategyname, configCountryCode),
        (factorname, event_quarter, strategyname, repTeamName),
        (factorname, event_quarter, strategyname, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, channel, configCountryCode),
        (factorname, event_quarter, strategyname, channel, repTeamName),
        (factorname, event_quarter, strategyname, channel, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, configCountryCode, repTeamName),
        (factorname, event_quarter, strategyname, configCountryCode, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, channel, configCountryCode, repTeamName),
        (factorname, event_quarter, strategyname, channel, configCountryCode, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, channel, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_quarter, strategyname, channel, configCountryCode, repTeamName, strategicActionCategoryId)

    )