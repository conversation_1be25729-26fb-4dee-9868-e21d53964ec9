{{ config(materialized='table') }}

with candidate_insight_matching_days as
(
select 
coalesce(candidate_matching_pre_days, -1) candidate_matching_pre_days,
coalesce(candidate_matching_post_days, 3) candidate_matching_post_days,
coalesce(insight_matching_pre_days, -1) insight_matching_pre_days,
coalesce(insight_matching_post_days, 3) insight_matching_post_days
from  {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
)

select
        a.recordType,
        a.accountuid,
        a.productuid as productuid,
        a.segmentname as segmentname,
        a.repuid as actoruid,
        a.eventtypename,
        coalesce(a.factoruid, s.factoruid) as factoruid,
        coalesce(a.factorname, s.factorname) as factorname,
        coalesce(a.usecasename, s.usecasename) as usecasename,
        coalesce(a.strategyname, s.strategyname) as strategyname,
        a.messagetopic,
        a.eventdatetimeutc,
        a.suggestiondriver,
        a.isconversionevent,
        a.conversionvalue,
        case when a.suggestionreferenceid is null and s.suggestionreferenceid is not null then 1 else 0 end is_candidate_usecase,
        date_diff('day', a.eventdatetimeutc, s.eventdatetimeutc) match_closeness,
        s.suggestionreferenceid,
        s.internalsuggestionreferenceid,
        a.actiontaken,
        a.issuggestioncompleteddirect,
        a.issuggestioncompletedinfer,
        1 as is_candidate_same_product,
        case when a.eventtypename = s.intmatcheventtypename then 1 else 0 end is_candidate_same_event_type,
        a.interactionuid,
        a.mergeid,
        repActionTypeId,
        detailrepactiontypeid,
        a.call_type,
        a.call_channel_raw,
        a.call_channel_category 
    from {{ ref('kpi_event_interaction') }} a
    cross join candidate_insight_matching_days d
    left join {{ ref('suggestion_candidate_product_dtl_usecases_v') }} s
        on a.accountuid = s.accountuid
       and a.repuid = s.repuid
       and a.productuid = s.productuid
       and s.candidate_order = 1
       and a.eventdatetimeutc between date_add('day', d.candidate_matching_pre_days, s.eventdatetimeutc) and date_add('day', d.candidate_matching_post_days, s.eventdatetimeutc)
    where a.suggestionreferenceid is null
      and a.recordType = 'Interaction-factor-product level'