{{ config(materialized='table') }}


select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, configCountryCode, repTeamName, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, configCountryCode, repTeamName, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, channel, repTeamName, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, channel, configCountryCode, productName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, configCountryCode, repTeamName, usecasename, event_quarter),
            (engagementsegment_quarterly, repTeamName, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, configCountryCode, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, configCountryCode, repTeamName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, channel, productName, usecasename, event_quarter),
            (engagementsegment_quarterly, channel, repTeamName, usecasename, event_quarter),
            (engagementsegment_quarterly, channel, configCountryCode, usecasename, event_quarter),
            (engagementsegment_quarterly, productName, usecasename, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

UNION ALL

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    -- 'All factors' as factorname,
    9999 as factorid,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    'All actions' as strategicCategoryGroupUid,
    engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    count(distinct interactionuid) interaction_cnt,
    count(distinct (case when strategicCategoryGroupUid = '4' then '' else accountuid end)) - 1 strategic_account_cnt,
    count(1) row_cnt,
    avg(median_bucket_engagementrate_quarterly) as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_events') }}
    where strategicCategoryGroupUid in ('1','3','4')
group by
    grouping sets (
            -- lines below are for drill down tool tip
            (engagementsegment_quarterly, repTeamName, usecasename, event_quarter),
            (engagementsegment_quarterly, configCountryCode, usecasename, event_quarter),
            (engagementsegment_quarterly, channel, usecasename, event_quarter),
            (engagementsegment_quarterly, usecasename, event_quarter)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'