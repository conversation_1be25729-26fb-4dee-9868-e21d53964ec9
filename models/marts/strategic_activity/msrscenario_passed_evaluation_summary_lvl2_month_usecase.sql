{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    tagName,
    'All strategies' as strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl2') }}
group by
    grouping sets (
        (factorname, event_yearmonth, tagName),
        (factorname, event_yearmonth, tagName, channel),
        (factorname, event_yearmonth, tagName, configCountryCode),
        (factorname, event_yearmonth, tagName, repTeamName),
        (factorname, event_yearmonth, tagName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode),
        (factorname, event_yearmonth, tagName, channel, repTeamName),
        (factorname, event_yearmonth, tagName, channel, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, configCountryCode, repTeamName),
        (factorname, event_yearmonth, tagName, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, repTeamName),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, tagName, channel, configCountryCode, repTeamName, strategicActionCategoryId)

    )