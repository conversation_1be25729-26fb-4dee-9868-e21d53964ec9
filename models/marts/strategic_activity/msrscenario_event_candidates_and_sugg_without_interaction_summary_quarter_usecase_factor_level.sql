{{ config(materialized='table') }}

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    '4' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    0 as interaction_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_event_candidates_and_sugg_without_interaction') }}
group by
    grouping sets (
            -- lines below are for bottom section
            (channel, configCountryCode, repTeamName, productName, usecasename, factorname, event_quarter),
            (configCountryCode, repTeamName, productName, usecasename, factorname, event_quarter),
            (channel, repTeamName, productName, usecasename, factorname, event_quarter),
            (channel, configCountryCode, productName, usecasename, factorname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    '4' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    0 as interaction_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_event_candidates_and_sugg_without_interaction') }}
group by
    grouping sets (
            -- lines below are for bottom section
            (channel, configCountryCode, repTeamName, usecasename, factorname, event_quarter),
            (repTeamName, productName, usecasename, factorname, event_quarter),
            (configCountryCode, productName, usecasename, factorname, event_quarter),
            (configCountryCode, repTeamName, usecasename, factorname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    '4' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    case when GROUPING(productName) = 0 then productName else 'All products' end as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    0 as interaction_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_event_candidates_and_sugg_without_interaction') }}
group by
    grouping sets (
            -- lines below are for bottom section
            (channel, productName, usecasename, factorname, event_quarter),
            (channel, repTeamName, usecasename, factorname, event_quarter),
            (channel, configCountryCode, usecasename, factorname, event_quarter),
            (productName, usecasename, factorname, event_quarter)
        )

having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'

union all

select
    case when GROUPING(usecasename) = 0 then usecasename else 'All usecases' end as usecasename,
    case when GROUPING(factorname) = 0 then factorname else 'All factors' end as factorname,
    'All strategies' as strategyname,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    'Not used' as event_date,
    event_quarter,
    '4' as strategicCategoryGroupUid,
    'All' as engagementsegment_quarterly,
    case when GROUPING(configCountryCode) = 0 then configCountryCode else 'All countries' end as configCountryCode,
    case when GROUPING(repTeamName) = 0 then repTeamName else 'All rep teams' end as repTeamName,
    'All products' as productName,
    'FieldRep' as ActorName,
    max(recordType) as recordType,
    count(distinct accountuid) account_cnt,
    count(distinct suggestionreferenceid) suggestion_cnt,
    0 as interaction_cnt,
    count(1) row_cnt,
    0 as median_bucket_engagementrate_quarterly,
    count(distinct actoruid) actor_cnt
from
    {{ ref('msrscenario_event_candidates_and_sugg_without_interaction') }}
group by
    grouping sets (
            -- lines below are for bottom section
            (repTeamName, usecasename, factorname, event_quarter),
            (configCountryCode, usecasename, factorname, event_quarter),
            (channel, usecasename, factorname, event_quarter),
            (usecasename, factorname, event_quarter)
        )

-- having case when GROUPING(productName) = 0 then productName else 'All products' end != 'Not product level'