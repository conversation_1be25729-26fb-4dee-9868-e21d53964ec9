{{ config(materialized='view') }}

with events_agg_groupuid as (
select a.*
from {{ ref('msrscenario_events_summary') }} a
where a.strategicCategoryGroupUid = 'All actions'

)


select a.*
from {{ ref('msrscenario_events_summary') }} a
where a.strategicCategoryGroupUid != 'All actions'
-- groupuid = '1, 2, 3, 5'

union all

select
    usecasename,
    factorName,
    strategyname,
    channel,
    event_date,
    event_quarter,
    strategicCategoryGroupUid,
    engagementsegment_quarterly,
    configCountryCode,
    repTeamName,
    productName,
    ActorName,
    recordType,
    account_cnt,
    suggestion_cnt,
    interaction_cnt,
    0 as strategic_account_cnt,
    row_cnt,
    median_bucket_engagementrate_quarterly,
    actor_cnt
from {{ ref('msrscenario_event_candidates_and_sugg_without_interaction_summary') }} b
-- groupuid = '4'

union all

select
    coalesce(a.usecasename, b.usecasename) usecasename,
    coalesce(a.factorName, b.factorName) factorName,
    coalesce(a.strategyname, b.strategyname) strategyname,
    coalesce(a.channel, b.channel) channel,
    coalesce(a.event_date, b.event_date) event_date,
    coalesce(a.event_quarter, b.event_quarter) event_quarter,
    'All actions' as strategicCategoryGroupUid,
    coalesce(a.engagementsegment_quarterly, b.engagementsegment_quarterly) engagementsegment_quarterly,
    coalesce(a.configCountryCode, b.configCountryCode) configCountryCode,
    coalesce(a.repTeamName, b.repTeamName) repTeamName,
    coalesce(a.productName, b.productName) productName,
    coalesce(a.ActorName, b.ActorName) ActorName,
    coalesce(a.recordType, b.recordType) recordType,
    coalesce(a.account_cnt, 0) + coalesce(b.account_cnt, 0) as account_cnt,
    coalesce(a.suggestion_cnt, 0) + coalesce(b.suggestion_cnt, 0) as suggestion_cnt,
    coalesce(a.interaction_cnt, 0) + coalesce(b.interaction_cnt, 0) as interaction_cnt,
    a.strategic_account_cnt as strategic_account_cnt,
    coalesce(a.row_cnt, 0) + coalesce(b.row_cnt, 0) as row_cnt,
    a.median_bucket_engagementrate_quarterly as median_bucket_engagementrate_quarterly,
    coalesce(a.actor_cnt, 0) + coalesce(b.actor_cnt, 0) as actor_cnt

from events_agg_groupuid a
left join {{ ref('msrscenario_event_candidates_and_sugg_without_interaction_summary') }} b
on a.usecasename = b.usecasename 
and a.factorName = b.factorName
and a.channel = b.channel
and a.event_date = b.event_date
and a.event_quarter = b.event_quarter
and a.configCountryCode = b.configCountryCode
and a.repTeamName = b.repTeamName
and a.engagementsegment_quarterly = b.engagementsegment_quarterly
and a.ActorName = b.ActorName
and a.recordType = b.recordType
and a.productName = b.productName
and a.strategyname = b.strategyname
-- if combination has 'All', add '4' to 'All'

union all

select
    coalesce(a.usecasename, b.usecasename) usecasename,
    coalesce(a.factorName, b.factorName) factorName,
    coalesce(a.strategyname, b.strategyname) strategyname,
    coalesce(a.channel, b.channel) channel,
    coalesce(a.event_date, b.event_date) event_date,
    coalesce(a.event_quarter, b.event_quarter) event_quarter,
    'All actions' as strategicCategoryGroupUid,
    coalesce(a.engagementsegment_quarterly, b.engagementsegment_quarterly) engagementsegment_quarterly,
    coalesce(a.configCountryCode, b.configCountryCode) configCountryCode,
    coalesce(a.repTeamName, b.repTeamName) repTeamName,
    coalesce(a.productName, b.productName) productName,
    coalesce(a.ActorName, b.ActorName) ActorName,
    coalesce(a.recordType, b.recordType) recordType,
    coalesce(a.account_cnt, 0) + coalesce(b.account_cnt, 0) as account_cnt,
    coalesce(a.suggestion_cnt, 0) + coalesce(b.suggestion_cnt, 0) as suggestion_cnt,
    coalesce(a.interaction_cnt, 0) + coalesce(b.interaction_cnt, 0) as interaction_cnt,
    0 as strategic_account_cnt,
    b.row_cnt as row_cnt,
    b.median_bucket_engagementrate_quarterly as median_bucket_engagementrate_quarterly,
    b.actor_cnt as actor_cnt

from {{ ref('msrscenario_event_candidates_and_sugg_without_interaction_summary') }} b
left join events_agg_groupuid a
on a.usecasename = b.usecasename 
and a.factorName = b.factorName
and a.channel = b.channel
and a.event_date = b.event_date
and a.event_quarter = b.event_quarter
and a.configCountryCode = b.configCountryCode
and a.repTeamName = b.repTeamName
and a.engagementsegment_quarterly = b.engagementsegment_quarterly
and a.ActorName = b.ActorName
and a.recordType = b.recordType
and a.productName = b.productName
and a.strategyname = b.strategyname
where a.event_date is null
-- if combination only has '4', add 'All'