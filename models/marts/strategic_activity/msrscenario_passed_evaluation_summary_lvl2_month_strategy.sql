{{ config(materialized='table') }}

select
    coalesce(factorName, 'All factors') factorName,
    -- coalesce(cast(repActionTypeId as varchar), 'All actions') repActionTypeId,
    case when GROUPING(channel) = 0 then channel else 'All channels' end as channel,
    concat(event_yearmonth, '-01') event_date,
    'Not used' as event_quarter,
    coalesce(strategicActionCategoryId, '600') strategicCategoryGroupUid,
    coalesce(configCountryCode, 'All countries') configCountryCode,
    coalesce(repTeamName, 'All rep teams') repTeamName,
    'All usecases' as tagName,
    strategyname,
    count(distinct accountId) account_cnt,
    count(1) row_cnt
from
    {{ ref('msrscenario_passed_evaluations_lvl2') }}
group by
    grouping sets (
        (factorname, event_yearmonth, strategyname),
        (factorname, event_yearmonth, strategyname, channel),
        (factorname, event_yearmonth, strategyname, configCountryCode),
        (factorname, event_yearmonth, strategyname, repTeamName),
        (factorname, event_yearmonth, strategyname, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, channel, configCountryCode),
        (factorname, event_yearmonth, strategyname, channel, repTeamName),
        (factorname, event_yearmonth, strategyname, channel, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, configCountryCode, repTeamName),
        (factorname, event_yearmonth, strategyname, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, channel, configCountryCode, repTeamName),
        (factorname, event_yearmonth, strategyname, channel, configCountryCode, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, channel, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, configCountryCode, repTeamName, strategicActionCategoryId),
        (factorname, event_yearmonth, strategyname, channel, configCountryCode, repTeamName, strategicActionCategoryId)

    )