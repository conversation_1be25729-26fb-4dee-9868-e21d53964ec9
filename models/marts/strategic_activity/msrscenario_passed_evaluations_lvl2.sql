{{ config(materialized='table') }}

select
    a.factorName,
    a.channel,
    a.configCountryCode,
    a.repTeamName,
    a.accountId,
    a.tagName,
    a.strategyname,
    a.event_yearmonth,
    a.event_quarter,
    case
        when b.accountuid is not null and b.isRecommended = 1 then 'passed_eval_with_suggestion'
        when b.accountuid is not null and b.isRecommended = 0 then 'passed_eval_with_candidate'
        else 'passed_eval_without_candidate' -- constrained
    end eval_match_status,
    case
        when b.accountuid is not null and b.isRecommended = 1 then '601'
        when b.accountuid is not null and b.isRecommended = 0 then '602'
        else '603' -- constrained
    end strategicActionCategoryId,
    min(runDate) firstRunDate,
    max(runDate) lastRunDate
from
    {{ ref("msrscenario_all_evaluations_lvl2") }} a
    left join {{ ref("suggestion_candidate_factor_dtl_usecases_v") }} b
    on a.runUID = b.runUID
    and a.factoruid = b.factoruid
    and a.accountuid = b.accountuid
where a.status = 1
group by a.factorName,
    a.channel,
    a.configCountryCode,
    a.repTeamName,
    a.accountId,
    a.tagName,
    a.strategyname,
    a.event_yearmonth,
    a.event_quarter,
    case
        when b.accountuid is not null and b.isRecommended = 1 then 'passed_eval_with_suggestion'
        when b.accountuid is not null and b.isRecommended = 0 then 'passed_eval_with_candidate'
        else 'passed_eval_without_candidate' -- constrained
    end ,
    case
        when b.accountuid is not null and b.isRecommended = 1 then '601'
        when b.accountuid is not null and b.isRecommended = 0 then '602'
        else '603' -- constrained
    end