{{ config(materialized='table') }}

with interaction_level_of_detail_param as
(
select
coalesce(params.interaction_level_of_detail , 'Interaction-factor level') param_interaction_level_of_detail
from  {{ source('manually_maintained','param_msrscenario') }} params
where params.uid = 'default'
),
filtered_level as (
    select * from {{ ref('msrscenario_event_candidates_without_interaction_lvl1') }} a, interaction_level_of_detail_param p
    where a.recordType = p.param_interaction_level_of_detail

    union all

    select * from {{ ref('msrscenario_event_candidates_without_interaction_lvl2') }} a, interaction_level_of_detail_param p
    where a.recordType = p.param_interaction_level_of_detail

    union all

    select * from {{ ref('msrscenario_event_candidates_without_interaction_lvl3') }} a, interaction_level_of_detail_param p
    where a.recordType = p.param_interaction_level_of_detail
)
select
        e.recordType,
        e.usecasename,
        e.strategyname,
        e.accountuid,
        e.suggestionreferenceid,
        e.factorname,
        e.actoruid,
        at.configCountryCode,
        at.repteamname,
        coalesce(p.productname, 'Not product level') as productname,
        event_yearmonth,
        event_quarter,
        acm_s.channelname channel,
        engq.engagementsegment_median as engagementsegment_quarterly,
        engq.median_bucket_engagementrate as median_bucket_engagementrate_quarterly

    from filtered_level e
    left join {{ ref('actor_v') }} a on a.externalid = e.actoruid
    left join {{ ref('actorteamactor_v') }} aa on aa.repid = a.repid
    left join {{ ref('actorteam_v') }} at on aa.repteamid = at.repteamid
    left join {{ ref('actionchannelmap_v') }} acm_s on acm_s.actortypeid = a.reptypeid and acm_s.actiontypeid = e.detailRepActionTypeId
    left join {{ ref('product_v') }} p on p.externalid = e.productuid
    left join {{ ref('imp_repengagementsegmentation_quarterly_v') }} engq on e.actoruid = engq.repuid and event_quarter  = concat(date_format(engq.periodvalue, '%Y'), '-Q', cast(quarter(engq.periodvalue) as varchar))

    union all

    select e.recordType,
        e.usecasename,
        e.strategyname,
        e.accountuid,
        e.suggestionreferenceid,
        e.factorname,
        e.actoruid,
        e.configCountryCode,
        e.repteamname,
        e.productname,
        date_format(e.eventdatetimeutc, '%Y-%m') event_yearmonth,
        concat(date_format(e.eventdatetimeutc, '%Y'), '-Q', cast(quarter(e.eventdatetimeutc) as varchar)) event_quarter,
        e.channel,
        e.engagementsegment_quarterly,
        e.median_bucket_engagementrate_quarterly
    from {{ ref('msrscenario_events') }} e
    where e.strategicActionCategoryId = 501

