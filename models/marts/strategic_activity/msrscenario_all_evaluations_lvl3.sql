{{ config(materialized='view') }}

select 
    a.runUID, 
    a.factorUID, 
    coalesce(a.factorName, 'Channel Execution') factorName,
    a.factorType,
    a.accountId,
    a.accountUID, 
    -- a.tags,
    a.evaluation_order,
    -- fn.factorname as configFactorName,
    coalesce(fn.tagName, 'Channel Execution') tagName,
    coalesce(fn.strategyname, 'Undefined Strategy') strategyname,
    a.status,
    a.repActionTypeId,
    acm.channelName as channel,
    a.productId,
    a.productUID,
    p.productName,
    r.seConfigId,
    r.startDateLocal runDate,
    date_format(r.startDateLocal, '%Y-%m') event_yearmonth,
    concat(date_format(r.startDateLocal, '%Y'), '-Q', cast(quarter(r.startDateLocal) as varchar)) event_quarter,
    dc.repTeamId,
    rt.repTeamName,
    dc.configCountryCode
from {{ ref("factor_evaluation_v") }} a
join {{ ref('sparkdserun_v') }} r 
    on a.runUID = r.runUID
join {{ ref('dseconfig_v') }} dc
    on r.seConfigId = dc.seConfigId
left join {{ ref('akt_rep_team_v') }} rt
    on dc.repTeamId = rt.repTeamId
left join {{ ref('product_v') }} p
    on p.productId = a.productId
left join {{ ref('actionchannelmap_v') }} acm
    on acm.actionTypeId = a.repActionTypeId and acm.actorTypeName = 'FieldRep'
left join (
        select
            f.runuid,
            f.factoruid,
            f.productuid,
            min(factorname) as factorname,
            min(tagname) as tagname,
            max(strategyname) as strategyname
        from {{ ref('sparkdserunconfigfactor_v') }} f
        left join {{ ref('sparkdserunconfigfactortag_v') }} t
            on f.runuid = t.runuid
            and f.factoruid = t.factoruid
            and t.tagtype = 'USECASE'            
        group by
            f.runuid,
            f.factoruid,
            f.productuid) as fn
    on fn.runuid = a.runuid
    and fn.factoruid = a.factoruid
    and fn.productuid = a.productuid

where a.factorType = 'triggerFactor'