import logging.config
import time
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
import pandas as pd
import numpy as np
import getopt, sys, os

log_config = {
    "version":1,
    "root":{
        "handlers" : ["console", "file"],
        "level": "ERROR"
    },
    "handlers":{
        "console":{
            "formatter": "std_out",
            "class": "logging.StreamHandler",
            "level": "ERROR"
        },
        "file":{
            "formatter":"std_out",
            "class":"logging.FileHandler",
            "level":"ERROR",
            "filename":"all_messages.log"
        }
    },
    "formatters":{
        "std_out": {
            "format": "%(levelname)s : %(module)s : %(funcName)s : %(message)s",
        }
    },
}

logging.config.dictConfig(log_config)
logger = logging.getLogger("athena_python_examples")


def get_transition_array(path):
  '''
    This function takes as input a user journey (string) where each state transition is marked by a >. 
    The output is an array that has an entry for each individual state transition.
  '''
  state_transition_array = path.split(">")
  initial_state = state_transition_array[0]
  
  state_transitions = []
  for state in state_transition_array[1:]:
    state_transitions.append(initial_state.strip()+' > '+state.strip())
    initial_state =  state
  
  return state_transitions


def convert_to_string_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    print("sub:" + substr)
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
       arr2.append(x.strip())

    return arr2

def convert_to_double_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
       arr2.append(float(x.strip()))

    return arr2


def get_transition_probability_graph(df, msrscenariouid, removal_state = "null"):
  '''
  This function calculates a subset of the transition probability graph based on the state to exclude
      removal_state: channel that we want to exclude from our Transition Probability Matrix
  returns subset of the Transition Probability matrix as pandas Dataframe
  '''
  print("inside get_transition_probability_graph: " + removal_state)
 
  transition_probability_pandas_df = None
  
  # Get the transition probability graph without any states excluded if the removal_state is null
  if removal_state == "null":
    filter_df = df[(df.msrscenariouid == msrscenariouid)]
    agg_df = filter_df.groupby('start_state').agg(end_state=('end_state',list), transition_probability=('transition_probability',list)).reset_index()
    agg_df = agg_df[agg_df['end_state'].map(len) > 0]
    transition_probability_pandas_df = agg_df.rename(columns={'end_state':'next_stages', 'transition_probability':'next_stage_transition_probabilities'})

  # Otherwise, get the transition probability graph with the specified channel excluded/removed
  else:
    filter_df = df
    filter_df['end_state2'] = np.where(((df.msrscenariouid == msrscenariouid) & (df.start_state != removal_state) & df['end_state'] == removal_state), 'Null', df['end_state'])
    filter_df = filter_df[((df.msrscenariouid == msrscenariouid) & (df.start_state != removal_state))]
    agg_df = filter_df.groupby('start_state').agg(end_state=('end_state2',list), transition_probability=('transition_probability',list)).reset_index()
    agg_df = agg_df[agg_df['end_state'].map(len) > 0]
    transition_probability_pandas_df = agg_df.rename(columns={'end_state':'next_stages', 'transition_probability':'next_stage_transition_probabilities'})

  print(transition_probability_pandas_df)
  transition_probability_pandas_df["next_stages"] = np.array(transition_probability_pandas_df["next_stages"])
  transition_probability_pandas_df["next_stage_transition_probabilities"] = np.array(transition_probability_pandas_df["next_stage_transition_probabilities"])

  return transition_probability_pandas_df


def calculate_conversion_probability(transition_probability_pandas_df, calculated_state_conversion_probabilities, visited_states, current_state="Start"):
  '''
  This function calculates total conversion probability based on a subset of the transition probability graph
    transition_probability_pandas_df: This is a Dataframe that maps the current state to all probable next stages along with their transition probability
    removal_state: the channel that we want to exclude from our Transition Probability Matrix
    visited_states: set that keeps track of the states that have been visited thus far in our state transition graph.
    current_state: by default the start state for the state transition graph is Start state
  returns conversion probability of current state/channel 
  '''
 
  #If the customer journey ends with conversion return 1
  if current_state=="Conversion":
    return 1.0
  
  #If the customer journey ends without conversion, or if we land on the same state again, return 0.
  #Note: this step will mitigate looping on a state in the event that a customer path contains a transition from a channel to that same channel.
  elif (current_state=="Null") or (current_state in visited_states):
    return 0.0
  
  #Get the conversion probability of the state if its already calculated
  elif current_state in calculated_state_conversion_probabilities.keys():
    return calculated_state_conversion_probabilities[current_state]
  
  else:
  #Calculate the conversion probability of the new current state
    #Add current_state to visited_states
    visited_states.add(current_state)

    #Get all of the transition probabilities from the current state to all of the possible next states
    current_state_transition_df = transition_probability_pandas_df.loc[(transition_probability_pandas_df.start_state==current_state)]

    #Get the next states and the corresponding transition probabilities as a list.
    if (len(current_state_transition_df.next_stages) == 0):
       return 0.0

    next_states = current_state_transition_df.next_stages.to_list()[0]
    next_states_transition_probab = current_state_transition_df.next_stage_transition_probabilities.to_list()[0]

    #print("all next states:" + str(type(next_states)))
    #print("all next states type:" + str(type(next_states)))

    #print("all_next_states_transition_probab:" + str(type(next_states_transition_probab)))
    #print("all_next_states_transition_probab type:" + str(type(next_states_transition_probab)))
    
    #This will hold the total conversion probability of each of the states that are candidates to be visited next from the current state.
    current_state_conversion_probability_arr = []
    
    #Call this function recursively until all states in next_states have been incorporated into the total conversion probability
    import copy
    #Loop over the list of next states and their transition probabilities recursively
    for next_state, next_state_tx_probability in zip(next_states, next_states_transition_probab):
      #print(next_states)
      #print(next_states_transition_probab)
      #print("next:" + str(next_state))
      #print("next_prob:" + str(next_state_tx_probability))
      current_state_conversion_probability_arr.append(next_state_tx_probability * calculate_conversion_probability(transition_probability_pandas_df, calculated_state_conversion_probabilities, copy.deepcopy(visited_states), next_state))
    
    #Sum the total conversion probabilities we calculated above to get the conversion probability of the current state.
    #Add the conversion probability of the current state to our calculated_state_conversion_probabilities dictionary.
    calculated_state_conversion_probabilities[current_state] =  sum(current_state_conversion_probability_arr)
    
    #Return the calculated conversion probability of the current state.
    return calculated_state_conversion_probabilities[current_state]


def model(dbt, fal):
    dbt.config(materialized="table")
#    dbt.config(fal_environment="ml")
    df: pd.DataFrame = dbt.ref("msrscenario_transition_matrix")
    scenariouid = "default"
    
    print('get_transition_probability_graph')
    transition_probability_pandas_df = get_transition_probability_graph(df, scenariouid);

    print('get_conversion_probability')
    total_conversion_probability = calculate_conversion_probability(transition_probability_pandas_df, {}, visited_states=set(), current_state="Start");

    total_conversion_pandas_df = pd.DataFrame({'msrscenariouid': [scenariouid], 'conversion_probability': [total_conversion_probability]})

    removal_effect_per_channel = {}
    for channel in transition_probability_pandas_df.start_state.to_list():
           if channel!="Start":
               transition_probability_subset_pandas_df = get_transition_probability_graph(df, scenariouid, removal_state=channel)
               new_conversion_probability =  calculate_conversion_probability(transition_probability_subset_pandas_df, {}, visited_states=set(), current_state="Start")
               if (total_conversion_probability > 0.0):
                   removal_effect_per_channel[channel] = round(((total_conversion_probability-new_conversion_probability)/total_conversion_probability), 2)
               else:
                   removal_effect_per_channel[channel] = 1.0

    conversion_attribution={}
 
    for channel in removal_effect_per_channel.keys():
            if (sum(removal_effect_per_channel.values()) > 0.0):
                conversion_attribution[channel] = round(removal_effect_per_channel[channel] / sum(removal_effect_per_channel.values()), 2)
            else:
                conversion_attribution[channel] = 0.0

 
    channels = list(conversion_attribution.keys())
    conversions = list(conversion_attribution.values())

    conversion_pandas_df= pd.DataFrame({'attribution_model': 
                                             ['markov_chain' for _ in range(len(channels))], 
                                            'channel':channels, 
                                            'attribution_percent': conversions})

    conversion_pandas_df['msrscenariouid'] = scenariouid
    conversion_pandas_df['conversion_probability'] = total_conversion_probability


    return conversion_pandas_df
