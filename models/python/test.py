# read AWS credentials and load AWS Athena table called ABC
import boto3
aws_access_key_id = "your_aws_access_key_id"
aws_secret_access_key = "your_aws_secret_access_key"
client = boto3.client(
    'athena',
    aws_access_key_id=aws_access_key_id,
    aws_secret_access_key=aws_secret_access_key,
    region_name='us-east-1'
)
query_execution_id = client.start_query_execution(
    QueryString="select * from ABC",
    QueryExecutionContext={
        'Database': 'ABC'
    },
    ResultConfiguration={
        'OutputLocation': 's3://ABC/output'
    }
)
print(query_execution_id)
