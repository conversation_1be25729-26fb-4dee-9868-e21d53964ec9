import pandas as pd
from top2vec import Top2Vec

def model(dbt, fal):
    dbt.config(materialized="table")
#    dbt.config(fal_environment="topicml")
    df: pd.DataFrame = dbt.ref("message_v")

    model = Top2Vec(df['messagedescription'].values, embedding_model='universal-sentence-encoder')

    print(model.get_num_topics())

    topic_words = model.topic_words

    topic_mapping = model.hierarchical_topic_reduction(num_topics=20)

    topic_words = model.topic_words_reduced

    print(topic_words)

    cols = [];

    idx = 0;
    for x in topic_words:
        dict = {}
        idx = idx + 1
        words = '';
        dict['topic'] = idx
        for w in x:
           words = words + w + ",";
        dict['words'] = words;
        cols.append(dict) 

    print(cols)            

    outdf = pd.DataFrame(cols)

    return outdf

