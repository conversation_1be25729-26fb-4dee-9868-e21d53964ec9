{{ config(materialized='view') }}

-- depends_on: {{ source('archivedata', 'sales_account_level') }}

{% set query %}
   SELECT table_name FROM information_schema.tables
   WHERE table_schema like '{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}'
     AND table_name = 'sales_account_level'
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}
     {% if results.columns[0][0] %}
        {% set tabname = results.columns[0][0]  %}

    {{ build_latest_sql('sales_account_level','account_identifier, product_identifier, data_point_unit, sales_date','cast(updatedatyear as int) desc, cast(updatedatmonth as int) desc, cast(updatedatday as int) desc') }}

      {% else %}

    SELECT
        CAST(NULL as VARCHAR) as account_identifier,
        CAST(NULL as VARCHAR) as product_identifier,
        CAST(NULL as VARCHAR) as datapointunit,
        CAST(NULL as VARCHAR) as attribute1,
        CAST(NULL as VARCHAR) as attribute2,
        CAST(NULL as VARCHAR) as attribute3,
        CAST(NULL as DATE) as salesdate,
        CAST(NULL as VARCHAR) as periodtype,
        CAST(NULL as DOUBLE)  as datapointvalue,
        CAST(NULL as VARCHAR) as countrycode

    WHERE false

     {% endif %}
{% endif %}


