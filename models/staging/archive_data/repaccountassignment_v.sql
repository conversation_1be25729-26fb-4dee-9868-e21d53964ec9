{{ config(materialized='view') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

    -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

-- since we do snapshot of repaccountassignment at the end of every month, we need to get the max date of the snapshot
-- and use it to filter the data
{% set default_max_date_query %}
  select coalesce(date_format(date_add('month', -1, date_trunc('MONTH', max(updatedat))), '%Y-%m-%d'), date_format(date_trunc('MONTH', now()), '%Y-%m-%d')) as maxupdated from {{ref('repaccountassignment_cdc_v')}}
{% endset %}

{% set results = run_query(default_max_date_query) %}
{% if execute %}
{% set max_date = results.columns[0].values() %}
{% else %}
{% set max_date = [] %}
{% endif %}

{{ build_latest_sql(table_name = 'repaccountassignment',
                    idfields = 'repid,accountid',
                    wherecondition = "updatedat >= date '" ~ max_date[0] ~ "'",
                    additional_fields = ",  cast(null as integer) as ismytarget_rpt") 
}}

{% endif %}

