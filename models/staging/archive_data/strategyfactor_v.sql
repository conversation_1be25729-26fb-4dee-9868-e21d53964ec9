{{ config(materialized='view') }}

{% set var_strategyfactor_exists = build_check_athena_table_exists(var('customer', 'default') + '_' + var('src_env', 'prod'), 'strategyfactor') %}

{% if var_strategyfactor_exists == 1 %}

{{ build_latest_sql('strategyfactor','strategyid,factoruid,seconfigid', 'cast(updatedatyear as int) DESC, cast(updatedatmonth as int) DESC, cast(updatedatday as int) DESC') }}

{% else %}

select 0 as seconfigid, 'NA' as factoruid, '0' as strategyid, 'NA' as strategyname, 'US' as countrycode

{% endif %}

