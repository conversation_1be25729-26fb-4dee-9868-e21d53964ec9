{{ config(materialized='view') }}

{% set custom_models = var("customized_models").split(",") %}

{% if this.identifier in custom_models %}

 -- depends on: {{ ref(var("customer") ~ '_' ~ this.identifier) }}
    select
        *
    from  {{ ref(var("customer") ~ '_' ~ this.identifier) }}

{% else %}

select * ,  cast(null as integer) as ismytarget_rpt from {{ source('archivedata', 'repaccountassignment') }}

{% endif %}

