{{ config(materialized='view') }}

{% set var_strategy_exists = build_check_athena_table_exists(var('customer', 'default') + '_' + var('src_env', 'prod'), 'strategy') %}

{% if var_strategy_exists == 1 %}

{{ build_latest_sql('strategy','strategyid',wherecondition="status in ('ACTIVE','PROCESSING','INPROGRESS')") }}

{% else %}

select 0 as strategyid, 'NA' as name, 'US' as countrycode, 'NA' as repteamuid, 'NA' as productuid, 0 as goalid, 'NA' as messagetopics, cast(null as varchar) as audienceconditions,
      'NA' as startperiod, 0 as startperiodid, 'NA' as endperiod, 0 as endperiodid, 0 as strategyrank, 'NA' as status, false as isdeleted, cast(null as timestamp) as createdat,
      'NA' as createdby, cast(null as timestamp) as updatedat,
      'NA' as updatedby, 'NA' as chunk_date_value, '2000' as updatedatyear, '01' as updatedatmonth, '01' as updatedatday, 1 as row_number

{% endif %}