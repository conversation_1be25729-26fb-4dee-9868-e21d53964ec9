version: 2

sources:
  - name: archivedata  
    description: “Archived data from MySql”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}"
    tables:
        - name: account_dse
          description: "engine account table"
        - name: product
          description: "engine product table"
        - name: accountproduct
          description: "engine accountproduct table"
        - name: facility
          description: "engine facility table"
        - name: rep
          description: "engine rep table"
        - name: message
          description: "engine message table"
        - name: repteam
          description: "engine repteam table"
        - name: rpt_suggestion_delivered_stg
          description: "rpt_suggestion_delivered_stg table"
        - name: accounttype
          description: "accounttype"
        - name: reptype
          description: "reptype"
        - name: repactiontype
          description: "repactiontype"
        - name: actiongroup
          description: "actiongroup"
        - name: actionchannelmap
          description: "actionchannelmap"
        - name: actortypeaccounttypemap
          description: "actortypeaccounttypemap"
        - name: channel 
          description: "channel"
        - name: channeltype 
          description: "channeltype"
        - name: channelcategory 
          description: "channelcategory"
        - name: interactiontype
          description: "interactiontype"
        - name: productinteractiontype
          description: "productinteractiontype"
        - name: interaction
          description: "interaction"
        - name: interactionaccount
          description: "interactionaccount"
        - name: interactionproduct
          description: "interactionproduct"
        - name: call2_key_message_vod__c
          description: "call2_key_message_vod__c"
        - name: key_message_vod__c
          description: "key_message_vod__c"
        - name: clm_presentation_vod__c
          description: "clm_presentation_vod__c"
        - name: akt_stablemessage
          description: "akt_stablemessage"
        - name: akt_stablemessage_physicalmessage
          description: "akt_stablemessage_physicalmessage"
        - name: call2_vod__c
          description: "call2_vod__c"
        - name: call2_detail_vod__c
          description: "call2_detail_vod__c"
        - name: sent_email_vod__c
          description: "sent_email_vod__c"
        - name: recordtype
          description: "recordtype"
        - name: user
          description: "user"
        - name: product_vod__c
          description: "product_vod__c"
        - name: product_metrics_vod__c
          description: "product_metrics_vod__c"
        - name: repaccountassignment
          description: "repaccountassignment"
        - name: repproductauthorization
          description: "repproductauthorization"
        - name: repteamrep
          description: "repteamrep"
        - name: dseconfig
          description: "dseconfig"
        - name: approved_document_vod__c
          description: "approved_document_vod__c"
        - name: sparkdserun
          description: "sparkdserun"
        - name: sparkdserunrepdate
          description: "sparkdserunrepdate"
        - name: sparkdserunrepdatesuggestion
          description: "sparkdserunrepdatesuggestion"
        - name: sparkdserunrepdatesuggestiondetail
          description: "sparkdserunrepdatesuggestiondetail"
        - name: sparkdserunrepdatesuggestionreason
          description: "sparkdserunrepdatesuggestionreason"
        - name: sparkdserunconfigfactor
          description: "sparkdserunconfigfactor"
        - name: sparkdserunconfigfactortag
          description: "sparkdserunconfigfactortag"
        - name: sparkdserunconfigfactortagparam
          description: "sparkdserunconfigfactortagparam"
        - name: sparkdserunaccount
          description: "sparkdserunaccount"
        - name: sparkdserunaccountrep
          description: "sparkdserunaccountrep"
        - name: sparkdserunaccountenhancedinsight
          description: "sparkdserunaccountenhancedinsight"
        - name: account_cs
          description: "sfdc account table"
        - name: strategytarget
          description: "startegytarget"
        - name: targetsperiod
          description: "targetsperiod"
        - name: event
          description: "engine event table"
        - name: eventtype
          description: "engine eventtype table"  
        - name: akt_replicense
          description: "akt_replicense table" 
        - name: akt_replicense_arc
          description: "historical data for rep license"  
        - name: strategy
          description: "Strategy data scc schema"
        - name: kpitype
          description: "KPIType data scc schema" 
        - name: kpi
          description: "KPI data for rep license"
        - name: goalkpiaudiencetypedefmapping
          description: "GoalKPIAudienceTypeDefMapping data" 
        - name: goal
          description: "Goal data scc schema"
        - name: fiscalperiod
          description: "FiscalPeriod data scc schema" 
        - name: fiscalperioddefinition
          description: "FiscalPeriodDefinition data scc schema"         
        - name: attributetypedefinition
          description: "AttributeTypeDefinition data"
        - name: attributetype
          description: "AttributeType data"
        - name: strategyfactor
          description: "startegyfactor"
        - name: repunavailableperiod
          description: "repunavailableperiod"
        - name: holiday
          description: "holiday"
        - name: papi_param_filterproperties
          description: "papi_param_filterproperties from stage schema"      
        - name: sif_accountmapping
          description: "sif_accountmapping from stage schema"
        - name: sif_productmapping
          description: "sif_productmapping from stage schema"
        - name: rpt_dim_calendar
          description: "rpt_dim_calendar"
        - name: rpt_go_live_filter
          description: "rpt_go_live_filter"
        - name: dsesuggestionlifecycle
          description: "dsesuggestionlifecycle"
        - name: sales_account_level
          description: "sales_account_level"
        - name: sales_market_basket
          description: "sales_Market_Basket"
