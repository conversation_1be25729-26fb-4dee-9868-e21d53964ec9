{{ config(materialized='view') }}

-- depends_on: {{ source('archivedata', 'sales_market_basket') }}

{% set query %}
   SELECT table_name FROM information_schema.tables
   WHERE table_schema like '{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}'
     AND table_name = 'sales_market_basket'
 {% endset %}

 {% set results = run_query(query) %}

 {% if execute %}
     {% if results.columns[0][0] %}
        {% set tabname = results.columns[0][0]  %}

    {{ build_latest_sql('sales_market_basket','data_point_unit, market_identifier, product_identifier','cast(updatedatyear as int) desc, cast(updatedatmonth as int) desc, cast(updatedatday as int) desc') }}

      {% else %}

    SELECT
        CAST(NULL as VARCHAR) as market_identifier,
        CAST(NULL as VARCHAR) as product_identifier,
        CAST(NULL as VARCHAR) as data_point_unit,
        CAST(NULL as VARCHAR) as market_short_name,
        CAST(NULL as VARCHAR) as market_long_name,
        CAST(NULL as DATE) as as_of_date,
        CAST(NULL as DATE) as chunk_date_value,
        CAST(NULL as VARCHAR) as countrycode

    WHERE false

     {% endif %}
{% endif %}




