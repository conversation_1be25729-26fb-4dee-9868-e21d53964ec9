{{ config(materialized='view') }}

select customerName,
    deploymentName,
    runUID,
    factorName,
    factorUID,
    factorType,
    tags,
    productId,
    productUID,
    channelId,
    channelUID,
    accountId,
    accountUID,
    repId,
    repUID,
    repActionTypeId,
    repActionTypeUID,
    status,
    rundate,
    configid,
        row_number() OVER (
        PARTITION BY
            accountId,
            repActionTypeId,
            factorUID,
            productId
        ORDER BY
            accountId,
            repActionTypeId,
            factorUID,
            productId,
            rundate desc) as evaluation_order

 from {{ source('engine_generated', 'factor_evaluations') }}