{{ config(materialized='view') }}

SELECT
    a.*,
    b.segmentType AS cj_segmentType,
    b.value AS cj_value,
    b.segment AS cj_segment,
    b.segmentRank AS cj_segmentRank,
    b.rankChange AS cj_rankChange,
    b.attribute AS cj_attribute
FROM
    {{ref('scored_candidates_v')}} a
LEFT JOIN
    {{ref('cj_latest')}} b
ON
    a.accountId = b.accountId
    AND a.productId = b.productId
    AND date_format(date_parse(a.rundate,'%Y-%m-%d'), '%Y-%m') = date_format(date_parse(b.runmonth,'%Y-%m'), '%Y-%m')
    WHERE b.segmentType = 'Combined'