{{ config(materialized='view') }}

with suggestion_factor_tag as (
  select
    runuid,
    factoruid,
    min(tagid) tagid,
    min(tagname) tagname,
    min(tagtype) tagtype
  from {{ ref('sparkdserunconfigfactortag_v') }}
  where tagtype = 'USECASE'
  group by runuid, factoruid
),
suggestion_factor as (
  select
    a.runuid,
    a.factoruid,
    min(a.factorname) factorname,
    min(a.factortype) factortype,
    min(c.strategyid) strategyid,
    min(c.strategyname) strategyname
  from {{ ref('sparkdserunconfigfactor_v') }} a
  inner join {{ ref('sparkdserun_v') }} b on a.runuid = b.runuid
  left join {{ ref('strategyfactor_map_v') }} c on b.seconfigid = c.seconfigid and a.factoruid = c.factoruid
  group by a.runuid, a.factoruid
),
factors as (
  select
    dc.suggestionCandidateUid,
    reason.factorUID as factorUID
  from {{ref('scored_candidates_v')}} dc
  cross join unnest(dc.reasons) as t (reason)
  UNION
  select
    dc.suggestionCandidateUid,
    dc.factorUID as factorUID
  from {{ref('scored_candidates_v')}} dc
  UNION
  select
    dc.suggestionCandidateUid,
    product.factorUID as factorUID
  from {{ref('scored_candidates_v')}} dc
  cross join unnest(dc.products) as t (product)
)
select
  dc.*, f.factorUID reason_factoruid
--   sf.factorname as reason_factorName,
--   sf.factortype as reason_factorType,
--   sf.strategyid  as reason_strategyId,
--   sf.strategyname as reason_strategyName,
--   sft.tagid as reason_tagId,
--   sft.tagname as reason_tagName
from {{ref('scored_candidates_v')}} dc
  left join factors f on dc.suggestionCandidateUid = f.suggestionCandidateUid
-- left join suggestion_factor sf
-- on er.reason_factorUID = sf.factoruid and er.dserunuid = sf.runuid
-- left join suggestion_factor_tag sft
-- on er.reason_factorUID = sft.factoruid;