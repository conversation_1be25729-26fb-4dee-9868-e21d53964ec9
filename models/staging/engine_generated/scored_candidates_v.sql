{{ config(materialized='view') }}

WITH range_limits AS (
    SELECT
        scenario_uid,
        approx_percentile(finalscore, 0.03) AS min_score,
        approx_percentile(finalscore, 0.97) AS max_score
    FROM
        {{ source('engine_generated', 'dco_candidates') }}
    GROUP BY
        scenario_uid
),
category_thresholds AS (
    SELECT
        scenario_uid,
        min_score,
        max_score,
        (max_score - min_score) / 3 AS bin_width,
        min_score + ((max_score - min_score) / 3) AS low_threshold,
        min_score + 2 * ((max_score - min_score) / 3) AS high_threshold
    FROM
        range_limits
),

candidates_without_status as (
SELECT
    dc.*,
    -- ct.bin_width,
    -- ct.low_threshold,
    -- ct.high_threshold,
    CASE
        WHEN dc.finalscore <= ct.low_threshold THEN 'low'
        WHEN dc.finalscore <= ct.high_threshold THEN 'medium'
        ELSE 'high'
    END AS expectedvalue_group
FROM
    {{ source('engine_generated', 'dco_candidates') }} dc
JOIN
    category_thresholds ct ON dc.scenario_uid = ct.scenario_uid
ORDER BY
    dc.scenario_uid, dc.finalscore
),

group_rank as (
    select dco_run_uid, groupid, min(rank) as min_rank, 
    min(suggesteddate) as min_suggestedDate, 
    min(repid) as min_repId 
    from candidates_without_status r 
    where recommended = True and groupid > 0 
    group by dco_run_uid, groupid
),

factor_group_rank as (
    select dco_run_uid, groupidfactor, min(rankfactor) min_factor_rank, 
    min(suggesteddate) as min_suggestedDate, 
    min(repid) as min_repId 
    from candidates_without_status r 
    where recommended = True and groupidfactor > 0 
    group by dco_run_uid, groupidfactor
),

passthrough_group_rank as (
    select dco_run_uid, groupIdPassThrough, 
    min(rankPassThrough) as min_rank_passthrough, 
    min(suggesteddate) as min_suggestedDate, 
    min(repid) as min_repId 
    from candidates_without_status r 
    where recommended = True and groupIdPassThrough > 0 
    group by dco_run_uid, groupIdPassThrough
),



raw_status as (
select results.*,
-- case when recommended = False and dcoReasonText like 'Account/channel has been filtered based on auto-snooze list%' then '01. Auto-expired'
--      when recommended = False and dcoReasonText like 'Rejected: Action value low. Non-optimal action candidate%' then '02. Account-level conflict'
--      when recommended = False and dcoReasonText like 'Rejected: Action value low. Non-optimal action candidate in the Factor group%' then '03. Factor-level conflict'
--      when recommended = False and g.groupId is not null and c.rank != g.min_rank then '02. Account-level conflict'
--      when recommended = False and g.groupId is null and c.rank != 1 then '02. Account-level conflict'
--      when recommended = False and fg.groupIdFactor is not null and c.rankFactor != fg.min_factor_rank then '03. Factor-level conflict'
--      when recommended = False and fg.groupIdFactor is null and c.rankFactor != 1 then '03. Factor-level conflict'
--      when recommended = False and repAvailableCapAdditionalRun < 1 and dcoReasonText like '%. --- Rejected: Maximum rep capacity reached%' then '04. End user capacity'
--      when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableRequiredCap = 0 and channelAvailableAdditionalCap > 0 and dcoReasonText like '%Rejected: Maximum channel capacity reached%' then '05. Channel capacity'
--      when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableAdditionalCap = 0 then '05. Channel capacity'
--      when recommended = False and dcoReasonText like '%Rejected: Number of recommended suggestions exceeded the average Horizon maximum for the rep%' then '06. Horizon planning max exceeded'
--      when recommended = False and dcoReasonText like '%Rejected: Action value is less than threshold%' then '07. Action value below threshold'
--      when recommended = False then '12. Unknown reason'
--      when recommended = True and repAvailableCapRequiredRun > 0 and channelAvailableRequiredCap > 0 then '00. Recommended'
--      when recommended = True and repAvailableCapAdditionalRun > 0 and channelAvailableRequiredCap = 0 and channelAvailableAdditionalCap > 0 then '00. Recommended'
--      end as status_reason

case
     when recommended = False and dcoReasonText like 'Account/channel has been filtered based on auto-snooze list%' then '01. Success unlikely'
     when recommended = False and dcoReasonText like 'Rejected: Action value low. Non-optimal action candidate%' then '06. Low Expected Value'
     when recommended = False and dcoReasonText like '%Rejected: Action value is less than threshold%' then '02. Low Expected Value'
     when recommended = False and repAvailableCapAdditionalRun < 1 and dcoReasonText like '%. --- Rejected: Maximum rep capacity reached%' then '07. Actor Capacity'
     when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableRequiredCap = 0 and channelAvailableAdditionalCap > 0 and dcoReasonText like '%Rejected: Maximum channel capacity reached%' then '08. Channel Capacity'
     when recommended = False and repAvailableCapAdditionalRun > 0 and channelAvailableAdditionalCap = 0 then '08. Channel Capacity'
     when recommended = False and dcoReasonText like '%Rejected: Number of recommended suggestions exceeded the average Horizon maximum for the rep%' then '09. Horizon Capacity'
     when recommended = False then '99. Unknown Reason'
     when recommended = True then '00. Recommended'
     end dco_reason_category,
case
    when recommended = False and results.groupId > 0 and g.groupId is not null then concat(case when results.rank < g.min_rank then 'Another ' else 'Better ' end, case when results.repid != g.min_repId then 'Actor' when results.suggesteddate != g.min_suggestedDate then 'Date' else 'Channel' end, ' selected')
    when recommended = False and results.groupIdPassThrough > 0 and p.groupIdPassThrough is not null then concat(case when results.rankPassThrough < p.min_rank_passthrough then 'Another ' else 'Better ' end, case when results.repid != p.min_repId then 'Actor' when results.suggesteddate != p.min_suggestedDate then 'Date' else 'Channel' end, ' selected')
    else null
    end alternate_recommendation_type,
case
    when recommended = False and ((results.groupId > 0 and g.groupId is not null) or (results.groupIdPassThrough > 0 and p.groupIdPassThrough is not null) ) then ''
    when recommended = False and ((results.groupId > 0 and g.groupId is null and results.rank = 1) or (results.groupIdPassThrough > 0 and p.groupIdPassThrough is null and results.rankPassThrough = 1) ) then '*'
    when recommended = False and ((results.groupId > 0 and g.groupId is null and results.rank > 1) or (results.groupIdPassThrough > 0 and p.groupIdPassThrough is null and results.rankPassThrough > 1) ) then '06. Low Expected Value'
    end alternate_recommendation_exists,
case
    when recommended = False and fg.groupIdFactor is not null then ''
    when recommended = False and fg.groupIdFactor is null then '*'
    end factor_alternate_recommendation_exists


from candidates_without_status results
left join group_rank g 
    on results.dco_run_uid = g.dco_run_uid 
    and results.groupId = g.groupId
left join factor_group_rank fg 
    on results.dco_run_uid = fg.dco_run_uid 
    and results.groupIdFactor = fg.groupIdFactor
left join passthrough_group_rank p 
    on results.dco_run_uid = p.dco_run_uid and results.groupIdPassThrough > 0 and results.groupIdPassThrough = p.groupIdPassThrough
)

select a.*,
case when substring(alternate_recommendation_exists,1,1) = '0' then alternate_recommendation_exists
    when alternate_recommendation_exists = '*' and substring(dco_reason_category,2,1) in ('7', '8', '9')
        then concat( substring(dco_reason_category,1,1), chr(codepoint(cast(substr(dco_reason_category,2,1) as varchar(1))) -4), substring(dco_reason_category,3), alternate_recommendation_exists )
    else dco_reason_category
end status_reason

from raw_status a
