version: 2

sources:
  - name: engine_generated
    description: “Artifacts From Candidate Generator and Optimizer”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: suggestion_candidates
          description: "Suggestion Candidates"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=suggestionCandidates/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-2YEARS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,50', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=suggestionCandidates/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
               - name: id
                 data_type: string
               - name: actorid
                 data_type: struct<id:int,externalId:string>
               - name: accountid
                 data_type: struct<id:int,externalId:string>
               - name: channelinfo
                 data_type: struct<channel:struct<id:int,externalId:string>,actionType:struct<id:int,externalId:string>,actorType:struct<id:int,externalId:string,name:string>>
               - name: isoptimizedbyai
                 data_type: boolean
               - name: suggesteddate
                 data_type: string
               - name: products
                 data_type: array<struct<productId:struct<id:int,externalId:string>,factorUID:string,detailedRepActionType:struct<id:int,externalId:string>,messageInfo:struct<messageSet:struct<id:int,externalId:string>,messageTopic:struct<id:int,externalId:string>,messages:array<struct<id:int,externalId:string>>>>>
               - name: source
                 data_type: struct<engine:string,type:string>
               - name: accountimportance
                 data_type: struct<isCritical:boolean,score:double>
               - name: suggestionimportance
                 data_type: struct<isCritical:boolean,score:double>
               - name: dismissalfollowuptype
                 data_type: string
               - name: primaryfactor
                 data_type: struct<factorId:string,name:string,order:int,tags:array<struct<tagId:int,tagName:string,params:array<struct<tagParamId:int,tagParamName:string,value:string>>>>,segmentFactorIds:array<string>>
               - name: secondaryfactors
                 data_type: array<struct<factorId:string,name:string,order:int,tags:array<struct<tagId:int,tagName:string,params:array<struct<tagParamId:int,tagParamName:string,value:string>>>>,segmentFactorIds:array<string>>>
               - name: dco
                 data_type: struct<channelPrefScore:double,preferred:boolean>
               - name: metadata
                 data_type: struct<runDate:string,configId:int,configName:string,configVersionId:int,runGroupId:int,runUID:string,countryCode:string>
               - name: reasons
                 data_type: array<struct<crmFieldName:string,reasonRank:int,text:string,factorUID:string>>
               - name: suggestedinsight
                 data_type: struct<text:string,rank:int,isCritical:boolean>
               - name: suggestedenhancedinsight
                 data_type: struct<text:string,rank:int,isCritical:boolean>
               - name: suggestionreference
                 data_type: struct<suggestionReferenceId:string,internalSuggestionReferenceId:string>
               - name: surveyuid
                 data_type: string
               - name: suggestiondeliverymode
                 data_type: struct<id:int,externalId:string>

        - name: factor_evaluations
          description: "Factor Evaluations"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=factor-evaluations/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=factor-evaluations/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
               - name: customerName
                 data_type: string
               - name: deploymentName
                 data_type: string
               - name: runUID
                 data_type: string
               - name: factorName
                 data_type: string
               - name: factorUID
                 data_type: string
               - name: factorType
                 data_type: string
               - name: remEnabled
                 data_type: int
               - name: tteEnabled
                 data_type: int
               - name: tags
                 data_type: array<string>
               - name: productId
                 data_type: int
               - name: productUID
                 data_type: string
               - name: channelId
                 data_type: int
               - name: channelUID
                 data_type: string
               - name: accountId
                 data_type: int
               - name: accountUID
                 data_type: string
               - name: repId
                 data_type: int
               - name: repUID
                 data_type: string
               - name: repActionTypeId
                 data_type: int
               - name: repActionTypeUID
                 data_type: string
               - name: status
                 data_type: int
               - name: failureReason
                 data_type: string
               - name: suppressionFactorUID
                 data_type: string

        - name: failed_evaluations
          description: "failed factor Evaluations"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=failed-evaluations/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=failed-evaluations/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
               - name: customerName
                 data_type: string
               - name: deploymentName
                 data_type: string
               - name: runUID
                 data_type: string
               - name: factorName
                 data_type: string
               - name: factorUID
                 data_type: string
               - name: factorType
                 data_type: string
               - name: remEnabled
                 data_type: int
               - name: tteEnabled
                 data_type: int
               - name: tags
                 data_type: array<string>
               - name: productId
                 data_type: int
               - name: productUID
                 data_type: string
               - name: channelId
                 data_type: int
               - name: channelUID
                 data_type: string
               - name: accountId
                 data_type: int
               - name: accountUID
                 data_type: string
               - name: repId
                 data_type: int
               - name: repUID
                 data_type: string
               - name: repActionTypeId
                 data_type: int
               - name: repActionTypeUID
                 data_type: string
               - name: status
                 data_type: int
               - name: failureReason
                 data_type: string
               - name: suppressionFactorUID
                 data_type: string

        - name: dco_candidates
          description: "DCO Suggestion Candidates"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/dco/data/dco_read_write/candidate_output/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: (
              'projection.enabled'='true',
              'projection.isactive.type'='integer',
              'projection.isactive.range'='0,1',
              'projection.rundate.type'='date',
              'projection.rundate.format'='yyyy-MM-dd',
              'projection.rundate.interval'='1',
              'projection.rundate.interval.unit'='DAYS',
              'projection.rundate.range'='NOW-1YEAR,NOW',
              'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/dco/data/dco_read_write/candidate_output/isactive=${isactive}/rundate=${rundate}"
              )
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
              - name: isactive
                data_type: int
              - name: rundate
                data_type: string
          columns:
            - name: suggestionCandidateUid
              data_type: string
            - name: accountId
              data_type: int
            - name: accountUID
              data_type: string
            - name: DSERunUID
              data_type: string
            - name: seConfigId
              data_type: int
            - name: versionId
              data_type: int
            - name: runGroupId
              data_type: int
            - name: repId
              data_type: int
            - name: repUID
              data_type: string
            - name: suggestedDate
              data_type: string
            - name: isSuggestionCritical
              data_type: boolean
            - name: suggestionScore
              data_type: double
            - name: factorUID
              data_type: string
            - name: extProductId
              data_type: int
            - name: productUID
              data_type: string
            - name: channel
              data_type: string
            - name: explanation
              data_type: string
            - name: message
              data_type: string
            - name: reasonText
              data_type: string
            - name: source
              data_type: string
            - name: sourceType
              data_type: string
            - name: countryCode
              data_type: string
            - name: suggestionDeliveryModeId
              data_type: int
            - name: suggestionDeliveryModeName
              data_type: string
            - name: primaryRepActionTypeId
              data_type: int
            - name: detailRepActionTypeUID
              data_type: string
            - name: suggestedRepActionTypeUID
              data_type: string
            - name: suggestedChannelId
              data_type: int
            - name: suggestionReferenceId
              data_type: string
            - name: surveyUID
              data_type: string
            - name: internalSuggestionReferenceId
              data_type: string
            - name: products
              data_type: array<struct<productId:struct<id:int,externalId:string>,factorUID:string,detailedRepActionType:struct<id:int,externalId:string>,messageInfo:struct<messageSet:struct<id:int,externalId:string>,messageTopic:struct<id:int,externalId:string>,messages:array<struct<id:int,externalId:string>>>>>
            - name: reasons
              data_type: array<struct<crmFieldName:string,reasonRank:int,text:string,factorUID:string>>
            - name: suggestionScoreWeighted
              data_type: double
            - name: influenceListValue
              data_type: array<double>
            - name: originalInfluenceListValue
              data_type: array<double>
            - name: influenceUidList
              data_type: array<string>
            - name: groupIdRepDate
              data_type: int
            - name: repDateRank
              data_type: int
            - name: economicRevenue
              data_type: float
            - name: economicCost
              data_type: float
            - name: adjustedRevenue
              data_type: float
            - name: adjustedCost
              data_type: float
            - name: expectedRevenue
              data_type: float
            - name: expectedCost
              data_type: float
            - name: finalScore
              data_type: float
            - name: groupId
              data_type: int
            - name: rank
              data_type: int
            - name: groupIdFactor
              data_type: int
            - name: rankFactor
              data_type: int
            - name: hcp_segment_index
              data_type: int
            - name: hcp_segment
              data_type: string
            - name: recommended
              data_type: boolean
            - name: dcoReasonText
              data_type: string
            - name: channelAvailableRequiredCap
              data_type: float
            - name: channelAvailableAdditionalCap
              data_type: float
            - name: repAvailableCapRequiredRun
              data_type: float
            - name: repAvailableCapAdditionalRun
              data_type: float
            - name: scenario_uid
              data_type: string
            - name: dco_run_uid
              data_type: string
            - name: force
              data_type: double
            - name: productId
              data_type: int
            - name: insight_text
              data_type: string
            - name: is_insight_critical
              data_type: boolean
            - name: insight_rank
              data_type: int
            - name: enh_insight_text
              data_type: string
            - name: is_enh_insight_critical
              data_type: boolean
            - name: enh_insight_rank
              data_type: int
            - name: repNumberOfRemainingInteractions
              data_type: float
            - name: useCaseTagParamsList
              data_type: string
            - name: xAiScaledCompUids
              data_type: array<string>
            - name: xAiScaledCompValues
              data_type: array<double>
            - name: xAiMaxValueComponent
              data_type: string
            - name: xAiMaxValue
              data_type: float
            - name: xAiConfidenceDegree
              data_type: string
            - name: content_recommended
              description: "An array of recommended content structures"
              data_type: array<struct<productId:int,productUid:string,productName:string,
                potentialMessages:array<struct<rank:int,cmsMessageUid:string,messageUID:string,message_name:string,presentationName:string,
                fragment_json:array<struct<fragment_rank:int,messageuid:string,cms_message_uid:string,message_name:string>>>>,
                content:struct<id:int,uid:string,cmsUid:string,phyUid:string,name:string,topic:string,desc:string,insight:string,reason:string,
                source:string,presentationName:string,fragmentsCMSUidList:string,fragmentsUidList:string,fragmentsNameList:string>>>
            - name: groupIdPassThrough
              data_type: int
            - name: rankPassThrough
              data_type: int


        - name: cj_output
          description: "Customer Journey Output"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/customer-journey/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: (
              'projection.enabled'='true',
              'projection.runmonth.type'='date',
              'projection.runmonth.format'='yyyy-MM',
              'projection.runmonth.range'='NOW-1YEAR,NOW',
              'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/customer-journey/runmonth=${runmonth}"
              )
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
              - name: runmonth
                data_type: string
          columns:
            - name: accountId
              data_type: int
            - name: productId
              data_type: int
            - name: yearMonth
              data_type: string
            - name: segmentType
              data_type: string
            - name: value
              data_type: double
            - name: segment
              data_type: string
            - name: segmentRank
              data_type: int
            - name: rankChange
              data_type: int
            - name: attribute
              data_type: double

        - name: cp_output
          description: "Channel Propensity Output"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/channel_propensity/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: (
              'projection.enabled'='true',
              'projection.runmonth.type'='date',
              'projection.runmonth.format'='yyyy-MM-dd',
              'projection.runmonth.range'='NOW-1YEAR,NOW',
              'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/channel_propensity/runmonth=${runmonth}"
              )
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
              - name: runmonth
                data_type: date
          columns:
            - name: accountId
              data_type: int
            - name: accountUid
              data_type: string
            - name: date
              data_type: date
            - name: send_prob_cali
              data_type: double
            - name: visit_prob_cali
              data_type: double
            - name: virtual_visit_prob_cali
              data_type: double
            - name: send_prob_cali_segment
              data_type: string
            - name: visit_prob_cali_segment
              data_type: string
            - name: virtual_visit_prob_cali_segment
              data_type: string

        - name: Holiday
          description: "holidays"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=Holiday/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=Holiday/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: date
              data_type: string
            - name: configCountryCode
              data_type: string

        - name: RepAccountAssignment
          description: "rep account assignment"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepAccountAssignment/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepAccountAssignment/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repId
              data_type: integer
            - name: accountId
              data_type: integer
            - name: startDate
              data_type: string
            - name: endDate
              data_type: string
            - name: accountName
              data_type: string

        - name: RepProductAuthorization
          description: "rep product authorization"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepAccountAssignment/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepProductAuthorization/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repProductAuthorizationId
              data_type: integer
            - name: repId
              data_type: integer
            - name: productid
              data_type: integer

        - name: RepAccountHold
          description: "rep account hold"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepAccountHold/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepAccountHold/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repAccountHoldId
              data_type: integer
            - name: repId
              data_type: integer
            - name: accountId
              data_type: integer
            - name: startDate
              data_type: string
            - name: endDate
              data_type: string
            - name: channelId
              data_type: integer

        - name: RepUnavailablePeriod
          description: "rep unavailable period"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepUnavailablePeriod/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepUnavailablePeriod/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repId
              data_type: integer
            - name: startDate
              data_type: string
            - name: endDate
              data_type: string

        - name: SuggestibleReps
          description: "suggestible reps"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=SuggestibleReps/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=SuggestibleReps/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repId
              data_type: integer
            - name: repTypeId
              data_type: integer
            - name: workWeekId
              data_type: integer
            - name: repUID
              data_type: string
            - name: repName
              data_type: string
            - name: timeZoneId
              data_type: integer
            - name: configCountryCode
              data_type: string
            - name: isActivated
              data_type: boolean
            - name: isDeleted
              data_type: boolean
            - name: seConfigId
              data_type: integer
            - name: repTypeUId
              data_type: string

        - name: RepTeam
          description: "Rep Team for the config"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepTeam/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepTeam/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repTeamId
              data_type: integer
            - name: repTeamName
              data_type: string
            - name: externalId
              data_type: string
            - name: seConfigId
              data_type: integer
            - name: configCountryCode
              data_type: string
            - name: isDeleted
              data_type: boolean

        - name: RepTeamRep
          description: "rep team rep"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepTeamRep/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-6MONTHS,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=RepTeamRep/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: repId
              data_type: integer
            - name: repTeamId
              data_type: integer
            - name: createdAt
              data_type: string
            - name: updatedAt
              data_type: string

        - name: DSEConfig
          description: "dse config"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=DSEConfig/"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('projection.rundate.type'='date','projection.enabled'='true','projection.rundate.range'='NOW-1MONTH,NOW','projection.rundate.format'='yyyy-MM-dd', 'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS', 'projection.configid.type'='integer', 'projection.configid.range'='1,300', 'storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/env={{ var('src_env', 'prod') }}/datatype=DSEConfig/rundate=${rundate}/configid=${configid}")
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: rundate
                 data_type: string
               - name: configid
                 data_type: string
          columns:
            - name: seconfigid
              data_type: integer
            - name: seconfigname
              data_type: string
            - name: description
              data_type: string
            - name: numRunGroups
              data_type: integer
            - name: numUsers
              data_type: integer
            - name: numActiveUsers
              data_type: integer
            - name: numAccounts
              data_type: integer
            - name: configCountryCode
              data_type: string
            - name: isReadOnly
              data_type: boolean
            - name: createdAt
              data_type: timestamp
            - name: createdBy
              data_type: string
            - name: updatedAt
              data_type: timestamp
            - name: updatedBy
              data_type: string
            - name: statusId
              data_type: integer
            - name: specVersion
              data_type: integer
            - name: isDeleted
              data_type: boolean
            - name: configSpecJson
              data_type: string
            - name: isMasterConfig
              data_type: boolean
            - name: repTeamId
              data_type: integer

        - name: HCP_Content_Affinity
          description: "HCP_Content_Affinity"
          external:
            location: "s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/content_affinity"
            row_format: "SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'"
            table_properties: ('storage.location.template'="s3://aktana-bdp-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/content_affinity")
            hive_compatible_partitions: true
            projection_partitions: true
          columns:
            - name: accountId
              data_type: int
            - name: productId
              data_type: int
            - name: recommended_content
              data_type: string
            - name: scores
              data_type: string





