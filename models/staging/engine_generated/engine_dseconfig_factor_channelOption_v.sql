{{ config(materialized='view') }}

WITH exploded AS (
    SELECT 
        -- configspecjson,
        -- json_extract(factor_item, '$.parameters') AS parameters,
        configid,
        rundate,
        cast(json_extract(factor_item, '$.factorUID') as varchar) AS factorUid,
        cast(json_extract(factor_item, '$.parameters.productIds') as varchar) productIds,
        cast(json_extract(factor_item, '$.parameters.actorTypeId') as varchar) actorTypeId,
        cast(json_extract(factor_item, '$.parameters.accountTypeIds') as varchar) accountTypeIds,
        CAST(json_parse(json_extract_scalar(factor_item, '$.parameters.channelOptions')) AS ARRAY<JSON>) AS channel_options
    FROM 
        {{ ref('engine_dseconfig_v') }}
    CROSS JOIN UNNEST(CAST(json_extract(configspecjson, '$.factorList') AS ARRAY<JSON>)) AS t(factor_item)
)

SELECT 
    configid,
    rundate,
    factorUid,
    productIds,
    actorTypeId,
    accountTypeIds,
    COALESCE(
        array_join(
            array_agg(DISTINCT json_extract_scalar(option_item, '$.actorTypeId')), 
            ','
        ), 
        'NULL'
    ) AS channelOptionActorTypeUIds
FROM 
    exploded
LEFT JOIN UNNEST(channel_options) AS t2(option_item) on TRUE
GROUP BY 
    configid, rundate, factorUid, productIds, actorTypeId, accountTypeIds