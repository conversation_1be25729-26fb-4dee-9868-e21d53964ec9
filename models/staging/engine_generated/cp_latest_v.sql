{{ config(materialized='view') }}

with cp_max_prob as (
    SELECT
        a.*,
        greatest(send_prob_cali, visit_prob_cali, virtual_visit_prob_cali) preferred_channel_prob
    FROM
        {{ ref('cp_latest_raw_v') }} a
)
select a.*,
    case when send_prob_cali = preferred_channel_prob then 'SEND_CHANNEL'
         when visit_prob_cali = preferred_channel_prob then 'VISIT_CHANNEL'
         when virtual_visit_prob_cali = preferred_channel_prob then 'VIRTUAL_VISIT_CHANNEL'
         else '' end preferred_channel,
    case when send_prob_cali = preferred_channel_prob then send_prob_cali_segment
         when visit_prob_cali = preferred_channel_prob then visit_prob_cali_segment
         when virtual_visit_prob_cali = preferred_channel_prob then virtual_visit_prob_cali_segment
         else '' end preferred_channel_segment
FROM
    cp_max_prob a

