{{ config(materialized='view') }}

with suggestion_factor_tag as (
  select
    runuid,
    factoruid,
    min(tagid) tagid,
    min(tagname) tagname,
    min(tagtype) tagtype
  from {{ ref('sparkdserunconfigfactortag_v') }}
  where tagtype = 'USECASE'
  group by runuid, factoruid
),
suggestion_factor as (
  select
    a.runuid,
    a.factoruid,
    min(a.factorname) factorname,
    min(a.factortype) factortype,
    min(c.strategyid) strategyid,
    min(c.strategyname) strategyname
  from {{ ref('sparkdserunconfigfactor_v') }} a
  inner join {{ ref('sparkdserun_v') }} b on a.runuid = b.runuid
  left join {{ ref('strategyfactor_map_v') }} c on b.seconfigid = c.seconfigid and a.factoruid = c.factoruid
  group by a.runuid, a.factoruid
),
exploded_reasons as (
  select
    dc.*,
    reason.crmFieldName as reason_crmFieldName,
    reason.reasonRank as reason_rank,
    reason.text as reason_text,
    reason.factorUID as reason_factorUID
  from {{ref('scored_candidates_with_cj_v')}} dc
  cross join unnest(dc.reasons) as t (reason)
)
select
  er.*,
  sf.factorname as reason_factorName,
  sf.factortype as reason_factorType,
  sf.strategyid  as reason_strategyId,
  sf.strategyname as reason_strategyName,
  sft.tagid as reason_tagId,
  sft.tagname as reason_tagName
from exploded_reasons er
left join suggestion_factor sf
on er.reason_factorUID = sf.factoruid
left join suggestion_factor_tag sft
on er.reason_factorUID = sft.factoruid;