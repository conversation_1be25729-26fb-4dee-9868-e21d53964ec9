{{ config(materialized='view') }}

select raa.*, r.repTypeUid as actortypeuid, actv.externalId as accounttypeuid
from {{ source('engine_generated', 'RepAccountAssignment') }} raa
left join {{ ref('engine_suggestible_reps_v') }} r
    on raa.repid = r.repid
    and raa.rundate = r.rundate
    and raa.configid = r.configid
left join {{ ref('account_dse_v') }} a
    on raa.accountid = a.accountid
left join {{ ref('accounttype_v') }} actv
    on a.accounttypeid = actv.accounttypeid