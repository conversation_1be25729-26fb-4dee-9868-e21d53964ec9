{{ config(materialized='view') }}

select a.*, 'Undefined Strategy' as strategyname, -- TODO: Need to add strategy to suggestion candidates
case when d.suggestionReferenceId is null then 0 else 1 end as isRecommended 
from {{ source('engine_generated', 'suggestion_candidates') }} a 
left join 
(select distinct b.suggestionReferenceId, c.suggestedDate, c.runUID
from {{ref('sparkdserunrepdatesuggestion_v')}} b
join {{ref('sparkdserunrepdate_v')}} c
on b.runRepDateId = c.runRepDateId
) d
on a.suggestionreference.suggestionreferenceid = d.suggestionReferenceId
and date_parse(a.suggesteddate, '%Y-%m-%d') = d.suggestedDate
and a.metadata.runUID = d.runUID



