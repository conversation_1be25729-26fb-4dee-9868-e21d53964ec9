version: 2

sources:
  - name: rawsales  
    description: “Archived data from Loaders”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: ext_sales_snsob_ad_core_wk 
          description: "raw weekly sales data for Atopic Dermatitis table"
          external:
            location: "s3://aktana-externalfiles-sanofius/{{ var('src_env', 'prod') }}/repository/sales/snsob_ad_core_wk/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20221001,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: CLIENT_NUMBER 
                 data_type: varchar(15)
               - name: REPORT_NUMBER 
                 data_type: varchar(15)
               - name: CHANNEL_INDICATOR 
                 data_type: varchar(15)
               - name: PRESC_ID 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_1 
                 data_type: varchar(15)
               - name: SPECIALTY_ABBREVIATION 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_8 
                 data_type: varchar(15)
               - name: DIAGNOSIS_GROUP 
                 data_type: varchar(100)
               - name: FLEXIBLE_FIELD_2 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_3 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_4 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_5 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_6 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_7 
                 data_type: varchar(15)
               - name: PROD_GRP 
                 data_type: varchar(50)
               - name: WEEK_ID 
                 data_type: varchar(50)
               - name: XPO_NRX 
                 data_type: double
               - name: XPO_TRX 
                 data_type: double
               - name: XPO_UNKNRX 
                 data_type: double
               - name: XPO_UNKTRX 
                 data_type: double
               - name: XPO_RRX 
                 data_type: double
               - name: XPO_UNKRRX 
                 data_type: double
               - name: XPO_NTS 
                 data_type: double
               - name: XPO_CN 
                 data_type: double
               - name: XPO_SW 
                 data_type: double
               - name: XPO_AO 
                 data_type: double
               - name: RSN 
                 data_type: double
               - name: RD 
                 data_type: double
               - name: XPO_CR 
                 data_type: double
               - name: RSR 
                 data_type: double
               - name: XPO_SWF 
                 data_type: double
               - name: RDF 
                 data_type: double
               - name: XPO_AT 
                 data_type: double
               - name: NBRX 
                 data_type: double
               - name: DRX 
                 data_type: double
               - name: CURR_CCT_ID 
                 data_type: varchar(10)
               - name: FRZ_CCT_ID 
                 data_type: varchar(10)
               - name: FRZ_CCT_ID_QTR 
                 data_type: varchar(10)

        - name: ext_sales_snsob_as_core_wk 
          description: "raw weekly sales data for Other Indication table"
          external:
            location: "s3://aktana-externalfiles-sanofius/{{ var('src_env', 'prod') }}/repository/sales/snsob_as_core_wk/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20221001,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: CLIENT_NUMBER 
                 data_type: varchar(15)
               - name: REPORT_NUMBER 
                 data_type: varchar(15)
               - name: CHANNEL_INDICATOR 
                 data_type: varchar(15)
               - name: PRESC_ID 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_1 
                 data_type: varchar(15)
               - name: SPECIALTY_ABBREVIATION 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_8 
                 data_type: varchar(15)
               - name: DIAGNOSIS_GROUP 
                 data_type: varchar(100)
               - name: FLEXIBLE_FIELD_2 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_3 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_4 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_5 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_6 
                 data_type: varchar(15)
               - name: FLEXIBLE_FIELD_7 
                 data_type: varchar(15)
               - name: PROD_GRP 
                 data_type: varchar(50)
               - name: WEEK_ID 
                 data_type: varchar(50)
               - name: XPO_NRX 
                 data_type: double
               - name: XPO_TRX 
                 data_type: double
               - name: XPO_UNKNRX 
                 data_type: double
               - name: XPO_UNKTRX 
                 data_type: double
               - name: XPO_RRX 
                 data_type: double
               - name: XPO_UNKRRX 
                 data_type: double
               - name: XPO_NTS 
                 data_type: double
               - name: XPO_CN 
                 data_type: double
               - name: XPO_SW 
                 data_type: double
               - name: XPO_AO 
                 data_type: double
               - name: RSN 
                 data_type: double
               - name: RD 
                 data_type: double
               - name: XPO_CR 
                 data_type: double
               - name: RSR 
                 data_type: double
               - name: XPO_SWF 
                 data_type: double
               - name: RDF 
                 data_type: double
               - name: XPO_AT 
                 data_type: double
               - name: NBRX 
                 data_type: double
               - name: DRX 
                 data_type: double
               - name: CURR_CCT_ID 
                 data_type: varchar(10)
               - name: FRZ_CCT_ID 
                 data_type: varchar(10)
               - name: FRZ_CCT_ID_QTR 
                 data_type: varchar(10)

        - name: ext_sales_akt_trxnrx_data
          description: "raw weekly sales data for sanofi genmed"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/AKT_TRXNRX_DATA/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: customerId 
                 data_type: varchar(10)
               - name: productName 
                 data_type: varchar(25)
               - name: prev5to13w_TRx_akt 
                 data_type: double
               - name: cur4w_TRx_akt 
                 data_type: double
               - name: prev4w_TRx 
                 data_type: double
               - name: cur13w_TRx_akt 
                 data_type: double
               - name: prev13w_TRx 
                 data_type: double
               - name: cur1w_TRx_akt 
                 data_type: double
               - name: prev2to104w_TRx_akt 
                 data_type: double
               - name: prev7to14w_TRx_akt 
                 data_type: double
               - name: cur6w_TRx_akt 
                 data_type: double

        - name: ext_sales_akt_nbrx_data
          description: "raw weekly sales data for sanofi genmed"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/AKT_NBRX_DATA/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: customerId 
                 data_type: varchar(10)
               - name: productName 
                 data_type: varchar(25)
               - name: cur4w_nbrx_akt 
                 data_type: double
               - name: cur13w_nbrx_akt 
                 data_type: double
               - name: cur1w_nbrx_akt 
                 data_type: double
               - name: prev2_104w_nbrx_akt 
                 data_type: double
               - name: prev5_13w_nbrx_akt 
                 data_type: double

        - name: ext_sales_pfi_akt_trx_m_data
          description: "raw monthly trx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1269_md_prod_trx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: trxmonthback1 
                 data_type: double
               - name: trxmonthback2
                 data_type: double
               - name: trxmonthback3
                 data_type: double
               - name: trxmonthback4
                 data_type: double
               - name: trxmonthback5
                 data_type: double
               - name: trxmonthback6
                 data_type: double
               - name: trxmonthback7
                 data_type: double
               - name: trxmonthback8
                 data_type: double
               - name: trxmonthback9
                 data_type: double
               - name: trxmonthback10
                 data_type: double
               - name: trxmonthback11
                 data_type: double
               - name: trxmonthback12
                 data_type: double
               - name: trxmonthback13
                 data_type: double
               - name: trxmonthback14
                 data_type: double
               - name: trxmonthback15
                 data_type: double
               - name: trxmonthback16
                 data_type: double
               - name: trxmonthback17
                 data_type: double
               - name: trxmonthback18
                 data_type: double
               - name: trxmonthback19
                 data_type: double
               - name: trxmonthback20
                 data_type: double
               - name: trxmonthback21
                 data_type: double
               - name: trxmonthback22
                 data_type: double
               - name: trxmonthback23
                 data_type: double
               - name: trxmonthback24
                 data_type: double
               - name: trxunitmonthback1 
                 data_type: double
               - name: trxunitmonthback2
                 data_type: double
               - name: trxunitmonthback3
                 data_type: double
               - name: trxunitmonthback4
                 data_type: double
               - name: trxunitmonthback5
                 data_type: double
               - name: trxunitmonthback6
                 data_type: double
               - name: trxunitmonthback7
                 data_type: double
               - name: trxunitmonthback8
                 data_type: double
               - name: trxunitmonthback9
                 data_type: double
               - name: trxunitmonthback10
                 data_type: double
               - name: trxunitmonthback11
                 data_type: double
               - name: trxunitmonthback12
                 data_type: double
               - name: trxunitmonthback13
                 data_type: double
               - name: trxunitmonthback14
                 data_type: double
               - name: trxunitmonthback15
                 data_type: double
               - name: trxunitmonthback16
                 data_type: double
               - name: trxunitmonthback17
                 data_type: double
               - name: trxunitmonthback18
                 data_type: double
               - name: trxunitmonthback19
                 data_type: double
               - name: trxunitmonthback20
                 data_type: double
               - name: trxunitmonthback21
                 data_type: double
               - name: trxunitmonthback22
                 data_type: double
               - name: trxunitmonthback23
                 data_type: double
               - name: trxunitmonthback24
                 data_type: double

        - name: ext_sales_pfi_akt_nrx_m_data
          description: "raw monthly nrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1269_md_prod_nrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: nrxmonthback1 
                 data_type: double
               - name: nrxmonthback2
                 data_type: double
               - name: nrxmonthback3
                 data_type: double
               - name: nrxmonthback4
                 data_type: double
               - name: nrxmonthback5
                 data_type: double
               - name: nrxmonthback6
                 data_type: double
               - name: nrxmonthback7
                 data_type: double
               - name: nrxmonthback8
                 data_type: double
               - name: nrxmonthback9
                 data_type: double
               - name: nrxmonthback10
                 data_type: double
               - name: nrxmonthback11
                 data_type: double
               - name: nrxmonthback12
                 data_type: double
               - name: nrxmonthback13
                 data_type: double
               - name: nrxmonthback14
                 data_type: double
               - name: nrxmonthback15
                 data_type: double
               - name: nrxmonthback16
                 data_type: double
               - name: nrxmonthback17
                 data_type: double
               - name: nrxmonthback18
                 data_type: double
               - name: nrxmonthback19
                 data_type: double
               - name: nrxmonthback20
                 data_type: double
               - name: nrxmonthback21
                 data_type: double
               - name: nrxmonthback22
                 data_type: double
               - name: nrxmonthback23
                 data_type: double
               - name: nrxmonthback24
                 data_type: double
               - name: nrxunitmonthback1 
                 data_type: double
               - name: nrxunitmonthback2
                 data_type: double
               - name: nrxunitmonthback3
                 data_type: double
               - name: nrxunitmonthback4
                 data_type: double
               - name: nrxunitmonthback5
                 data_type: double
               - name: nrxunitmonthback6
                 data_type: double
               - name: nrxunitmonthback7
                 data_type: double
               - name: nrxunitmonthback8
                 data_type: double
               - name: nrxunitmonthback9
                 data_type: double
               - name: nrxunitmonthback10
                 data_type: double
               - name: nrxunitmonthback11
                 data_type: double
               - name: nrxunitmonthback12
                 data_type: double
               - name: nrxunitmonthback13
                 data_type: double
               - name: nrxunitmonthback14
                 data_type: double
               - name: nrxunitmonthback15
                 data_type: double
               - name: nrxunitmonthback16
                 data_type: double
               - name: nrxunitmonthback17
                 data_type: double
               - name: nrxunitmonthback18
                 data_type: double
               - name: nrxunitmonthback19
                 data_type: double
               - name: nrxunitmonthback20
                 data_type: double
               - name: nrxunitmonthback21
                 data_type: double
               - name: nrxunitmonthback22
                 data_type: double
               - name: nrxunitmonthback23
                 data_type: double
               - name: nrxunitmonthback24
                 data_type: double

        - name: ext_sales_pfi_akt_trx_w_data
          description: "raw weekly trx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_trx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: trxweekback1 
                 data_type: double
               - name: trxweekback2
                 data_type: double
               - name: trxweekback3
                 data_type: double
               - name: trxweekback4
                 data_type: double
               - name: trxweekback5
                 data_type: double
               - name: trxweekback6
                 data_type: double
               - name: trxweekback7
                 data_type: double
               - name: trxweekback8
                 data_type: double
               - name: trxweekback9
                 data_type: double
               - name: trxweekback10
                 data_type: double
               - name: trxweekback11
                 data_type: double
               - name: trxweekback12
                 data_type: double
               - name: trxweekback13
                 data_type: double
               - name: trxweekback14
                 data_type: double
               - name: trxweekback15
                 data_type: double
               - name: trxweekback16
                 data_type: double
               - name: trxweekback17
                 data_type: double
               - name: trxweekback18
                 data_type: double
               - name: trxweekback19
                 data_type: double
               - name: trxweekback20
                 data_type: double
               - name: trxweekback21
                 data_type: double
               - name: trxweekback22
                 data_type: double
               - name: trxweekback23
                 data_type: double
               - name: trxweekback24               
                 data_type: double
               - name: trxweekback25               
                 data_type: double
               - name: trxweekback26               
                 data_type: double
               - name: trxunitweekback1 
                 data_type: double
               - name: trxunitweekback2
                 data_type: double
               - name: trxunitweekback3
                 data_type: double
               - name: trxunitweekback4
                 data_type: double
               - name: trxunitweekback5
                 data_type: double
               - name: trxunitweekback6
                 data_type: double
               - name: trxunitweekback7
                 data_type: double
               - name: trxunitweekback8
                 data_type: double
               - name: trxunitweekback9
                 data_type: double
               - name: trxunitweekback10
                 data_type: double
               - name: trxunitweekback11
                 data_type: double
               - name: trxunitweekback12
                 data_type: double
               - name: trxunitweekback13
                 data_type: double
               - name: trxunitweekback14
                 data_type: double
               - name: trxunitweekback15
                 data_type: double
               - name: trxunitweekback16
                 data_type: double
               - name: trxunitweekback17
                 data_type: double
               - name: trxunitweekback18
                 data_type: double
               - name: trxunitweekback19
                 data_type: double
               - name: trxunitweekback20
                 data_type: double
               - name: trxunitweekback21
                 data_type: double
               - name: trxunitweekback22
                 data_type: double
               - name: trxunitweekback23
                 data_type: double
               - name: trxunitweekback24
                 data_type: double
               - name: trxunitweekback25
                 data_type: double
               - name: trxunitweekback26
                 data_type: double

        - name: ext_sales_pfi_akt_nrx_w_data
          description: "raw weekly nrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_nrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: nrxweekback1 
                 data_type: double
               - name: nrxweekback2
                 data_type: double
               - name: nrxweekback3
                 data_type: double
               - name: nrxweekback4
                 data_type: double
               - name: nrxweekback5
                 data_type: double
               - name: nrxweekback6
                 data_type: double
               - name: nrxweekback7
                 data_type: double
               - name: nrxweekback8
                 data_type: double
               - name: nrxweekback9
                 data_type: double
               - name: nrxweekback10
                 data_type: double
               - name: nrxweekback11
                 data_type: double
               - name: nrxweekback12
                 data_type: double
               - name: nrxweekback13
                 data_type: double
               - name: nrxweekback14
                 data_type: double
               - name: nrxweekback15
                 data_type: double
               - name: nrxweekback16
                 data_type: double
               - name: nrxweekback17
                 data_type: double
               - name: nrxweekback18
                 data_type: double
               - name: nrxweekback19
                 data_type: double
               - name: nrxweekback20
                 data_type: double
               - name: nrxweekback21
                 data_type: double
               - name: nrxweekback22
                 data_type: double
               - name: nrxweekback23
                 data_type: double
               - name: nrxweekback24               
                 data_type: double
               - name: nrxweekback25               
                 data_type: double
               - name: nrxweekback26               
                 data_type: double
               - name: nrxunitweekback1 
                 data_type: double
               - name: nrxunitweekback2
                 data_type: double
               - name: nrxunitweekback3
                 data_type: double
               - name: nrxunitweekback4
                 data_type: double
               - name: nrxunitweekback5
                 data_type: double
               - name: nrxunitweekback6
                 data_type: double
               - name: nrxunitweekback7
                 data_type: double
               - name: nrxunitweekback8
                 data_type: double
               - name: nrxunitweekback9
                 data_type: double
               - name: nrxunitweekback10
                 data_type: double
               - name: nrxunitweekback11
                 data_type: double
               - name: nrxunitweekback12
                 data_type: double
               - name: nrxunitweekback13
                 data_type: double
               - name: nrxunitweekback14
                 data_type: double
               - name: nrxunitweekback15
                 data_type: double
               - name: nrxunitweekback16
                 data_type: double
               - name: nrxunitweekback17
                 data_type: double
               - name: nrxunitweekback18
                 data_type: double
               - name: nrxunitweekback19
                 data_type: double
               - name: nrxunitweekback20
                 data_type: double
               - name: nrxunitweekback21
                 data_type: double
               - name: nrxunitweekback22
                 data_type: double
               - name: nrxunitweekback23
                 data_type: double
               - name: nrxunitweekback24
                 data_type: double
               - name: nrxunitweekback25
                 data_type: double
               - name: nrxunitweekback26
                 data_type: double

        - name: ext_sales_pfi_akt_nbrx_w_data
          description: "raw weekly nbrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_nbrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: nbrxweekback1 
                 data_type: double
               - name: nbrxweekback2
                 data_type: double
               - name: nbrxweekback3
                 data_type: double
               - name: nbrxweekback4
                 data_type: double
               - name: nbrxweekback5
                 data_type: double
               - name: nbrxweekback6
                 data_type: double
               - name: nbrxweekback7
                 data_type: double
               - name: nbrxweekback8
                 data_type: double
               - name: nbrxweekback9
                 data_type: double
               - name: nbrxweekback10
                 data_type: double
               - name: nbrxweekback11
                 data_type: double
               - name: nbrxweekback12
                 data_type: double
               - name: nbrxweekback13
                 data_type: double
               - name: nbrxweekback14
                 data_type: double
               - name: nbrxweekback15
                 data_type: double
               - name: nbrxweekback16
                 data_type: double
               - name: nbrxweekback17
                 data_type: double
               - name: nbrxweekback18
                 data_type: double
               - name: nbrxweekback19
                 data_type: double
               - name: nbrxweekback20
                 data_type: double
               - name: nbrxweekback21
                 data_type: double
               - name: nbrxweekback22
                 data_type: double
               - name: nbrxweekback23
                 data_type: double
               - name: nbrxweekback24               
                 data_type: double
               - name: nbrxweekback25               
                 data_type: double
               - name: nbrxweekback26               
                 data_type: double


        - name: ext_sales_nvsus_akt_trxnbrx_w_data
          description: "raw weekly trx sales data for novartisus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/NEW_GW5620H_C357R01/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: client_number 
                 data_type: integer
               - name: report_number 
                 data_type: integer
               - name: ims_id 
                 data_type: varchar(10)
               - name: sra_2_specialty 
                 data_type: varchar(1)
               - name: sra_3_zip_mo 
                 data_type: varchar(1)
               - name: sra_4_payment_type 
                 data_type: varchar(1)
               - name: sales_category 
                 data_type: integer
               - name: rx_type 
                 data_type: varchar(3)
               - name: product_group_number 
                 data_type: integer
               - name: filler 
                 data_type: varchar(1)
               - name: me_number 
                 data_type: varchar(15)
               - name: prescriber_last_name 
                 data_type: varchar(50)
               - name: prescriber_first_name 
                 data_type: varchar(50)
               - name: prescriber_middle_initial 
                 data_type: varchar(3)
               - name: prescriber_street_address 
                 data_type: varchar(100)
               - name: prescriber_city 
                 data_type: varchar(25)
               - name: prescriber_state
                 data_type: varchar(5)
               - name: prescriber_zipcode
                 data_type: varchar(10)
               - name: supplemental_data
                 data_type: varchar(1)
               - name: pay_plan
                 data_type: varchar(1)
               - name: ndc_desc
                 data_type: varchar(1)
               - name: data_date
                 data_type: varchar(10)
               - name: bucket_count
                 data_type: integer
               - name: trx_1 
                 data_type: double
               - name: trx_2
                 data_type: double
               - name: trx_3
                 data_type: double
               - name: trx_4
                 data_type: double
               - name: trx_5
                 data_type: double
               - name: trx_6
                 data_type: double
               - name: trx_7
                 data_type: double
               - name: trx_8
                 data_type: double
               - name: trx_9
                 data_type: double
               - name: trx_10
                 data_type: double
               - name: trx_11
                 data_type: double
               - name: trx_12
                 data_type: double
               - name: trx_13
                 data_type: double
               - name: trx_14
                 data_type: double
               - name: trx_15
                 data_type: double
               - name: trx_16
                 data_type: double
               - name: trx_17
                 data_type: double
               - name: trx_18
                 data_type: double
               - name: trx_19
                 data_type: double
               - name: trx_20
                 data_type: double
               - name: trx_21
                 data_type: double
               - name: trx_22
                 data_type: double
               - name: trx_23
                 data_type: double
               - name: trx_24               
                 data_type: double
               - name: trx_25               
                 data_type: double
               - name: trx_26               
                 data_type: double
               - name: trx_27               
                 data_type: double
               - name: nbrx_1 
                 data_type: double
               - name: nbrx_2
                 data_type: double
               - name: nbrx_3
                 data_type: double
               - name: nbrx_4
                 data_type: double
               - name: nbrx_5
                 data_type: double
               - name: nbrx_6
                 data_type: double
               - name: nbrx_7
                 data_type: double
               - name: nbrx_8
                 data_type: double
               - name: nbrx_9
                 data_type: double
               - name: nbrx_10
                 data_type: double
               - name: nbrx_11
                 data_type: double
               - name: nbrx_12
                 data_type: double
               - name: nbrx_13
                 data_type: double
               - name: nbrx_14
                 data_type: double
               - name: nbrx_15
                 data_type: double
               - name: nbrx_16
                 data_type: double
               - name: nbrx_17
                 data_type: double
               - name: nbrx_18
                 data_type: double
               - name: nbrx_19
                 data_type: double
               - name: nbrx_20
                 data_type: double
               - name: nbrx_21
                 data_type: double
               - name: nbrx_22
                 data_type: double
               - name: nbrx_23
                 data_type: double
               - name: nbrx_24               
                 data_type: double
               - name: nbrx_25               
                 data_type: double
               - name: nbrx_26               
                 data_type: double
               - name: nbrx_27               
                 data_type: double
               - name: source_of_business
                 data_type: varchar(30)
               - name: prior_product_group
                 data_type: varchar(100)
               - name: diagnosis_group
                 data_type: varchar(30)
               - name: prior_exposure_arb
                 data_type: varchar(1)
               - name: prior_exposure_ace
                 data_type: varchar(1)
               - name: prior_exposure_bb
                 data_type: varchar(1)
               - name: prior_exposure_lcz
                 data_type: varchar(1)
               - name: prior_exposure_corlanor
                 data_type: varchar(1)
               - name: prior_exposure_other
                 data_type: varchar(1)

        - name: ext_sales_adc_nbrx_data
          description: "raw weekly nbrx sales data for abbott"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/ADC_NBRX_DATA/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20240101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: npi 
                 data_type: varchar(20)
               - name: external_id
                 data_type: varchar(20)
               - name: hcp_name
                 data_type: varchar(100)
               - name: channel
                 data_type: varchar(20)
               - name: manufacturer
                 data_type: varchar(100)
               - name: month_end_date
                 data_type: varchar(20)
               - name: week_end_date
                 data_type: varchar(20)
               - name: week_rank
                 data_type: integer
               - name: product
                 data_type: varchar(100)
               - name: product_ndc
                 data_type: varchar(20)
               - name: territory_id
                 data_type: varchar(20)
               - name: patient_segment
                 data_type: varchar(20)
               - name: actual_nbrx
                 data_type: integer
               - name: projected_nbrx
                 data_type: double

        - name: ext_sales_adc_trxnrx_data
          description: "raw weekly trx sales data for abbott"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/ADC_TRXNRX_DATA/"
            row_format: "DELIMITED FIELDS TERMINATED BY '|' ESCAPED BY '\\\\' LINES TERMINATED BY '\\n'"
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20240101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: npi 
                 data_type: varchar(20)
               - name: external_id
                 data_type: varchar(20)
               - name: hcp_name
                 data_type: varchar(100)
               - name: target_type
                 data_type: varchar(20)
               - name: ispdrp
                 data_type: integer
               - name: behavioural_segment
                 data_type: varchar(100)
               - name: manufacturer
                 data_type: varchar(20)
               - name: product
                 data_type: varchar(100)
               - name: product_ndc
                 data_type: varchar(20)
               - name: week_end_date
                 data_type: varchar(20)
               - name: month_end_date
                 data_type: varchar(20)
               - name: week_rank
                 data_type: integer
               - name: trx_count
                 data_type: integer
               - name: retail_trx
                 data_type: integer
               - name: retail_14_day_rx
                 data_type: double
               - name: dme_trx
                 data_type: integer

        - name: ext_sales_gne_sales_data
          description: "raw weekly nrx unit sales data for genentechus"
          external:
            location: "s3://aktana-externalfiles-genentechus/{{ var('src_env', 'prod') }}/repository/sales/SALESPROCESSED/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20240101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: customer_identifier
                 data_type: varchar(20)
               - name: period_end_date
                 data_type: varchar(20)
               - name: week_end_date
                 data_type: varchar(20)
               - name: month_end_date
                 data_type: varchar(20)
               - name: data_point_value
                 data_type: double 
               - name: data_point_type
                 data_type: varchar(100)
               - name: data_point_unit
                 data_type: varchar(100)
               - name: data_point_time_period
                 data_type: varchar(100)
               - name: data_point_period
                 data_type: varchar(100)
               - name: market_long_name
                 data_type: varchar(255)
               - name: market_short_name
                 data_type: varchar(100)
               - name: market_identifier
                 data_type: varchar(255)
               - name: channel
                 data_type: varchar(255)
               - name: product_name
                 data_type: varchar(255)
               - name: product_identifier
                 data_type: varchar(100)
               - name: level
                 data_type: varchar(100)
               - name: level_description
                 data_type: varchar(255)
               - name: custom_attr1
                 data_type: varchar(255)
               - name: custom_attr2
                 data_type: varchar(255)
               - name: custom_attr3
                 data_type: varchar(255)
               - name: country_code
                 data_type: varchar(10)
               - name: data_source
                 data_type: varchar(100)
