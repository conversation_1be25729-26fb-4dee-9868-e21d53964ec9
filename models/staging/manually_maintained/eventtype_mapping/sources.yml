version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained EventType Mapping Data”
    database: awsdatacatalog
    schema: "impact_{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}"
    tables:
        - name: eventtype_category_mapping 
          description: "This table holds EventType Mappings"
          external:
              location: "s3://aktana-bdp-{{ var('customer', 'default') }}/{{ var('src_env', 'prod') }}/tempdata/event_mapping"
              row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
          columns:
              - name: eventypeparentcategory 
                data_type: varchar(100)
              - name: eventtypecategory 
                data_type: varchar(100)
              - name: eventtypesubcategory 
                data_type: varchar(100)
              - name: eventtypeuid 
                data_type: varchar(255)
              - name: isDeleted
                data_type: boolean
              - name: isModified
                data_type: boolean
 
 