version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained Data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: tempdata_akt_apptracking
          description: "table to get enhanced insight views tracking"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/AKT_AppTracking/"
            row_format: "DELIMITED FIELDS TERMINATED BY ',' ESCAPED BY '\\\\' "
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: "connect_topic"
                 data_type: string
               - name: "connect_partition" 
                 data_type: bigint
               - name: "connect_offset"
                 data_type: bigint
               - name: datetime
                 data_type: timestamp
               - name: ishighlighted
                 data_type: string
               - name: sessionid
                 data_type: string
               - name: type
                 data_type: string
               - name: online
                 data_type: string
               - name: products
                 data_type: string
               - name: productuids
                 data_type: string
               - name: duration
                 data_type: bigint
               - name: suggestionreferenceid
                 data_type: string
               - name: customeruid
                 data_type: string
               - name: application
                 data_type: string
               - name: viewtype
                 data_type: string
               - name: action
                 data_type: string
               - name: category
                 data_type: string
               - name: sortcriteria
                 data_type: string
               - name: accountuid
                 data_type: string
               - name: repuid
                 data_type: string
               - name: suggestionuid
                 data_type: string
               - name: messages
                 data_type: string
               - name: messageids
                 data_type: string
               - name: additionalinfo
                 data_type: string

