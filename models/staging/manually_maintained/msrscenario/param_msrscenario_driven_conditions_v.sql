{{ config(materialized='view') }}

-- `candidate_matching_dimensions` string COMMENT 'from deserializer',
-- `driven_category_ids` string COMMENT 'from deserializer',
-- `partially_driven_category_ids` string COMMENT 'from deserializer',
-- `aligned_category_ids` string COMMENT 'from deserializer',
-- `interaction_level_of_detail` 

-- `candidate_matching_dimensions` default 'ACCOUNT,REP',
-- `driven_category_ids` default '101,102',
-- `partially_driven_category_ids` default '202,203,301',
-- `aligned_category_ids`  default '401,402,403,404',
-- `interaction_level_of_detail`  default 'VISIT',

select uid as msrscenariouid, name msrscenarioname, description msrscenariodescription, 
',' || coalesce(driven_category_ids,'101,102,202,203,301') || ',' as driven_category_ids,
',' || coalesce(partially_driven_category_ids,'') || ',' as partially_driven_category_ids,
',' || coalesce(aligned_category_ids,'401,402,403,404') || ',' as aligned_category_ids,
coalesce(interaction_level_of_detail,'Interaction-factor level') as interaction_level_of_detail,
case when coalesce(interaction_level_of_detail,'Interaction-factor level') = 'Interaction-factor-product level' then 'factor-product level'
else 'factor level' end as evaluation_level_of_detail,
case when position('PRODUCT' IN upper(coalesce(candidate_matching_dimensions,'ACCOUNT,REP'))) > 0 then true else false end as use_product_in_candidate_matching,
case when position('CHANNEL' IN upper(coalesce(candidate_matching_dimensions,'ACCOUNT,REP'))) > 0 then true else false end as use_channel_in_candidate_matching
from  {{ source('manually_maintained','param_msrscenario') }}
;
