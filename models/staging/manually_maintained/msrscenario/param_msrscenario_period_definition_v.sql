{{ config(materialized='view') }}

-- computes the dates and quarters to use for each scenario and period
-- if rolling_offset_in_months is null, use explicitly specified dt_end
-- if rolling_duration_in_months is null, use explicitly specified dt_begin
-- otherwise, compute dt_begin and dt_end as follows:
--   first compute the dates using:
--        end date as lastday_of_current_month + rolling_offset_in_months 
--        begin date as firstday_of_current_month + rolling_offset_in_months - rolling_duration_in_months
--   then, if dt_begin and dt_end are specified, limit the values to the maxinum (raw_begin_dt and computed_begin_dt) and minimum (raw_end_dt and computed_end_dt)
with current_dates as (
     SELECT
          current_date as currentdate,
          (
               date_trunc('month', current_date) + interval '1' month - interval '1' day
          ) as lastday_of_month,
          date_trunc('month', current_date) as firstday_of_month
),
computed_dates as (
     SELECT
          uid as msrscenariouid,
          name msrscenarioname,
          description msrscenariodescription,
          prd.uid period_type,
          prd.name period_name,
          current_date,
          lastday_of_month,
          firstday_of_month,
          prd.rolling_offset_in_months,
          prd.rolling_duration_in_months,
          date_parse(prd.dt_begin, '%Y-%m-%d') as raw_begin_dt,
          date_parse(prd.dt_end, '%Y-%m-%d') raw_end_dt,
          prd.yearquarter_begin raw_begin_quarter,
          prd.yearquarter_end raw_end_quarter,
          (
               date_add(
                    'month',
                    coalesce(prd.rolling_offset_in_months, 0),
                    firstday_of_month
               ) + interval '1' month - interval '1' day
          ) as computed_end_dt,
          (
               date_add(
                    'month',
                    coalesce(prd.rolling_offset_in_months, 0) - coalesce(prd.rolling_duration_in_months, 24),
                    firstday_of_month
               )
          ) as computed_begin_dt
     from
          current_dates c,
          {{ source('manually_maintained', 'param_msrscenario') }} s
          cross join unnest (period_definitions) t(prd)
),
merged_dates as (
     select
          msrscenariouid,
          msrscenarioname,
          msrscenariodescription,
          period_type,
          period_name,
          computed_begin_dt,
          computed_end_dt,
          raw_begin_dt,
          raw_end_dt,
          rolling_offset_in_months,
          rolling_duration_in_months,
          case
               when rolling_duration_in_months is null then raw_begin_dt
               else case
                    when raw_begin_dt is null then computed_begin_dt
                    else greatest(raw_begin_dt, computed_begin_dt)
               end
          end as dt_begin,
          case
               when rolling_offset_in_months is null then raw_end_dt
               else case
                    when raw_end_dt is null then computed_end_dt
                    else least(raw_end_dt, computed_end_dt)
               end
          end as dt_end,
          case
               when rolling_duration_in_months is null then raw_begin_quarter
               else case
                    when raw_begin_quarter is null 
                    then cast( year(computed_begin_dt) as varchar) || '-Q' || cast(quarter(computed_begin_dt) as varchar)
                    else cast(
                         year(greatest(raw_begin_dt, computed_begin_dt)) as varchar
                    ) || '-Q' || cast(
                         quarter(greatest(raw_begin_dt, computed_begin_dt)) as varchar
                    ) end
          end as yearquarter_begin,
          case
               when rolling_offset_in_months is null then raw_end_quarter
               else case
                    when raw_end_quarter is null 
                    then cast(year(computed_end_dt) as varchar) || '-Q' || cast(quarter(computed_end_dt) as varchar)
                    else cast(
                         year(least(raw_end_dt, computed_end_dt)) as varchar
                    ) || '-Q' || cast(
                         quarter(least(raw_end_dt, computed_end_dt)) as varchar
                    ) end
          end as yearquarter_end
     from
          computed_dates
)
select
     msrscenariouid,
     msrscenarioname,
     msrscenariodescription,
     period_type,
     period_name,
     dt_begin,
     dt_end,
     yearquarter_begin,
     yearquarter_end,
     cardinality(
          filter(
               sequence(dt_begin, dt_end, interval '1' day),
               d -> day_of_week(d) not in (6, 7)
          )
     ) business_days,
     computed_begin_dt,
     computed_end_dt,
     raw_begin_dt,
     raw_end_dt,
     rolling_offset_in_months,
     rolling_duration_in_months
from
     merged_dates;