{{ config(materialized='view') }}

-- computes asof_date and begin_date to use for each scenario
-- if repaccountassignment_asof_offset_in_months is null, use explicitly specified repaccountassignment_asofdate
-- if repaccountassignment_begin_duration_in_months is null, use explicitly specified repaccountassignment_begindate
-- otherwise, compute asof_date and begin_dt as follows:
--   first compute the dates using:
--        asof date as lastday_of_current_month + rolling_offset_in_months 
--        begin date as firstday_of_current_month + rolling_offset_in_months - rolling_duration_in_months
--   then, if dt_begin and dt_end are specified, limit the values to the maxinum (raw_begin_dt and computed_begin_dt) and minimum (raw_asof_dt and computed_asof_dt)
with current_dates as (
     SELECT
          current_date as currentdate,
          (
               date_trunc('month', current_date) + interval '1' month - interval '1' day
          ) as lastday_of_month,
          date_trunc('month', current_date) as firstday_of_month
),
computed_dates as (
     SELECT
          uid as msrscenariouid,
          name msrscenarioname,
          description msrscenariodescription,
          current_date,
          lastday_of_month,
          firstday_of_month,
          repaccountassignment_begin_duration_in_months,
          repaccountassignment_asof_offset_in_months,
          date_parse(repaccountassignment_asofdate, '%Y-%m-%d') as raw_asof_dt,
          date_parse(repaccountassignment_begindate, '%Y-%m-%d') raw_begin_dt,
          (
               date_add(
                    'month',
                    coalesce(repaccountassignment_asof_offset_in_months, 0),
                    firstday_of_month
               ) + interval '1' month - interval '1' day
          ) as computed_asof_dt,
          (
               date_add(
                    'month',
                    coalesce(repaccountassignment_asof_offset_in_months, 0) - coalesce(repaccountassignment_begin_duration_in_months, 24),
                    firstday_of_month
               )
          ) as computed_begin_dt
     from
          current_dates c,
          {{ source('manually_maintained', 'param_msrscenario') }} s
),
merged_dates as (
     select
          msrscenariouid,
          msrscenarioname,
          msrscenariodescription,
          computed_begin_dt,
          computed_asof_dt,
          raw_begin_dt,
          raw_asof_dt,
          repaccountassignment_asof_offset_in_months,
          repaccountassignment_begin_duration_in_months,
          case
               when repaccountassignment_begin_duration_in_months is null then raw_begin_dt
               else case
                    when raw_begin_dt is null then computed_begin_dt
                    else greatest(raw_begin_dt, computed_begin_dt)
               end
          end as repaccountassignment_begindate,
          case
               when repaccountassignment_asof_offset_in_months is null then raw_asof_dt
               else case
                    when raw_asof_dt is null then computed_asof_dt
                    else least(raw_asof_dt, computed_asof_dt)
               end
          end as repaccountassignment_asofdate
     from
          computed_dates
)
select msrscenariouid, msrscenarioname, msrscenariodescription, 
date_trunc('day',repaccountassignment_asofdate) as as_of_date, 
year(repaccountassignment_asofdate) as as_of_year, 
month(repaccountassignment_asofdate) as as_of_month, 
day(repaccountassignment_asofdate) as as_of_day,
date_trunc('day',repaccountassignment_begindate) as begin_date, 
year(repaccountassignment_begindate) as begin_year, 
month(repaccountassignment_begindate) as begin_month, 
day(repaccountassignment_begindate) as begin_day,
computed_begin_dt,
computed_asof_dt,
raw_begin_dt,
raw_asof_dt,
repaccountassignment_asof_offset_in_months,
repaccountassignment_begin_duration_in_months
from  merged_dates;

