{{ config(materialized='view') }}

select coalesce(call_type, 'cast(null as varchar)') as call_type, 
coalesce(call_channel_raw, 'cast(null as varchar)') as call_channel_raw,
coalesce(call_channel_category, 'cast(null as varchar)') as call_channel_category,
coalesce(call_channel_dse, 'cast(null as varchar)') as call_channel_dse
from  {{ source('manually_maintained','param_msrscenario') }}
where uid = 'default';

