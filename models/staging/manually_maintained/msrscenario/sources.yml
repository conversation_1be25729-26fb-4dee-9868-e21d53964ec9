version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained Data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: param_msrscenario 
          description: "json that holds measurement scenario definition"
          external:
              location: "s3://aktana-bdp-{{ var('customer', 'default') }}/{{ var('src_env', 'prod') }}/tempdata/MSRScenario"
              row_format: "SERDE 'org.openx.data.jsonserde.JsonSerDe'"
          columns:
              - name: uid
                data_type: string
              - name: name
                data_type: string
              - name: type
                data_type: string
              - name: description
                data_type: string
              - name: period_definitions
                data_type: array<struct<uid:string,name:string,yearquarter_begin:string,yearquarter_end:string,dt_begin:string,dt_end:string,rolling_duration_in_months:int,rolling_offset_in_months:int>> 
              - name: repaccountassignment_begindate
                data_type: string
              - name: repaccountassignment_asofdate
                data_type: string
              - name: repaccountassignment_begin_duration_in_months
                data_type: int
              - name: repaccountassignment_asof_offset_in_months
                data_type: int
              - name: interaction_events
                data_type: array<string> 
              - name: usecase_level_interactions
                data_type: array<string> 
              - name: topic_level_interactions
                data_type: array<string> 
              - name: conversion_events
                data_type: array<string> 
              - name: analyze_driven_events
                data_type: boolean
              - name: analyze_aligned_events
                data_type: boolean
              - name: factorusecasename_map_override
                data_type: array<struct<factor_name:string,usecase_name:string>>
              - name: remove_same_state_transitions
                data_type: boolean
              - name: pilot_rep_predicate
                data_type: string
              - name: accountproduct_segment_rpt
                data_type: string
              - name: accountproduct_tier_rpt
                data_type: string
              - name: accountproduct_dim1_rpt
                data_type: string
              - name: accountproduct_dim1_rpt_label
                data_type: string
              - name: accountproduct_dim2_rpt
                data_type: string
              - name: accountproduct_dim2_rpt_label
                data_type: string
              - name: account_dim1_rpt
                data_type: string
              - name: account_dim1_rpt_label
                data_type: string
              - name: account_dim2_rpt
                data_type: string
              - name: account_dim2_rpt_label
                data_type: string
              - name: repaccount_istarget_rpt
                data_type: string
              - name: sales_filedate
                data_type: string
              - name: sales_filedate_offset_weeks
                data_type: integer
              - name: sales_interaction_offset_months
                data_type: integer
              - name: golive_date
                data_type: string
              - name: candidate_matching_dimensions
                data_type: string
              - name: candidate_matching_pre_days
                data_type: integer
              - name: candidate_matching_post_days
                data_type: integer
              - name: insight_matching_pre_days
                data_type: integer
              - name: insight_matching_post_days
                data_type: integer
              - name: driven_category_ids
                data_type: string
              - name: partially_driven_category_ids
                data_type: string
              - name: aligned_category_ids
                data_type: string
              - name: interaction_level_of_detail
                data_type: string
              - name: call_type
                data_type: string
              - name: call_channel_raw
                data_type: string
              - name: call_channel_category
                data_type: string
              - name: call_channel_dse
                data_type: string
              - name: ctag_docs_per_topic
                data_type: integer
              - name: cri_offset_months
                data_type: integer
              - name: cri_look_back_months
                data_type: integer
              - name: ctag_docs_recent_usage_count
                data_type: integer
