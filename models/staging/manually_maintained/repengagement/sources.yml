version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained Data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: tempdata_rpt_rep_engagement_segmentation
          description: "Rep Engagement Segmentation"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/RPT_Rep_Engagement_Segmentation/"
            row_format: "DELIMITED FIELDS TERMINATED BY ',' ESCAPED BY '\\\\' "
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: segment_id
                 data_type: varchar(255)
               - name: repid
                 data_type: int
               - name: repuid
                 data_type: varchar(20)
               - name: repname
                 data_type: varchar(50)
               - name: configname
                 data_type: varchar(60)
               - name: periodvalue
                 data_type: date
               - name: engagementrate
                 data_type: double
               - name: engagementsegment
                 data_type: varchar(60)
               - name: createdat
                 data_type: timestamp
               - name: updatedat
                 data_type: timestamp
