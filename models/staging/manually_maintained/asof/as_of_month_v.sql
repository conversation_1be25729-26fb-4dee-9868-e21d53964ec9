{{ config(materialized='view') }}

with month_days as (
select year, month, quarter, yearquarter,
      date_parse(cast(year * 10000 + month * 100 + 1 as varchar(255)), '%Y%m%d') month_begin,
      date_parse(cast(year * 10000 + month * 100 + 1 as varchar(255)), '%Y%m%d') + interval '1' month - interval '1' day month_end
from {{ ref('as_of_month') }}
)
select year, month, quarter, yearquarter, month_begin, month_end,
cardinality(filter(
               sequence(month_begin, month_end, interval '1' day),
                d -> day_of_week(d) not in (6,7)
           )) business_days
from month_days;

