{{ config(materialized='view') }}

with quarter_days as (
select year, yearquarter, quarter, 
      date_parse(cast(year * 10000 + min(month) * 100 + 1 as varchar(255)), '%Y%m%d') quarter_begin,
      date_parse(cast(year * 10000 + max(month) * 100 + 1 as varchar(255)), '%Y%m%d') + interval '1' month - interval '1' day quarter_end
from {{ ref('as_of_month') }}
group by year, yearquarter, quarter
)
select year,  quarter, yearquarter, quarter_begin, quarter_end,
cardinality(filter(
               sequence(quarter_begin, quarter_end, interval '1' day),
                d -> day_of_week(d) not in (6,7)
           )) business_days
from quarter_days;
