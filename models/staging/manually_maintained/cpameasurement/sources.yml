version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained Data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: tempdata_rpt_stage_customer_impact_agg
          description: "Customer Impact Measurement"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/RPT_Stage_Customer_Impact_Agg/"
            row_format: "DELIMITED FIELDS TERMINATED BY ',' ESCAPED BY '\\\\' "
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: agg_id
                 data_type: varchar(255)
               - name: metrictype
                 data_type: varchar(20)
               - name: metricsubtype
                 data_type: varchar(20)
               - name: targetlevel
                 data_type: varchar(30)
               - name: externalid
                 data_type: varchar(255)
               - name: targetsperiodid
                 data_type: int
               - name: accountid
                 data_type: int
               - name: accountuid
                 data_type: varchar(40)
               - name: repid
                 data_type: int
               - name: repuid
                 data_type: varchar(20)
               - name: repteamid
                 data_type: int
               - name: repteamname
                 data_type: varchar(30)
               - name: productid
                 data_type: int
               - name: productuid
                 data_type: varchar(20)
               - name: productname
                 data_type: varchar(255)
               - name: repactiontypename
                 data_type: varchar(60)
               - name: periodvalue
                 data_type: date
               - name: aggregationtype
                 data_type: varchar(7)
               - name: metricvalue
                 data_type: int
               - name: proratedmetricvalue
                 data_type: double
               - name: createdat
                 data_type: timestamp
               - name: updatedat
                 data_type: timestamp
