{{ config(materialized='view') }}


with cust_impact_agg_target as (
    select * from {{ source('manually_maintained', 'tempdata_rpt_stage_customer_impact_agg') }} where metricsubtype = 'Targets'
)
select a.* from cust_impact_agg_target a
join {{ ref('rep_details_monthly') }} b on a.repuid = b.externalid and a.repteamname = b.repteamname
and YEAR(a.periodvalue) = b.year and MONTH(a.periodvalue) = b.month;

