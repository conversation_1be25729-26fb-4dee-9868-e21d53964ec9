version: 2

sources:
  - name: manually_maintained
    description: “Manually Maintained Data”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: tempdata_akt_accounts
          description: "table to do id mapping for account"
        - name: tempdata_akt_productcatalog
          description: "table to do id mapping for product"
        - name: tempdata_akt_productmap
          description: "table to map child product ids to parent product ids"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/AKT_ProductMap/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: parent_product 
                 data_type: varchar(50)
               - name: child_product 
                 data_type: varchar(50)
               - name: diagnosis_group 
                 data_type: varchar(50)

        - name: tempdata_akt_messagetomessagetopicmap
          description: "Message To MessageTopic Map"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/MessageToMessageTopicMap/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: messageId
                 data_type: bigint
               - name: externalId
                 data_type: varchar(40)
               - name: productId
                 data_type: bigint
               - name: messageChannelId
                 data_type: bigint
               - name: messageName
                 data_type: varchar(255)
               - name: messageDescription
                 data_type: varchar(255)
               - name: configCountryCode
                 data_type: varchar(255)
               - name: channelId
                 data_type: bigint
               - name: messageTopic
                 data_type: varchar(255)

        - name: tempdata_akt_marketbasket_product
          description: "table to product mapping"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/AKT_MarketBasket_Product/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: basketid 
                 data_type: varchar(50)
               - name: productname 
                 data_type: varchar(50)
               - name: brandname 
                 data_type: varchar(50)
               - name: marketname 
                 data_type: varchar(50)

        - name: tempdata_analytics_product_group
          description: "table analytics product grouping"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/Analytics_Product_Group_vod__c/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: id 
                 data_type: varchar(50)
               - name: ownerid 
                 data_type: varchar(50)
               - name: isdeleted 
                 data_type: bigint
               - name: name 
                 data_type: string
               - name: createddate 
                 data_type: timestamp
               - name: createdbyid 
                 data_type: string
               - name: lastmodifieddate 
                 data_type: timestamp
               - name: lastmodifiedbyid 
                 data_type: string
               - name: systemmodstamp 
                 data_type: timestamp
               - name: mayedit 
                 data_type: bigint
               - name: islocked 
                 data_type: bigint
               - name: lastvieweddate 
                 data_type: varchar(50)
               - name: lastreferenceddate 
                 data_type: varchar(50)
               - name: connectionreceivedid 
                 data_type: varchar(50)
               - name: connectionsentid 
                 data_type: varchar(50)
               - name: display_order_vod__c 
                 data_type: bigint
               - name: parent_product_group_vod__c 
                 data_type: varchar(100)
               - name: company_product_group_vod__c 
                 data_type: bigint
               - name: external_vod_id__c 
                 data_type: varchar(100)
               - name: display_name_vod__c 
                 data_type: varchar(100)
               - name: display_in_reports_vod__c 
                 data_type: bigint
               - name: market_vod__c 
                 data_type: varchar(100)
               - name: do_not_display_in_formulary_for_rep_vod__c 
                 data_type: bigint
               - name: backupcreateddate 
                 data_type: varchar(50)
               - name: backupmodifieddate 
                 data_type: varchar(50)

        - name: tempdata_akt_account_segment_mapping
          description: "table to do segment mapping for account"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/AKT_Account_Segment_Mapping/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: accountId
                 data_type: bigint
               - name: accountUID 
                 data_type: varchar(40)
               - name: productId
                 data_type: bigint
               - name: productUID
                 data_type: varchar(20)
               - name: hcpSegmentIndex
                 data_type: INT
               - name: hcpSegmentName 
                 data_type: varchar(20)
        
        - name: tempdata_akt_rep_team
          description: "table to have repTeam info"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/Rep_Team/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: repTeamId
                 data_type: INT
               - name: repTeamName 
                 data_type: varchar(40)
               - name: seConfigId
                 data_type: INT

        - name: tempdata_eventtype
          description: "eventtype mapping"
          external:
            location: "s3://aktana-bdp{{var('region', 'default')}}-glue/dbt/tempdata/{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/EventType/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'"
            table_properties: ('skip.header.line.count'='0')
          columns:
               - name: eventtypeid
                 data_type: bigint
               - name: eventtypename 
                 data_type: varchar(255)
               - name: externalid
                 data_type: varchar(255)
               - name: eventcategory
                 data_type: varchar(255)
               - name: productidrule
                 data_type: double
               - name: messagetopicrule 
                 data_type: double
               - name: messageidrule 
                 data_type: double
               - name: repidrule 
                 data_type: double
               - name: notes 
                 data_type: varchar(255)
               - name: createdat 
                 data_type: varchar(50)
               - name: updatedat 
                 data_type: varchar(50)






