{{ config(materialized='view') }}

select ms.messageid as messageid
     , ms.externalid as externalid
     , ms.productid as productid
     , ms.messagechannelid
     , ms.messagename
     , ms.messagedescription
     , ms.configcountrycode
     , ms.channelid 
     , t.topic_name as messagetopic
     , t.document_type
from {{ ref('ctag_out_document_topics_v') }} t
join (select Vault_Doc_Id_vod__c as cms_messageuid, 
             Id as messageuid
      from {{ ref('clm_presentation_vod__c_v') }}
      union all
      select vault_document_id_vod__c as cms_messageuid, 
             Id as messageuid
      from {{ ref('approved_document_vod__c_v') }}) m
  on t.cms_messageuid = m.cms_messageuid
 join message_v ms on m.messageuid = ms.externalid
 where topic_rank <= {{ var('msg_topic_rank') }};

