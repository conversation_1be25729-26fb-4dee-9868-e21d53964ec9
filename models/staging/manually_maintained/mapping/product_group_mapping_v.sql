{{ config(materialized='view') }}

select 
a.parent_product parentproductuid,
pp.productid parentproductid,
pp.productname parentproductname,
a.child_product productuid,
cp.productid productid,
a.diagnosis_group producttype

from {{ source('manually_maintained', 'tempdata_akt_productmap') }} a
join {{ ref('product_v') }} pp
    on pp.externalId = a.parent_product
join {{ ref('product_v') }} cp
    on cp.externalId = a.child_product


