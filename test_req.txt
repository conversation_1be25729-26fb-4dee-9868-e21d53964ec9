absl-py==1.4.0
aenum==3.1.12
agate==1.7.0
agate-sql==0.5.9
aiobotocore==2.5.0
aiohttp==3.8.1
aioitertools==0.11.0
aiosignal==1.3.1
alabaster==0.7.13
anyio==3.6.2
appnope==0.1.3
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
asn1crypto==1.5.1
astor==0.8.1
asttokens==2.2.1
astunparse==1.6.3
async-timeout==4.0.2
attrs==23.1.0
auth0-python==4.4.0
awswrangler==2.20.1
Babel==2.12.1
backcall==0.2.0
backoff==1.11.1
backports.functools-lru-cache==1.6.6
bcrypt==4.0.1
beautifulsoup4==4.12.2
betterproto==1.2.5
bleach==6.0.0
boto3==1.26.76
boto3-stubs==1.28.42
botocore==1.29.76
botocore-stubs==1.31.42
cached-property==1.5.2
cachetools==5.3.0
certifi==2022.12.7
cffi==1.15.1
charset-normalizer==2.0.12
click==8.1.3
cloudpickle==2.2.1
colorama==0.4.6
comm==0.1.3
contourpy==1.0.7
convertdate==2.4.0
cryptography==40.0.1
cycler==0.11.0
Cython==0.29.36
datadog-api-client==2.12.0
dbt-athena-community==1.5.2
dbt-core==1.5.1
dbt-extractor==0.4.1
dbt-fal==1.5.9
dbt-snowflake==1.5.3
dbt-spark==1.5.2
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
delta-spark==2.2.0
Deprecated==1.2.14
deprecation==2.1.0
dill==0.3.7
distlib==0.3.7
docutils==0.19
et-xmlfile==1.1.0
executing==1.2.0
fal==0.10.8
fastjsonschema==2.16.3
filelock==3.12.0
findspark==1.4.2
flatbuffers==23.3.3
fonttools==4.39.3
frozenlist==1.3.3
fsspec==2023.4.0
future==0.18.3
gast==0.4.0
gensim==4.3.2
geographiclib==1.52
geopy==2.2.0
google-api-core==2.11.1
google-api-python-client==2.97.0
google-auth==2.17.3
google-auth-httplib2==0.1.0
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
googleapis-common-protos==1.60.0
graphviz==0.20.1
greenlet==2.0.2
gremlinpython==3.6.2
grpc-interceptor==0.15.3
grpcio==1.54.0
grpclib==0.4.3
gspread==5.11.0
gspread-formatting==1.1.2
gspread-pandas==3.2.3
h11==0.14.0
h2==4.1.0
h5py==3.8.0
hdbscan==0.8.33
hijri-converter==2.3.1
holidays==0.11.1
hologram==0.0.15
hpack==4.0.0
httpcore==0.17.3
httplib2==0.22.0
httpx==0.24.1
hyperframe==6.0.1
idna==3.4
imagesize==1.4.1
importlib-metadata==6.6.0
importlib-resources==5.12.0
ipykernel==6.22.0
ipython==8.12.0
ipython-genutils==0.2.0
ipywidgets==8.0.6
isodate==0.6.1
isolate==0.12.2
isolate-proto==0.1.0
jaraco.classes==3.2.3
jedi==0.18.2
Jinja2==3.1.2
jmespath==1.0.1
joblib==1.3.2
jsonpath-ng==1.5.3
jsonschema==3.2.0
jupyter-events==0.6.3
jupyter_client==8.2.0
jupyter_core==5.3.0
jupyter_server==2.5.0
jupyter_server_terminals==0.4.4
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.7
keras==2.13.1
keyring==23.13.1
kiwisolver==1.4.4
korean-lunar-calendar==0.3.1
leather==0.3.4
libclang==16.0.0
lightgbm==3.3.5
llvmlite==0.39.1
Logbook==1.5.3
lxml==4.9.2
Markdown==3.4.3
markdown-it-py==3.0.0
MarkupSafe==2.0.1
mashumaro==3.6
matplotlib==3.7.1
matplotlib-inline==0.1.6
mdurl==0.1.2
minimal-snowplow-tracker==0.0.2
mistune==2.0.5
monotonic==1.6
more-itertools==9.1.0
msgpack==1.0.5
multidict==6.0.4
mypy-boto3-athena==1.28.36
mypy-boto3-glue==1.28.36
mypy-boto3-lakeformation==1.28.36
mypy-boto3-sts==1.28.37
mysql-connector-python==8.0.21
nbclassic==0.5.5
nbclient==0.7.4
nbconvert==7.3.1
nbformat==5.8.0
nest-asyncio==1.5.6
networkx==2.8.8
notebook==6.5.4
notebook_shim==0.2.3
numba==0.56.4
numpy==1.23.4
numpydoc==1.1.0
oauthlib==3.2.2
openai==0.28.0
openpyxl==3.0.10
opensearch-py==2.2.0
opentelemetry-api==1.20.0
opentelemetry-sdk==1.20.0
opentelemetry-semantic-conventions==0.41b0
opt-einsum==3.3.0
oscrypto==1.3.0
packaging==23.1
pandas==1.3.5
pandleau==0.4.1
pandocfilters==1.5.0
paramiko==3.1.0
parsedatetime==2.4
parso==0.8.3
pathspec==0.11.2
pexpect==4.8.0
pg8000==1.29.4
pickleshare==0.7.5
Pillow==9.5.0
platformdirs==2.6.2
ply==3.11
portalocker==2.7.0
posthog==1.4.9
progressbar2==4.2.0
prometheus-client==0.13.1
prompt-toolkit==3.0.38
protobuf==4.24.2
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
py4j==0.10.9.5
pyarrow==10.0.1
pyasn1==0.5.0
pyasn1-modules==0.3.0
pyathena==2.25.2
pycparser==2.21
pycryptodomex==3.17
pydantic==1.10.12
Pygments==2.15.1
PyJWT==2.6.0
PyMeeus==0.5.12
PyMySQL==1.0.3
PyNaCl==1.5.0
pynndescent==0.5.10
pyOpenSSL==23.1.0
pyparsing==3.0.9
pyrsistent==0.19.3
pyspark==3.3.1
python-dateutil==2.8.2
python-json-logger==2.0.7
python-slugify==8.0.1
python-utils==3.5.2
pytimeparse==1.1.8
pytz==2023.3
PyYAML==6.0
pyzmq==25.0.2
qgrid==1.3.1
redshift-connector==2.0.910
regex==2022.10.31
requests==2.31.0
requests-aws4auth==1.2.2
requests-oauthlib==1.3.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.5.2
rsa==4.9
s3fs==2023.4.0
s3transfer==0.6.0
scikit-learn==1.3.0
scipy==1.10.0
scramp==1.4.4
seaborn==0.11.0
Send2Trash==1.8.0
shap==0.41.0
six==1.16.0
sklearn-pandas==2.2.0
sklearn-pmml-model==1.0.1
sklearn2pmml==0.85.0
slicer==0.0.7
smart-open==6.3.0
sniffio==1.3.0
snowballstemmer==2.2.0
snowflake-connector-python==3.0.2
snowflake-sqlalchemy==1.4.7
soupsieve==2.4.1
Sphinx==6.2.1
sphinxcontrib-applehelp==1.0.4
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==2.0.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.5
SQLAlchemy==1.4.47
sqldf==0.4.2
sqlparams==5.1.0
sqlparse==0.4.3
sshtunnel==0.4.0
stack-data==0.6.2
statsd==3.3.0
stringcase==1.2.0
structlog==22.3.0
tableauserverclient==0.17.0
tabulate==0.9.0
tblib==2.0.0
tenacity==8.2.2
tensorboard==2.13.0
tensorboard-data-server==0.7.1
tensorboard-plugin-wit==1.8.1
tensorflow==2.13.0
tensorflow-estimator==2.13.0
tensorflow-hub==0.14.0
tensorflow-io-gcs-filesystem==0.32.0
tensorflow-text==2.13.0
termcolor==2.3.0
terminado==0.17.1
text-unidecode==1.3
threadpoolctl==3.1.0
tinycss2==1.2.1
top2vec==1.0.29
tornado==6.3.1
tqdm==4.65.0
traitlets==5.9.0
types-awscrt==0.19.1
types-python-dateutil==*********
types-s3transfer==0.6.2
typing_extensions==4.5.0
umap-learn==0.5.3
uritemplate==4.1.1
urllib3==1.26.15
virtualenv==20.21.1
wcwidth==0.2.6
webencodings==0.5.1
websocket-client==1.5.1
Werkzeug==2.1.0
widgetsnbextension==4.0.7
wordcloud==1.9.2
wrapt==1.15.0
xgboost==1.7.3
yarl==1.9.2
zipp==3.15.0
