# Paste the config below
environments:
  - name: ml
    type: venv
    requirements:
      - prophet

  - name: topicml
    type: venv
    requirements:
      - top2vec
      - top2vec[sentence_encoders]
      - gspread
      - gspread-pandas
      - gspread-formatting
      - google-auth
      - google-auth-oauthlib
      - google-auth-httplib2
      - google-api-python-client
      - beautifulsoup4
      - openai


