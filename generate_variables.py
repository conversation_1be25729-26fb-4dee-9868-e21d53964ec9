import os
import sys
import glob
import pathlib
 
def get_models():
    files_paths = glob.glob( f"{str(pathlib.Path().resolve())}{os.path.sep}custom_models{os.path.sep}{sys.argv[1]}{os.path.sep}**{os.path.sep}*.sql", recursive=True)
    files = []
    for fp in files_paths:
        files.append("".join(str(fp).split(os.path.sep)[-1:]).replace(".sql", ""))

    models_string = ",".join([item.replace(f"{sys.argv[1]}_", "") for item in files]) 
    return models_string

def main():
    models_string = get_models()
    sys.exit(models_string)

if __name__ == '__main__':
    main()
