
# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'kpi_reporting'
version: '1.0.0'
config-version: 2

vars:
   # customer
   customer: 'abbottus'

   # source environment
   src_env: 'preprod'

   # region - eueks, useks, jpeks, cn
   region: 'useks'

   ext_full_refresh: 'true'

   dbt_metrics_calendar_model: aktana_metrics_calendar
   custom_calendar_dimension_list: ["countrycode"]
   msg_topic_rank: 1

   customized_models: ''

# base_model.countrycode = calendar.countrycode
# This setting configures which "profile" dbt uses for this project.
profile: 'kpi_reporting'

# FIXME: query-comment must be disabled for <PERSON> to work because /* block comments are unsupported in Athena DML
# Removing this line will result in a Runtime Error during the integration test
#   `2 of 5 (2) create external table dbt.people_csv_partitioned ...`. The error is
#   "FAILED: ParseException line 1:0 cannot recognize input near '/' '*' '{".
# Is there a better way around this?
query-comment:

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models", "custom_models/{{ env_var('CUSTOMER') }}"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"


# Configuring models
# Full documentation: https://docs.getdbt.com/docs/configuring-models

# In this example config, we tell dbt to build all models in the example/ directory
# as tables. These settings can be overridden in the individual model files
# using the `{{ config(...) }}` macro.
models:
  kpi_reporting:
    +post-hook:
      - "{{ expire_glue_tables() }}"
# Config indicated by + and applies to all files under models/example/
    #example:
    #  +materialized: view

dispatch:
  - macro_namespace: dbt_utils
    search_order: [athena_utils, dbt_utils]
  - macro_namespace: dbt_expectations
    search_order: [athena_utils, dbt_expectations]
  - macro_namespace: metrics
    search_order: [metrics]

quoting:
  database: false
  schema: false
  identifier: false
