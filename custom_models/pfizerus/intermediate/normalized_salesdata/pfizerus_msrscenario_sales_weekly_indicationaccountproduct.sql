{{ config(materialized='table') }}

with scenario_filedate as 
(
select msrscenariouid, sales_filedate from {{ ref('param_msrscenario_sales_filedate_v') }}
),
monthly_sales_data as
(
select sf.msrscenariouid, rsales.indication, rsales.diagnosis_group, rsales.product_group, rsales.account_ref_id, mbp.basketid as product_ref_id,  rsales.sale_date, rsales.frequency, sum(coalesce(rsales.sales_value_trx, 0.0)) sales_value_trx, sum(coalesce(rsales.sales_value_nrx, 0.0)) sales_value_nrx, sum(coalesce(rsales.sales_value_nbrx)) sales_value_nbrx
from {{ ref('raw_sales_weekly_indicationaccountproduct') }} rsales 
inner join {{ ref('analytics_product_group_v') }} apg on apg.external_vod_id__c = rsales.product_ref_id 
inner join {{ ref('akt_marketbasket_product_v') }} mbp on mbp.productname = apg.display_name_vod__c
-- inner join {{ ref('akt_productcatalog_v') }} aktprd on aktprd.name = mbp.brandname and aktprd.iscompetitor = 0 
inner join {{ ref('akt_accounts_v') }} aktact on aktact.customerid = rsales.account_ref_id
inner join {{ ref('account_cs_v')  }} acct on acct.id = aktact.account_vod__c and cast(acct.pdrp_opt_out_vod__c as integer) = 0 and aktact.coveredbyrep = 1
cross join scenario_filedate sf
group by sf.msrscenariouid, rsales.indication, rsales.diagnosis_group, rsales.product_group, rsales.account_ref_id, mbp.basketid, rsales.sale_date, rsales.frequency
)
select r.msrscenariouid, r.indication, r.diagnosis_group, product_group, account_vod__c as accountuid, prodmap.id as productuid, acct.accountid, prod.productid, account_ref_id, product_ref_id, sale_date, sales_value_trx, sales_value_nrx, sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
from monthly_sales_data r inner join  {{ ref('akt_accounts_v') }} acctmap on r.account_ref_id = acctmap.customerid inner join {{ ref('akt_productcatalog_v') }}  prodmap on r.product_ref_id = prodmap.name and prodmap.iscompetitor = 0 and prodmap.isactive = 1 inner join {{ ref('account_dse_v') }} acct on acctmap.account_vod__c = acct.externalid inner join {{ ref('product_v') }} prod on prodmap.id = prod.externalid
