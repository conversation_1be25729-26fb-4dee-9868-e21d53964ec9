{{ config(materialized='view') }}

with combined_sales_ranked as (
       select filedate, indication, diagnosis_group, product_group, account_ref_id, product_ref_id, frequency, sale_date, sales_value_trx, 
       cast(null as double) as sales_value_nrx, sales_value_nbrx, sales_value_unit, cast(null as double) as sales_value_revenue,
       ROW_NUMBER() OVER(PARTITION BY indication, diagnosis_group, product_group, account_ref_id, product_ref_id, frequency, sale_date ORDER BY filedate desc ) AS row_number
       from {{ source('pfitransformedsales', 'pfi_raw_sales_weekly_indicationaccountproduct') }}
)
select filedate, indication, diagnosis_group, product_group, account_ref_id, product_ref_id, frequency, sale_date, sales_value_trx,  
       sales_value_nrx, sales_value_nbrx, sales_value_unit, sales_value_revenue
from combined_sales_ranked where row_number = 1

