{{ config(materialized='table') }}

with nrx_weekly as (
select date_parse(filedate, '%Y%m%d') as filedate, date_trunc('week', date_parse(week_end_date, '%Y-%m-%d')) as sale_date, 
date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) as sale_month_date,
customer_identifier as account_ref_id, product_name as product_ref_id, data_point_value 
from {{ source('rawsales', 'ext_sales_gne_sales_data') }} 
where data_point_type = 'Vol' and data_point_unit = 'NRX' and data_point_time_period = '1mo'
and data_point_period = 'Cur'
),
nrx_weekly_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from nrx_weekly
),

nrx_weekly_latest as (
    select sale_date, sale_month_date, account_ref_id, product_ref_id, 'NRX' as data_point_type, data_point_value from nrx_weekly_ranked
    where row_no = 1
),

missing_nrx_weekly as (
select date_parse(filedate, '%Y%m%d') as filedate, date_trunc('week', date_parse(week_end_date, '%Y-%m-%d')) as sale_date, 
date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) as sale_month_date,
customer_identifier as account_ref_id, product_name as product_ref_id, data_point_value 
from {{ source('rawsales', 'ext_sales_gne_sales_data') }} 
where data_point_type = 'Vol' and data_point_unit = 'NRX' and data_point_time_period = '3mo'
and data_point_period = 'Cur' and (
     date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2025-05-01')
),
missing_nrx_weekly_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from missing_nrx_weekly
),
missing_nrx_weekly_latest as (
    select sale_date, sale_month_date, account_ref_id, product_ref_id, 'NRX' as data_point_type, (data_point_value/3.0) as data_point_value from missing_nrx_weekly_ranked
    where row_no = 1
),

missing_units_weekly as (
select date_parse(filedate, '%Y%m%d') as filedate, date_trunc('week', date_parse(week_end_date, '%Y-%m-%d')) as sale_date, 
date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) as sale_month_date,
customer_identifier as account_ref_id, product_name as product_ref_id, data_point_value 
from {{ source('rawsales', 'ext_sales_gne_sales_data') }} 
where data_point_type = 'Vol' and data_point_unit = 'EQUIVALIZED_UNITS_SUPPLEMENTAL' and data_point_time_period = '1wk'
and data_point_period = 'Cur' and (date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2024-10-01' OR 
     date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2024-11-01' or 
     date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2025-05-01' or 
     date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2025-06-01' or 
     date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) = date '2025-07-01')
),
missing_units_weekly_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from missing_units_weekly
),
missing_units_weekly_latest as (
    select sale_date, sale_month_date, account_ref_id, product_ref_id, 'UNITS' as data_point_type, 
    case when (sale_month_date = date '2024-10-01') then data_point_value*5.0  
    when (sale_month_date = date '2024-11-01') then data_point_value*4.0 
    when (sale_month_date = date '2025-05-01') then data_point_value*5.0 
    when (sale_month_date = date '2025-06-01') then data_point_value*5.0 
    when (sale_month_date = date '2025-07-01') then data_point_value*4.0 end as data_point_value
    from missing_units_weekly_ranked
    where row_no = 1
),

units_weekly as (
select date_parse(filedate, '%Y%m%d') as filedate, date_trunc('week', date_parse(week_end_date, '%Y-%m-%d')) as sale_date, 
date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) as sale_month_date,
customer_identifier as account_ref_id, product_name as product_ref_id, data_point_value 
from {{ source('rawsales', 'ext_sales_gne_sales_data') }} 
where data_point_type = 'Vol' and data_point_unit = 'EQUIVALIZED_UNITS_SUPPLEMENTAL' and data_point_time_period = '1mo'
and data_point_period = 'Cur'
),
units_weekly_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from units_weekly
),
units_weekly_latest as (
    select sale_date, sale_month_date, account_ref_id, product_ref_id, 'UNITS' as data_point_type, data_point_value from units_weekly_ranked
    where row_no = 1
),

revenue_weekly as (
select date_parse(filedate, '%Y%m%d') as filedate, date_trunc('week', date_parse(week_end_date, '%Y-%m-%d')) as sale_date, 
date_trunc('month', date_parse(week_end_date, '%Y-%m-%d')) as sale_month_date,
customer_identifier as account_ref_id, product_name as product_ref_id, data_point_value 
from {{ source('rawsales', 'ext_sales_gne_sales_data') }} 
where data_point_type = 'Vol' and data_point_unit = 'GNE_867_DOLLARS' and data_point_time_period = '1mo'
and data_point_period = 'Cur'
),
revenue_weekly_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from revenue_weekly
),
revenue_weekly_latest as (
    select sale_date, sale_month_date, account_ref_id, product_ref_id, 'REVENUE' as data_point_type, data_point_value from revenue_weekly_ranked
    where row_no = 1
),


merged as (
    select * from nrx_weekly_latest
    union all
    select * from missing_nrx_weekly_latest
    union all
    select * from units_weekly_latest
    union all
    select * from missing_units_weekly_latest
    union all
    select * from revenue_weekly_latest
),

merged_week_count as (
    select account_ref_id, product_ref_id, sale_month_date, count(*) as week_count
    from merged
    group by account_ref_id, product_ref_id, sale_month_date 
),

pre_final as (
    select m.sale_date, m.sale_month_date, account_ref_id, product_ref_id, 
           sum(case when data_point_type = 'NRX' then data_point_value else 0 end) as nrx_value, 
           sum(case when data_point_type = 'UNITS' then data_point_value else 0 end) as units_value, 
           sum(case when data_point_type = 'REVENUE' then data_point_value else 0 end) as revenue_value 
    from merged m 
    group by m.sale_date, m.sale_month_date, account_ref_id, product_ref_id
),

final as (
    select p.sale_date, p.account_ref_id, p.product_ref_id, nrx_value/w.week_count as nrx_value, units_value/w.week_count as units_value, revenue_value/w.week_count as revenue_value   
    from pre_final p inner join merged_week_count w on p.account_ref_id = w.account_ref_id and p.product_ref_id = w.product_ref_id and p.sale_month_date = w.sale_month_date 
)

select 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, 
       account_ref_id, product_ref_id, sale_date, 'WEEKLY' as frequency, cast(null as double) as sales_value_trx, 
        cast(nrx_value as double) as sales_value_nrx, cast(null as double) as sales_value_nbrx,
               cast(units_value as double) as sales_value_unit, cast(revenue_value as double) as sales_value_revenue
from final