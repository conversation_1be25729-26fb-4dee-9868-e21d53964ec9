{{ config(materialized='table') }}

select r.indication, r.diagnosis_group, r.product_group, r.account_ref_id,  acct.externalid as accountuid, acct.accountname,  prodmap.id as productuid, acct.accountid, prod.productid, prod.productname, r.product_ref_id,  
       r.sale_date, sales_value_trx, sales_value_nrx, sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
  from {{ ref('external_sales_weekly_indicationaccountproduct') }} r inner join  {{ ref('account_cs_v') }} acctmap on r.account_ref_id = acctmap.external_id_vod__c inner join {{ ref('akt_productcatalog_v') }} prodmap on r.product_ref_id = prodmap.name inner join {{ ref('account_dse_v') }} acct on acctmap.id = acct.externalid inner join {{ ref('product_v') }} prod on prodmap.id = prod.externalid;
  