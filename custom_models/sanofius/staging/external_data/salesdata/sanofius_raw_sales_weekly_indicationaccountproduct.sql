{{ config(materialized='view') }}

with weekly_nrxtrx_data as
(
select 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, rsales.customerid as account_ref_id, rsales.productName as product_ref_id, DATE_PARSE(rsales.filedate, '%Y%m%d') as sale_date, 'weekly' as frequency, rsales.cur1w_TRx_akt as sales_value_trx, cast(null as double) as sales_value_nrx
from {{ source('rawsales', 'ext_sales_akt_trxnrx_data') }} rsales
where day_of_week(DATE_PARSE(rsales.filedate, '%Y%m%d')) = 1
),
weekly_nbrx_data as
(
select 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, rsales.customerid as account_ref_id, rsales.productName as product_ref_id, DATE_PARSE(rsales.filedate, '%Y%m%d') as sale_date, 'weekly' as frequency, rsales.cur1w_nbrx_akt as sales_value_nbrx
from {{ source('rawsales', 'ext_sales_akt_nbrx_data') }} rsales
where day_of_week(DATE_PARSE(rsales.filedate, '%Y%m%d')) = 1
),
combined_sales as
(
  select coalesce(a.indication, b.indication) as indication, coalesce(a.diagnosis_group, b.diagnosis_group) as diagnosis_group, 
         coalesce(a.product_group, b.product_group) as product_group,
         coalesce(a.account_ref_id, b.account_ref_id) account_ref_id, coalesce(a.product_ref_id, b.product_ref_id) product_ref_id,
         coalesce(a.sale_date, b.sale_date) as sale_date, coalesce(a.frequency, b.frequency) as frequency, a.sales_value_trx, a.sales_value_nrx, b.sales_value_nbrx
  from weekly_nrxtrx_data a full join weekly_nbrx_data b on a.indication = b.indication and a.diagnosis_group = b.diagnosis_group and
       a.account_ref_id = b.account_ref_id and a.product_ref_id = b.product_ref_id and a.sale_date = b.sale_date and a.frequency = b.frequency
)
select r.indication, r.diagnosis_group, r.product_group, r.account_ref_id, r.product_ref_id, r.sale_date, r.frequency, r.sales_value_trx, r.sales_value_nrx, r.sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
from combined_sales r

