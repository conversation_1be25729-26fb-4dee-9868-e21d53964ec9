{{ config(materialized='table') }}

select b.seconfigid, a.factoruid, case when b.seconfigid = 5 then 1011 when b.seconfigid = 8 then 1062 when b.seconfigId =9 then 1001 when b.seconfigId =32 then 1014 when b.seconfigId =41 then 1032 end 
as strategyid, min(a.factorname) as factorname from 
{{ ref('sparkdserunconfigfactor_v') }} a inner join 
{{ ref('sparkdserun_v') }} b on a.runuid = b.runuid
where a.factoruid is not null
group by b.seconfigid, a.factoruid, case when b.seconfigid = 5 then 1011 when b.seconfigid = 8 then 1062 when b.seconfigId =9 then 1001 when b.seconfigId =32 then 1014 when b.seconfigId =41 then 1032 end

