{{ config(materialized='table') }}

select b.seconfigid, a.factoruid, case when b.seconfigid = 4 then 1019 when b.seconfigid = 2 then 1057 when b.seconfigId = 3 then 1009 end as strategyid, min(a.factorname) as factorname from 
{{ ref('sparkdserunconfigfactor_v') }} a inner join 
{{ ref('sparkdserun_v') }} b on a.runuid = b.runuid
where a.factoruid is not null
group by b.seconfigid, a.factoruid, case when b.seconfigid = 4 then 1019 when b.seconfigid = 2 then 1057 when b.seconfigId = 3 then 1009 end

