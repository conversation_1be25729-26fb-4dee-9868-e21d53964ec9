{{ config(materialized='table') }}

with trx_weekly_totals as (
select date_parse(filedate, '%Y%m%d') as filedate, date_parse(week_end_date, '%Y-%m-%d') as sale_date, npi as account_ref_id, 'FSL_All' as product_ref_id, sum(retail_trx) sales_value_trx
from {{ source('rawsales', 'ext_sales_adc_trxnrx_data') }} 
where manufacturer = 'ADC' 
group by date_parse(filedate, '%Y%m%d'), date_parse(week_end_date, '%Y-%m-%d'), npi 
),
trx_weekly_totals_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from trx_weekly_totals
),
trx_weekly_totals_latest as (
  select sale_date, account_ref_id, product_ref_id, sales_value_trx from trx_weekly_totals_ranked 
  where row_no = 1
),
nbrx_weekly_totals as (
select date_parse(filedate, '%Y%m%d') as filedate, date_parse(week_end_date, '%Y-%m-%d') as sale_date, npi as account_ref_id, 'FSL_All' as product_ref_id, sum(actual_nbrx) sales_value_nbrx
from {{ source('rawsales', 'ext_sales_adc_nbrx_data') }} 
where channel = 'RETAIL' and product like '%SENSOR%' and manufacturer = 'ADC' 
group by date_parse(filedate, '%Y%m%d'), date_parse(week_end_date, '%Y-%m-%d'), npi
),
nbrx_weekly_totals_ranked as (
select *,
    ROW_NUMBER() over(partition by sale_date, account_ref_id, product_ref_id order by filedate desc) as row_no
    from nbrx_weekly_totals
),
nbrx_weekly_totals_latest as (
  select sale_date, account_ref_id, product_ref_id, sales_value_nbrx from nbrx_weekly_totals_ranked
  where row_no = 1
),

combined_sales as
(
  select 
         coalesce(a.account_ref_id, b.account_ref_id) account_ref_id, coalesce(a.product_ref_id, b.product_ref_id) product_ref_id,
         coalesce(a.sale_date, b.sale_date) as sale_date, a.sales_value_trx, b.sales_value_nbrx
  from trx_weekly_totals_latest a full join nbrx_weekly_totals_latest b on 
       a.account_ref_id = b.account_ref_id and a.product_ref_id = b.product_ref_id and a.sale_date = b.sale_date
)

select 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, 
       account_ref_id, product_ref_id, sale_date, 'WEEKLY' as frequency, cast(sales_value_trx as double) as sales_value_trx, cast(null as double) as sales_value_nrx, cast(sales_value_nbrx as double) as sales_value_nbrx,
               cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
from combined_sales 
