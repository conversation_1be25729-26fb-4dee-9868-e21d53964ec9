{{ config(materialized='table') }}

with latest_ad_sales_filedate as
(
  select max(filedate) latest_filedate from  {{ source('rawsales', 'ext_sales_snsob_ad_core_wk') }}
),
latest_as_sales_filedate as
(
  select max(filedate) latest_filedate from  {{ source('rawsales', 'ext_sales_snsob_as_core_wk') }}
),
weekly_ad_sales_data as
(
select 'Atopic Dermatitis' as indication, rsales.DIAGNOSIS_GROUP as diagnosis_group, rsales.PROD_GRP as product_group, CURR_CCT_ID as account_ref_id,  DATE_PARSE(rsales.WEEK_ID,'%m/%d/%Y') as sale_date, 'weekly' as frequency, XPO_TRX as sales_value_trx, XPO_NRX as sales_value_nrx, NBRX as sales_value_nbrx
from {{ source('rawsales', 'ext_sales_snsob_ad_core_wk') }} rsales inner join latest_ad_sales_filedate l on rsales.filedate = l.latest_filedate
),
weekly_as_sales_data as
(
select 'Other Indications' as indication, rsales.DIAGNOSIS_GROUP as diagnosis_group, rsales.PROD_GRP as product_group, CURR_CCT_ID as account_ref_id, DATE_PARSE(rsales.WEEK_ID,'%m/%d/%Y') as sale_date, 'weekly' as frequency, XPO_TRX as sales_value_trx, XPO_NRX as sales_value_nrx, NBRX as sales_value_nbrx
from {{ source('rawsales', 'ext_sales_snsob_as_core_wk') }} rsales inner join latest_as_sales_filedate l on rsales.filedate = l.latest_filedate
),
combined_sales as
(
select indication, diagnosis_group, product_group, account_ref_id, sale_date, frequency, sales_value_trx, sales_value_nrx, sales_value_nbrx from  weekly_ad_sales_data
union
select indication, diagnosis_group, product_group, account_ref_id, sale_date, frequency, sales_value_trx, sales_value_nrx, sales_value_nbrx from weekly_as_sales_data
)
select r.indication, r.diagnosis_group, r.product_group, r.account_ref_id, r.sale_date, r.frequency, r.sales_value_trx, r.sales_value_nrx, r.sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
from combined_sales r

