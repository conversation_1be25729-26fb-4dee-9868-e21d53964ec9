{{ config(materialized='table') }}

with weekly_sales_data as
(
select rsales.indication, rsales.diagnosis_group, rsales.product_group, rsales.account_ref_id, Child_Product as product_ref_id, rsales.sale_date, rsales.frequency, sum(coalesce(rsales.sales_value_trx, 0.0)) sales_value_trx, sum(coalesce(rsales.sales_value_nrx, 0.0)) sales_value_nrx, sum(coalesce(rsales.sales_value_nbrx, 0.0)) sales_value_nbrx 
from {{ ref('external_sales_weekly_indicationaccountproduct') }} rsales inner join {{ ref('akt_productmap_v') }} pmap on rsales.diagnosis_group = pmap.diagnosis_group  and pmap.Parent_Product = rsales.product_group
group by rsales.indication, rsales.diagnosis_group, rsales.product_group, rsales.account_ref_id, Child_Product, rsales.sale_date, rsales.frequency
)
select r.indication, r.diagnosis_group, product_group, account_vod__c as accountuid, prodmap.id as productuid, acct.accountid, prod.productid, account_ref_id, product_ref_id, sale_date, sales_value_trx, sales_value_nrx, sales_value_nbrx, cast(null as double) as sales_value_unit, cast(null as double) as sales_value_revenue
from weekly_sales_data r inner join  {{ ref('akt_accounts_v') }} acctmap on r.account_ref_id = acctmap.customerid inner join {{ ref('akt_productcatalog_v') }}  prodmap on r.product_ref_id = prodmap.name inner join {{ ref('account_dse_v') }} acct on acctmap.account_vod__c = acct.externalid inner join {{ ref('product_v') }} prod on prodmap.id = prod.externalid
