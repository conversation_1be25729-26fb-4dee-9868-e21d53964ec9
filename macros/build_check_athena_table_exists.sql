{% macro build_check_athena_table_exists(database_name, table_name) %}
{% set attributeQuery %}
SELECT cast(count(*) as int) as tableCount
FROM (
    SELECT t.table_name,
        t.table_schema AS database_name,
        t.table_catalog,
        t.table_type
    FROM information_schema.tables t
    LEFT JOIN information_schema.views v ON t.table_name = v.table_name
    AND t.table_schema = v.table_schema
    WHERE t.table_schema <> 'information_schema'
    AND t.table_schema = '{{database_name}}'
    AND v.table_name IS NULL
    AND t.table_name = '{{table_name}}'
)
{% endset %}
    {% set results = run_query(attributeQuery) %}
    {% set flag = false %}
    {% if execute %}
            {% if results.columns[0][0] == 0 %}
                {% set flag = false %}
            {% else %} 
                {% set flag = true %}    
            {% endif %}   
    {% else %}
            {{ "temp" }}
    {% endif %}
    {% do return(flag) %}
{% endmacro %}
