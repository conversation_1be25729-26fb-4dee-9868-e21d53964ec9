{% macro build_repaccount_expr(prefix) %}
  {% set query %}

        select        
            concat( 'case ' ,
                array_join(array_agg(concat(' when ',   '{{ prefix }}.msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and ', '{{ prefix }}.repaccount_istarget_rpt is not null then cast(cast(
                                                case when upper(cast (', repaccount_istarget_rpt, ' as varchar)) = ''YES'' then ''true''
                                                     when upper(cast (', repaccount_istarget_rpt, ' as varchar)) = ''NO'' then ''false''
                                                     else cast(', repaccount_istarget_rpt ,' as varchar) end as boolean) as integer) ')), ' '),
                    ' else cast(null as integer) end as repaccount_istarget_rpt ') as repaccount_istarget_rpt_expr
        from {{ ref('param_msrscenario_repaccount_fields_v') }}

  {% endset %}

  {% set results = run_query(query) %}

  {%if execute %}
    ,
    {{ results.columns[0][0] }}
  {% endif %}

{% endmacro %}
