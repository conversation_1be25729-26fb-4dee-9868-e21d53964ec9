
{% macro build_interpolated_tables(base_table_name, track_field_names, idfields, joinfields) %}

    with date_tab as (
        select * from {{ ref('as_of_date') }} where dt >= {{build_rolling_two_years_date(2)}} and day = 1 
        and dt <= {{build_get_end_of_the_frequency(2)}}
    ),
    unique_{{ base_table_name }} as (
        select distinct {{ idfields }} from {{ ref(base_table_name) }} b where updatedat >= {{build_rolling_two_years_date(2)}}
        and updatedat <= {{build_get_end_of_the_frequency(2)}}
    ),
    spine_{{ base_table_name }} as (
        select {{ idfields }}, a.* from unique_{{ base_table_name }} b cross join date_tab a
    ),
    row_num_{{ base_table_name }} as (
        select b.*, ROW_NUMBER() OVER ( PARTITION BY {{ idfields }}, YEAR(updatedat), month(updatedat) ORDER BY b.updatedat asc
            ) AS min_row_number
        from {{ ref(base_table_name) }} b
        order by updatedat asc
    ),
    row_by_month as (
        select * from row_num_{{ base_table_name }} where min_row_number = 1
    ),
    interpolated_{{ base_table_name }} as (
        select {{ idfields }}, b.dt,
        {% for track_field_name in track_field_names %}
            COALESCE(a.{{ track_field_name }}, lead(a.{{ track_field_name }}) IGNORE NULLS OVER (
                    PARTITION BY {{ idfields }} ORDER BY {{ idfields }} desc, b.dt desc )
            ) as {{ track_field_name }}
                {% if not loop.last %}
                            ,
                {% endif %}
        {% endfor %}
        from spine_{{ base_table_name }} b
            left join row_by_month a on cast(a.updatedatyear as int) = b.year and cast(a.updatedatmonth as int) = b.month 
            and {{ joinfields }}
        order by {{ idfields }},
            b.dt desc
    )
{% endmacro %}


