{% macro build_rolling_two_years_date(flag) %}
  {% set query %}
     SELECT date_trunc('MONTH', date_add('year', -2, current_date)) AS rollingdate;
  {% endset %}
  
  {% set results = run_query(query) %}
  {%if execute %}
    {%if flag == 1 %} {# flag=1 will return datevalue as string #}
      {{ results.columns[0][0] }}
    {% else %} {# flag=2 will return converted datevalue #}
      to_date('{{ results.columns[0][0] }}','yyyy-mm-dd')
    {% endif %} 
  {% endif %}

{% endmacro %}
