{% macro build_company() %}

{% set var_customer = var("customer") %}
{% set var_region = var("region")  %}
{% set var_region2 = var("customer")[-2:] %}
{% set var_region3 = var("customer")[-3:] %}

{% if "eks" in var("region") %}
{% set var_region = var("region")[0:-3] %}
{% endif %}

{% if (var_region == var_region2) %}
{% set var_company = var("customer")[0:-2] %}
{% elif (var_region in var_region3) %}
{% set var_company = var("customer")[0:-3] %}
{% else %}
{% set var_company = var("customer")[0:-2] %}
{% endif %}

{% do return(var_company) %}

{% endmacro %}
