{% macro build_check_is_veeva_customer() %}
{% set query %}
 SELECT count(*)
 FROM {{ ref('papi_param_filterproperties_v') }} 
 where propKey = 'kpiReportingDBT.isVeevaCustomer' and lower(strvalue) = 'no'     
{% endset %}
    {% set results = run_query(query) %}
    {% if execute %}
        {% if results.columns[0][0] == 0 %}
            {% do return(true) %}
        {% else %}
            {% do return(false) %}
        {% endif %}
    {% else %}
            {{ "temp" }}
    {% endif %}
{% endmacro %}
