{% macro build_accountdse_expr() %}
  {% set query %}

        select        
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and account_dim1_rpt is not null then cast(', account_dim1_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as account_dim1_rpt ') as account_dim1_rpt_expr,
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and account_dim2_rpt is not null then cast(', account_dim2_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as account_dim2_rpt ') as account_dim2_rpt_expr
        from {{ ref('param_msrscenario_account_fields_v') }}

  {% endset %}

  {% set results = run_query(query) %}

  {%if execute %}
    ,
    {{ results.columns[0][0] }},
    {{ results.columns[1][0] }}
  {% endif %}

{% endmacro %}
