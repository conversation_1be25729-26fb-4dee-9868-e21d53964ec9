
{% macro build_msrscenario_accountdse_statechange_sql(base_table_name, idfields="a.accountid", joinfields="a.accountid = b.accountid") %}

  {% set query %}

        select        
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and account_dim1_rpt is not null then cast(', account_dim1_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end ') as account_dim1_rpt_expr,
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and account_dim2_rpt is not null then cast(', account_dim2_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end ') as account_dim2_rpt_expr
        from {{ ref('param_msrscenario_account_fields_v') }}

  {% endset %}

  {% set results = run_query(query) %}

 {% if execute %}

  with
    {% set all_track_field_names = [results.columns[0][0], results.columns[1][0]] %}
    {% set all_track_stdfield_names = ["account_dim1_rpt", "account_dim2_rpt"] %}

    {% for std_field_name_derv in all_track_stdfield_names %}
        {% set track_field_name_derv = all_track_field_names[loop.index0] %}

        {{ base_table_name }}_daily_{{ std_field_name_derv }}  as (
            {# pick last day of the month value as the value for the month #}

            select msrscenariouid, {{ idfields }}, cast(updatedatyear as int) as updatedatyear, cast(updatedatmonth as int) as updatedatmonth, cast(updatedatday as int) as updatedatday, 
                {{ track_field_name_derv }} as {{ std_field_name_derv }},
            first_value({{ track_field_name_derv }}) OVER(PARTITION BY msrscenariouid, {{ idfields }}, updatedatyear, updatedatmonth ORDER BY cast(updatedatday as int) DESC) as month_{{ std_field_name_derv }}
            from {{ ref(base_table_name) }} a cross join 
                {{ ref('param_msrscenario_account_fields_v') }}
        ), {{ base_table_name }}_monthly_{{ std_field_name_derv }} as (
            {# aggregate it up to montly level #}

            select msrscenariouid, {{ idfields }}, updatedatyear, updatedatmonth, cast(max(month_{{ std_field_name_derv }}) as varchar) as month_{{std_field_name_derv}}
            from {{ base_table_name }}_daily_{{ std_field_name_derv }} a where month_{{ std_field_name_derv }} is not null
            group by msrscenariouid, {{ idfields }}, updatedatyear, updatedatmonth
        ), {{ base_table_name }}_unique_{{ std_field_name_derv }} as (
            {# find unique account products #}

            select msrscenariouid, {{ idfields }} 
            from {{ base_table_name }}_monthly_{{ std_field_name_derv }} a  group by msrscenariouid, {{ idfields }}
        ), {{ base_table_name }}_spine_{{ std_field_name_derv }} as (
            {# build a spine with account, product, year, month #}

            select msrscenariouid, {{ idfields }}, b.year as as_of_year, b.month as as_of_month from {{ base_table_name }}_unique_{{ std_field_name_derv }} as a cross join {{ ref('as_of_month')  }}  as b
        ), {{ base_table_name }}_interpolated_{{ std_field_name_derv }} as (
            {# interpolate values for each month from previous months value #}
            select a.msrscenariouid, {{ idfields }}, a.as_of_year, a.as_of_month, '{{ std_field_name_derv }}' as field_name, COALESCE(b.month_{{ std_field_name_derv }},
            lead(b.month_{{ std_field_name_derv }}) IGNORE NULLS OVER (PARTITION BY a.msrscenariouid, {{ idfields }} ORDER BY a.as_of_year desc, a.as_of_month desc)) as month_value
            from {{ base_table_name }}_spine_{{ std_field_name_derv }} as a left join {{ base_table_name }}_monthly_{{ std_field_name_derv }} b on {{ joinfields }}  and a.msrscenariouid = b.msrscenariouid and b.updatedatyear = a.as_of_year and b.updatedatmonth = a.as_of_month
        )

        {% if not loop.last %}
            ,
        {% endif %}

    {% endfor %}

    {% for std_field_name_derv in all_track_stdfield_names %}
        select * from {{ base_table_name }}_interpolated_{{ std_field_name_derv }}

        {% if not loop.last %}
            union
        {% endif %}

    {% endfor %}

  {% endif %}

{% endmacro %}
