{% macro build_get_end_of_the_frequency(flag) %}

{% set countrycode = build_country() %}
  {% set query %}
    SELECT 
    COALESCE(  
        (SELECT frequency FROM {{ ref('fiscalperioddefinition_v') }} WHERE countrycode = '{{countrycode}}' and isdefault = true),
        (SELECT frequency FROM {{ ref('fiscalperioddefinition_v') }} WHERE countrycode = 'DEFAULT' and isdefault = true)
    ) AS frequency;
  {% endset %}
  
  {% set results = run_query(query) %}
  {%if execute %}
    {% set frequency = results.columns[0][0] %}  
    {% set datequery %}
      SELECT 
      COALESCE(  
          (SELECT enddate FROM {{ ref('fiscalperiod_v') }} WHERE frequency = '{{frequency}}' and countrycode = '{{countrycode}}'
              and startdate <= current_date and enddate >= current_date),
          (SELECT enddate FROM {{ ref('fiscalperiod_v') }} WHERE frequency = '{{frequency}}' and countrycode = 'DEFAULT'
              and startdate <= current_date and enddate >= current_date)
      ) AS enddate;
    {% endset %}
    {% set datval = run_query(datequery) %}
    {%if execute %}
      {%if flag == 1 %} {# flag=1 will return datevalue as string #}
        {{ datval.columns[0][0] }}
      {% else %} {# flag=2 will return converted datevalue #}
        to_date('{{ datval.columns[0][0] }}','yyyy-mm-dd')
      {% endif %} 
    {% endif %}
  {% endif %}
{% endmacro %}
