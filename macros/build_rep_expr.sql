{% macro build_rep_expr() %}
  {% set query %}

        select
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                             ' and ', pilot_rep_predicate, ' then cast(''pilot'' as varchar) ')), ' '),
                    ' else cast(''control'' as varchar) end as pilotcontrol_rep')  as pilot_rep_expr
        from {{ ref('param_msrscenario_pilotcontrolexclude_rep_predicate_v') }}

  {% endset %}

  {% set results = run_query(query) %}

  {%if execute %}
    ,
    {{ results.columns[0][0] }}
  {% endif %}

{% endmacro %}
