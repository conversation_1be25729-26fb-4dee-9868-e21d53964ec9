
{% macro build_get_attribute_type_def(table_name, idfields, joinfields) %}

{% set attributeQuery %}
 SELECT distinct s3fieldname, CASE rdsobjectname
    WHEN  'AccountProduct' THEN 'back_fill_accountproduct'
    WHEN 'Account' THEN 'back_fill_account'
    WHEN 'RepAccountAssignment' THEN 'back_fill_repaccountassignment'
     ELSE rdsobjectname END as tab_name
 FROM {{ ref('attributetypedefinition_v') }} 
 where rdsobjectname = '{{table_name}}' and cast(isdeleted as boolean) = false and s3fieldname is not null 
    order by s3fieldname asc    
{% endset %}
    {% set results = run_query(attributeQuery) %}
    {% if execute %}
        {%- set rdsCols = results.columns[0].values() -%}
        {%- set tname = results.columns[1][0] -%}
        {{ build_interpolated_tables(tname, rdsCols, idfields, joinfields) }}
    {% else %}
            {{ "temp" }}
    {% endif %}
{% endmacro %}
