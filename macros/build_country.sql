{% macro build_country() %}

{% set var_customer = var("customer")  %}
{% set var_region = var("region")  %}
{% set var_multi_country = build_get_is_multicountry_flag() %}

{% if "eks" in var("region") %}
{% set var_region = var("region")[0:-3] %}
{% endif %}


{% set var_region2 = var("customer")[-2:] %}
{% set var_region3 = var("customer")[-3:] %}
{% set var_country = var("customer")[-2:] %}

{% if (var_multi_country == 'false') %}
{% set var_country = var("customer")[-2:] %}
{% else %}
{% set var_country = '' %}
{% endif %}

{% do return(var_country.upper()) %}

{% endmacro %}
