{% macro build_adlh_channel_count(table_name, group_fields, channel_name, column_name) %}
WITH interaction_with_dates as (
    SELECT a.*,
    date_format(activityDate, '%Y-%m') as yearMonth,
    week(activityDate) as interactionWeek
    FROM {{ ref(table_name) }} a
)    
    
    
    SELECT 
        {{ group_fields }}, 
        yearMonth,
        count(distinct interactionid) as {{ column_name }}1MonthCount,
        count(distinct activityDate) as {{ column_name }}1MonthDayCount,
        count(distinct interactionWeek) as {{ column_name }}1MonthWeekCount
    FROM interaction_with_dates
    WHERE currentChannel = '{{ channel_name }}'
    AND activityDate >= date_add('month', -12, ({{ get_max_date(table_name, 'activityDate') }}) )
    GROUP BY 
    {{ group_fields }}, yearMonth
{% endmacro %}