{% macro build_json_query() %}
    {% set table_schema= var('customer') ~ '_' ~ var('src_env') %}
    {% set aktColQuery %}
        SELECT column_name FROM information_schema.columns where table_schema = '{{table_schema}}'
        AND table_name = 'event' and column_name like '%_akt'
    {% endset %}
    {% set results = run_query(aktColQuery) %}
    {% if execute %}
        {%- set queryResults = build_get_query(results.columns[0].values()) -%}
        {{ queryResults }}
    {% else %}
            {{ "temp" }}
    {% endif %}
{% endmacro %}
