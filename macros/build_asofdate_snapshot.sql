
{% macro build_asofdate_snapshot_sql(table_name, idfields, asof_view_name, rankorderbyfields="cast(updatedatyear as int) desc, cast(updatedatmonth as int) desc, cast(updatedatday as int) desc", wherecondition=" cast(updatedatyear as int) <= as_of_year and cast(updatedatmonth as int) <= as_of_month and cast(updatedatday as int) <= as_of_day") %}
with {{ table_name }}_ranked as (
  SELECT
    *,
    ROW_NUMBER() OVER(PARTITION BY {{ idfields }} ORDER BY {{ rankorderbyfields }} ) AS row_number
  FROM {{ ref(table_name)  }} cross join {{ ref(asof_view_name) }}  {%- if wherecondition != '' -%} {{ ' where ' }} {%- endif -%}  {{ wherecondition }}
)
SELECT * from {{ table_name }}_ranked a WHERE a.row_number = 1;
{% endmacro %}
