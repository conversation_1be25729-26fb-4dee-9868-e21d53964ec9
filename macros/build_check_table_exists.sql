{% macro build_check_table_exists(table_name) %}
{% set attributeQuery %}
 SELECT cast(count(*) as int) as tableCount
 FROM {{ ref('attributetypedefinition_v') }} 
 where rdsobjectname = '{{table_name}}' and cast(isdeleted as boolean) = false   
{% endset %}
    {% set results = run_query(attributeQuery) %}
    {% set flag = false %}
    {% if execute %}
            {% if results.columns[0][0] == 0 %}
                {% set flag = false %}
            {% else %} 
                {% set flag = true %}    
            {% endif %}   
    {% else %}
            {{ "temp" }}
    {% endif %}
    {% do return(flag) %}
{% endmacro %}
