{% macro build_call_type_expr() %}
  {% set query %}

        select
            concat(call_type, ' as call_type' )  as call_type_expr, 
            concat(call_channel_raw, ' as call_channel_raw' )  as call_channel_raw_expr,
            concat(call_channel_category, ' as call_channel_category' )  as call_channel_category_expr,
            concat(call_channel_dse, ' as call_channel_dse' )  as call_channel_dse_expr 
        from {{ ref('param_msrscenario_call_type_v') }}

  {% endset %}

  {% set results = run_query(query) %}

  {%if execute %}
    {{ results.columns[0][0] }},
    {{ results.columns[1][0] }},
    {{ results.columns[2][0] }},
    {{ results.columns[3][0] }}
  {% endif %}

{% endmacro %}