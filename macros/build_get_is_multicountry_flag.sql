{% macro build_get_is_multicountry_flag() %}
{% set attributeQuery %}
select case when count(*) = 0 then 'false' else 'true' end as is_multicountry_flag from 
(
select configcountrycode 
from actor_v
where configcountrycode <> 'DEFAULT'
group by configcountrycode
)
{% endset %}
    {% set results = run_query(attributeQuery) %}
    {% if execute %}
        {% for column in results.columns[0].values() %}
            {% do return(column) %}
        {% endfor %}
    {% else %}
            {% do return('false') %}
    {% endif %}
{% endmacro %}



