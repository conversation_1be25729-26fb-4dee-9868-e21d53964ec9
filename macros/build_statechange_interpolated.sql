
{% macro build_statechange_interpolated_sql(base_table_name, track_field_name, std_field_name, idfields="a.accountid,a.productid", joinfields="a.accountid = b.accountid and a.productid = b.productid") %}
with {{ base_table_name }}_daily_{{ track_field_name }}  as (
    {# pick last day of the month value as the value for the month #}

    select {{ idfields }}, cast(updatedatyear as int) as updatedatyear, cast(updatedatmonth as int) as updatedatmonth, cast(updatedatday as int) as updatedatday, 
          {{ track_field_name }} as {{ std_field_name }},
    first_value({{ track_field_name }}) OVER(PARTITION BY {{ idfields }}, updatedatyear, updatedatmonth ORDER BY cast(updatedatday as int) DESC, updatedat DESC) as month_{{ std_field_name }}
    from {{ ref(base_table_name) }} a
), {{ base_table_name }}_monthly_{{ track_field_name }} as (
    {# aggregate it up to montly level #}

    select {{ idfields }}, updatedatyear, updatedatmonth, max(month_{{ std_field_name }}) as month_{{std_field_name}}
    from {{ base_table_name }}_daily_{{ track_field_name }} a where month_{{ std_field_name }} is not null
    group by {{ idfields }}, updatedatyear, updatedatmonth
), {{ base_table_name }}_unique as (
    {# find unique account products #}

    select {{ idfields }} 
    from {{ base_table_name }}_monthly_{{ track_field_name }} a  group by {{ idfields }}
), {{ base_table_name }}_spine as (
    {# build a spine with account, product, year, month #}

    select {{ idfields }}, b.year as as_of_year, b.month as as_of_month from {{ base_table_name }}_unique as a cross join {{ ref('as_of_month')  }}  as b
), {{ base_table_name }}_interpolated as (
    {# interpolate values for each month from previous months value #}
    select {{ idfields }}, a.as_of_year, a.as_of_month, cast(date_parse(CONCAT(CAST(a.as_of_year AS VARCHAR), '-', lpad(cast(a.as_of_month as varchar), 2, '0'), '-01'),'%Y-%m-%d') as date) as_of_date, COALESCE(b.month_{{ std_field_name }},
    lead(b.month_{{ std_field_name }}) IGNORE NULLS OVER (PARTITION BY {{ idfields }} ORDER BY a.as_of_year desc, a.as_of_month desc)) as month_{{ std_field_name }}
    from {{ base_table_name }}_spine as a left join {{ base_table_name }}_monthly_{{ track_field_name }} b on {{ joinfields }}  and b.updatedatyear = a.as_of_year and b.updatedatmonth = a.as_of_month
)
select * from {{ base_table_name }}_interpolated;
{% endmacro %}
