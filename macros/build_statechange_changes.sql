{% macro build_statechange_changes_sql(base_table_name, track_field_name, std_field_name, idfields="a.accountid,a.productid") %}

with {{ base_table_name }}_interpolated_changes as 
(
    {# determine months when there was a change in hcp tier #} 
    select {{ idfields }}, as_of_year, as_of_month, month_{{ std_field_name }}, 
    lead(month_{{ std_field_name}}) IGNORE NULLS OVER (PARTITION BY {{ idfields }}  ORDER BY as_of_year desc, as_of_month desc) as last_month_{{std_field_name}}
    from {{ ref('accountproduct_tier_interpolated') }} a
    where month_{{ std_field_name }} is not null
)
select * from {{ base_table_name}}_interpolated_changes where month_{{ std_field_name }} != last_month_{{ std_field_name }};

{% endmacro %}
