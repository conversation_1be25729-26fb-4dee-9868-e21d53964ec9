
{% macro build_latest_sql(table_name, idfields, rankorderbyfields="cast(updatedatyear as int) desc, cast(updatedatmonth as int) desc, cast(updatedatday as int) desc, updatedat desc", wherecondition="", dbsource="archivedata", additional_fields = "") %}
with {{ table_name }}_ranked as (
  SELECT
    *,
    ROW_NUMBER() OVER(PARTITION BY {{ idfields }} ORDER BY {{ rankorderbyfields }} ) AS row_number
    {{ additional_fields }}
  FROM {{ source( dbsource, table_name)  }} {%- if wherecondition != '' -%} {{ ' where ' }} {%- endif -%}  {{ wherecondition }}
)
SELECT * from {{ table_name }}_ranked WHERE row_number = 1;
{% endmacro %}
