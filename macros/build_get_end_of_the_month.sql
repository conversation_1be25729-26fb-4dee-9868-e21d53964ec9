{% macro build_get_end_of_the_month(flag) %}
  {% set query %}
      SELECT date_trunc('MONTH', current_date) + interval '1' month - interval '1' day AS rollingdate;
  {% endset %}
  
  {% set results = run_query(query) %}
  {%if execute %}
    {%if flag == 1 %} {# flag=1 will return datevalue as string #}
      {{ results.columns[0][0] }}
    {% else %} {# flag=2 will return converted datevalue #}
      to_date('{{ results.columns[0][0] }}','yyyy-mm-dd')
    {% endif %} 
  {% endif %}

{% endmacro %}
