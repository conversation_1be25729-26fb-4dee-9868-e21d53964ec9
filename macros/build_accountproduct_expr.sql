{% macro build_accountproduct_expr() %}
  {% set query %}

        select        
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and accountproduct_segment_rpt is not null then cast(', accountproduct_segment_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as accountproduct_segment_rpt ') as accountproduct_segment_rpt_expr,
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and accountproduct_tier_rpt is not null then cast(', accountproduct_tier_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as accountproduct_tier_rpt ') as accountproduct_tier_rpt_expr,
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and accountproduct_dim1_rpt is not null then cast(', accountproduct_dim1_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as accountproduct_dim1_rpt ') as accountproduct_dim1_rpt_expr,
            concat( 'case ' ,
                array_join(array_agg(concat(' when ', ' msrscenariouid ', ' = ', '''', msrscenariouid, '''', 
                            ' and accountproduct_dim2_rpt is not null then cast(', accountproduct_dim2_rpt, ' as varchar) ')), ' '),
                    ' else cast(null as varchar) end as accountproduct_dim2_rpt ') as accountproduct_dim2_rpt_expr
        from {{ ref('param_msrscenario_accountproduct_fields_v') }}

  {% endset %}

  {% set results = run_query(query) %}

  {%if execute %}
    ,
    {{ results.columns[0][0] }},
    {{ results.columns[1][0] }},
    {{ results.columns[2][0] }},
    {{ results.columns[3][0] }}
  {% endif %}

{% endmacro %}
