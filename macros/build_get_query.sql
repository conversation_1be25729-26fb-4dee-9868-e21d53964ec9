{% macro build_get_query(columns_list) %}
	SELECT eventid,externalid,JSON_FORMAT(CAST(
			CAST(
				ROW(
				JSON_EXTRACT(
		'{' ||
		{% for column in columns_list %}
			'"{{ column }}":"' || COALESCE(CAST({{ column }} AS VARCHAR), 'NULL') || '"' || {% if not loop.last %} ',' || {% endif %}
		{% endfor %}
		'}', '$' )
				) AS ROW(
					json_data JSON
				)
			) AS JSON
		) ) as json_row
	FROM {{ ref('event_v') }} 
{% endmacro %}