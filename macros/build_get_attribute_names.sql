{% macro build_get_attribute_names(table_name, table_alias) %}
{% set attributeQuery %}
 SELECT distinct s3fieldname
 FROM {{ ref('attributetypedefinition_v') }} 
 where rdsobjectname = '{{table_name}}' and isdeleted = false and s3fieldname is not null 
 order by s3fieldname asc     
{% endset %}
    {% set results = run_query(attributeQuery) %}
    {% if execute %}
        {% for column in results.columns[0].values() %}
            {{ table_alias }}.{{ column }} {% if not loop.last %} ,  {% endif %}
        {% endfor %}
    {% else %}
            {{ "temp" }}
    {% endif %}
{% endmacro %}
