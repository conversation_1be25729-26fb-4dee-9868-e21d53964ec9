1) create a conda environment with python 3.8 (3.7 does not work)

2) pip3 install dbt-athena-community

3) pip3 install "dbt-fal[athena]"

4) In .dbt/profile.yml 
   - creat a target as below 
   - if you want to execute python with athena use the target genzymeusdev_with_fal

    genzymeusdev_with_fal:
      type: fal
      db_profile: genzymeusdev
    genzymeusdev:
      database: awsdatacatalog
      num_retries: 1
      region_name: us-east-1
      s3_staging_dir: s3://aktana-bdpuseks-glue/dbt/satya/genzymeus
      schema: impact_genzymeus_satya
      type: athena
      work_group: primary

5) In the location where you have dbt_project.yml add another file: fal_project.yml
   This will be the environment where the python models will execute

# the config like below is defined to execute python code
environments:
  - name: ml
    type: venv
    requirements:
      - prophet


6) There is a bug in dbt-fal athena.py. Until this is fixed, the dbt-fal:athena.py in the "environment" (ml) above has to be searched and replaced with athena.py checked into dbt-fal-athena-bugfix/ folder in kpi-reporting-dbt. This is typically in the following folder - ./Library/Caches/isolate/virtualenv/


7) under the models folder you can put a *.py file as shown below

   note the environment selection
   you can use dbt.ref("...") to access an model and build dependency
   a model with the same file name will be created in athena
   you can use that as dependency in downstream models

import pandas

def model(dbt, fal):
    dbt.config(materialized="table")
    dbt.config(fal_environment="ml")
    df: pd.DataFrame = dbt.ref("product_v")

    return df
