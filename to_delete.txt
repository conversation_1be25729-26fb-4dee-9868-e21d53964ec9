absl-py==1.4.0
agate==1.7.0
agate-sql==0.5.9
aiobotocore==2.5.0
aiohttp==3.8.5
aioitertools==0.11.0
aiosignal==1.3.1
alabaster==0.7.13
anyio==4.0.0
appnope==0.1.3
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.2.3
asn1crypto==1.5.1
astor==0.8.1
asttokens==2.4.0
astunparse==1.6.3
async-lru==2.0.4
async-timeout==4.0.3
attrs==23.1.0
auth0-python==4.4.0
awswrangler==3.3.0
Babel==2.12.1
backcall==0.2.0
backoff==1.11.1
backports.functools-lru-cache==1.6.6
bcrypt==4.0.1
beautifulsoup4==4.12.2
bleach==6.0.0
boto3==1.26.76
boto3-stubs==1.28.46
botocore==1.29.76
botocore-stubs==1.31.46
CacheControl==0.13.1
cached-property==1.5.2
cachetools==5.3.1
certifi==2023.7.22
cffi==1.15.1
charset-normalizer==2.1.1
click==8.1.7
cloudpickle==2.2.1
colorama==0.4.6
comm==0.1.4
contourpy==1.1.0
convertdate==2.4.0
cryptography==40.0.1
cycler==0.11.0
Cython==0.29.36
datadog-api-client==2.12.0
dbt-athena-community==1.5.1
dbt-core==1.5.1
dbt-extractor==0.4.1
dbt-fal==1.5.1
dbt-snowflake==1.5.1
dbt-spark==1.5.1
debugpy==1.8.0
decorator==5.1.1
defusedxml==0.7.1
delta-spark==2.2.0
Deprecated==1.2.14
deprecation==2.1.0
dill==0.3.7
distlib==0.3.7
docutils==0.20.1
exceptiongroup==1.1.3
executing==1.2.0
fal-serverless==0.6.41
fastjsonschema==2.18.0
filelock==3.12.4
findspark==1.4.2
firebase-admin==5.4.0
flatbuffers==23.5.26
fonttools==4.42.1
fqdn==1.5.1
frozenlist==1.4.0
fsspec==2023.4.0
future==0.18.3
gast==0.4.0
gensim==4.3.2
geographiclib==1.52
geopy==2.2.0
google-api-core==2.11.1
google-api-python-client==2.99.0
google-auth==2.23.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.0.0
google-cloud-bigquery==2.34.4
google-cloud-bigquery-storage==2.22.0
google-cloud-core==2.3.3
google-cloud-firestore==2.12.0
google-cloud-storage==2.10.0
google-crc32c==1.5.0
google-pasta==0.2.0
google-resumable-media==2.6.0
googleapis-common-protos==1.60.0
graphviz==0.20.1
greenlet==2.0.2
grpc-interceptor==0.15.3
grpcio==1.58.0
grpcio-status==1.48.2
h11==0.14.0
h5py==3.9.0
hdbscan==0.8.33
hijri-converter==2.3.1
holidays==0.11.1
hologram==0.0.16
httpcore==0.17.3
httplib2==0.22.0
httpx==0.24.1
idna==3.4
imagesize==1.4.1
importlib-metadata==6.8.0
importlib-resources==6.0.1
ipykernel==6.25.2
ipython==8.15.0
ipywidgets==8.1.1
isodate==0.6.1
isoduration==20.11.0
isolate==0.12.2
isolate-proto==0.0.37
jaraco.classes==3.3.0
jedi==0.19.0
Jinja2==3.1.2
jmespath==1.0.1
joblib==1.1.1
json5==0.9.14
jsonpointer==2.4
jsonschema==4.19.0
jsonschema-specifications==2023.7.1
jupyter-events==0.7.0
jupyter-lsp==2.2.0
jupyter_client==8.3.1
jupyter_core==5.3.1
jupyter_server==2.7.3
jupyter_server_terminals==0.4.4
jupyterlab==4.0.5
jupyterlab-pygments==0.2.2
jupyterlab-widgets==3.0.9
jupyterlab_server==2.24.0
keras==2.13.1
keyring==23.13.1
kiwisolver==1.4.5
korean-lunar-calendar==0.3.1
leather==0.3.4
libclang==16.0.6
lightgbm==3.3.5
llvmlite==0.40.1
Logbook==1.5.3
Markdown==3.4.4
markdown-it-py==3.0.0
MarkupSafe==2.1.1
mashumaro==3.6
matplotlib==3.7.1
matplotlib-inline==0.1.6
mdurl==0.1.2
minimal-snowplow-tracker==0.0.2
mistune==3.0.1
monotonic==1.6
more-itertools==10.1.0
msgpack==1.0.5
multidict==6.0.4
mypy-boto3-athena==1.28.36
mypy-boto3-glue==1.28.36
mypy-boto3-lakeformation==1.28.36
mypy-boto3-sts==1.28.37
mysql-connector-python==8.0.21
nbclient==0.8.0
nbconvert==7.8.0
nbformat==5.9.2
nest-asyncio==1.5.7
networkx==2.8.8
notebook==7.0.3
notebook_shim==0.2.3
numba==0.57.1
numpy==1.23.4
numpydoc==1.1.0
oauthlib==3.2.2
openai==0.27.8
opentelemetry-api==1.20.0
opentelemetry-sdk==1.20.0
opentelemetry-semantic-conventions==0.41b0
opt-einsum==3.3.0
oscrypto==1.3.0
overrides==7.4.0
packaging==23.1
pandas==1.3.5
pandleau==0.4.1
pandocfilters==1.5.0
paramiko==3.1.0
parsedatetime==2.4
parso==0.8.3
pathspec==0.11.2
pexpect==4.8.0
pickleshare==0.7.5
Pillow==10.0.0
platformdirs==2.6.2
portalocker==2.7.0
posthog==1.4.9
prometheus-client==0.13.1
prompt-toolkit==3.0.39
proto-plus==1.22.3
protobuf==4.24.3
psutil==5.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
py4j==0.10.9.5
pyarrow==10.0.1
pyasn1==0.5.0
pyasn1-modules==0.3.0
PyAthena==3.0.7
pycparser==2.21
pycryptodomex==3.18.0
pydantic==1.10.12
Pygments==2.16.1
PyJWT==2.8.0
PyMeeus==0.5.12
PyMySQL==1.0.3
PyNaCl==1.5.0
pynndescent==0.5.10
pyOpenSSL==23.1.0
pyparsing==3.1.1
pyspark==3.3.1
python-dateutil==2.8.2
python-json-logger==2.0.7
python-slugify==8.0.1
pytimeparse==1.1.8
pytz==2023.3.post1
PyYAML==6.0.1
pyzmq==25.1.1
qgrid==1.3.1
referencing==0.30.2
regex==2022.10.31
requests==2.28.2
requests-oauthlib==1.3.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.5.2
rpds-py==0.10.3
rsa==4.9
s3fs==2023.4.0
s3transfer==0.6.2
scikit-learn==1.2.0
scipy==1.10.0
seaborn==0.11.0
Send2Trash==1.8.2
shap==0.41.0
six==1.16.0
sklearn-pandas==2.2.0
sklearn-pmml-model==1.0.1
sklearn2pmml==0.85.0
slicer==0.0.7
smart-open==6.4.0
sniffio==1.3.0
snowballstemmer==2.2.0
snowflake-connector-python==3.0.2
snowflake-sqlalchemy==1.4.7
soupsieve==2.5
Sphinx==7.2.5
sphinxcontrib-applehelp==1.0.7
sphinxcontrib-devhelp==1.0.5
sphinxcontrib-htmlhelp==2.0.4
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.6
sphinxcontrib-serializinghtml==1.1.9
SQLAlchemy==1.4.47
sqldf==0.4.2
sqlparams==5.1.0
sqlparse==0.4.3
sshtunnel==0.4.0
stack-data==0.6.2
statsd==3.3.0
structlog==22.3.0
tableauserverclient==0.17.0
tabulate==0.9.0
tblib==2.0.0
tenacity==8.2.3
tensorboard==2.13.0
tensorboard-data-server==0.7.1
tensorflow==2.13.0
tensorflow-estimator==2.13.0
tensorflow-hub==0.14.0
tensorflow-io-gcs-filesystem==0.34.0
tensorflow-text==2.13.0
termcolor==2.3.0
terminado==0.17.1
text-unidecode==1.3
threadpoolctl==3.2.0
tinycss2==1.2.1
tomli==2.0.1
top2vec==1.0.29
tornado==6.3.3
tqdm==4.66.1
traitlets==5.9.0
types-awscrt==0.19.1
types-python-dateutil==*********
types-s3transfer==0.6.2
typing_extensions==4.5.0
umap-learn==0.5.3
uri-template==1.3.0
uritemplate==4.1.1
urllib3==1.26.15
virtualenv==20.21.1
wcwidth==0.2.6
webcolors==1.13
webencodings==0.5.1
websocket-client==1.6.3
Werkzeug==2.3.6
widgetsnbextension==4.0.9
wordcloud==1.9.2
wrapt==1.15.0
xgboost==1.7.3
yarl==1.9.2
zipp==3.16.2
