kpi_reporting:
  outputs:
    target_with_fal:
      type: fal
      db_profile: default_target
      
    default_target:
      database: awsdatacatalog
      num_retries: 1
      region_name: "{{ env_var('AWS_REGION') }}"
      s3_staging_dir: "s3://aktana-bdp{{ env_var('REGION') }}-glue/dbt/{{ env_var('SCHEMA_SUFFIX') }}/{{ env_var('CUSTOMER') }}" 
      schema: "{{ env_var('SCHEMA_PREFIX') }}_{{ env_var('CUSTOMER') }}_{{ env_var('SCHEMA_SUFFIX') }}"
      type: athena
      work_group: primary
  target: default_target