import argparse
import jinja2
import yaml
import sys


def _get_cmdline_param():
    parser = argparse.ArgumentParser()

    # Add mandatory param
    parser.add_argument("--TARGET", required=True)
    parser.add_argument("--SCHEMA", required=True)
    parser.add_argument("--ENVIRONMENT", required=True)
    parser.add_argument("--REGION", required=True)
    parser.add_argument("--CUSTOMER", required=True)
    parser.add_argument("--AWS_REGION", required=True)

    args, unknown = parser.parse_known_args()

    arg_dict = {}
    for arg in vars(args):
        if getattr(args, arg):
            arg_dict[arg] = getattr(args, arg)

    # Process unknown params if any
    if unknown:
        for i in range(0, len(unknown), 2):
            key, value = unknown[i][2:], unknown[i + 1]
            arg_dict[key] = value

    print("Command line params:")
    print(arg_dict)
    return arg_dict

def _write_yaml_from_template(arg_dict, file_name):
    # Load jinja template
    with open(f'{file_name}_template.j2', 'r') as f:
        template = jinja2.Template(f.read())

    # Substitute and write to yaml file
    rendered = template.render(**arg_dict)
    print(f"{file_name}:")
    print(rendered)
    data = yaml.safe_load(rendered)
    with open(f'{file_name}.yml', 'w') as f:
        yaml.dump(data, f)

    print(f"Generated {file_name}.yml")


def main():
    # Read cmdline param
    arg_dict = _get_cmdline_param()
    
    # write dbt_project.yml
    _write_yaml_from_template(arg_dict, 'dbt_project')

    # write profiles.yml
    _write_yaml_from_template(arg_dict, 'profiles')


if __name__ == '__main__':
    main()
