
name: 'kpi_reporting'
version: '1.0.0'
config-version: 2

vars:
   # customer
   customer: 'xxx'

   # source environment
   src_env: 'prod'

   # region - eu, na, ap, cn
   region: 'xxx'

   # multicountry - true or false
   is_multicountry: false


profile: 'kpi_reporting'

query-comment:

model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"


models:
  kpi_reporting:
    
