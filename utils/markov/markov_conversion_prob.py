import logging.config
import time
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
from pyathena import connect
import pandas as pd
import awswrangler as wr
import getopt, sys, os

log_config = {
    "version":1,
    "root":{
        "handlers" : ["console", "file"],
        "level": "ERROR"
    },
    "handlers":{
        "console":{
            "formatter": "std_out",
            "class": "logging.StreamHandler",
            "level": "ERROR"
        },
        "file":{
            "formatter":"std_out",
            "class":"logging.FileHandler",
            "level":"ERROR",
            "filename":"all_messages.log"
        }
    },
    "formatters":{
        "std_out": {
            "format": "%(levelname)s : %(module)s : %(funcName)s : %(message)s",
        }
    },
}

logging.config.dictConfig(log_config)
logger = logging.getLogger("athena_python_examples")

AWS_ACCESS_KEY=os.environ['AWS_ACCESS_KEY_ID']
AWS_SECRET_KEY=os.environ['AWS_SECRET_ACCESS_KEY']
AWS_SESSION_TOKEN=os.environ['AWS_SESSION_TOKEN']

CUSTOMER = ""
AWS_REGION = ""
DEV_SUFFIX = ""
SCENARIOUIDS = "default,default2,default3,default4"
SCHEMA_NAME = "impact_"+CUSTOMER+"_satya"
S3_STAGING_DIR = ""

s3map={}
s3map["us-east-1"] =  "s3://aktana-bdpuseks-glue/"
s3map["eu-central-1"] =  "s3://aktana-bdpeueks-glue/"
s3map["ap-northeast-1"] =  "s3://aktana-bdpjpeks-glue/"

def get_s3staging(customer, awsregion, devsuffix):
    s3bucket = s3map[awsregion]
    s3bucket = s3bucket + "/" + "athena" + "/" +  "customer"
    return s3bucket;

def get_schema(customer, devsuffix):
    schema = ""
    if (devsuffix == ""):
       schema = customer + "_prod_" + "impact"
    else:
       schema = "impact_" + customer + "_" + devsuffix 

    return schema

def get_s3target(customer, awsregion, devsuffix):
    s3target = ""
    if (devsuffix == ""):
       s3target = "s3://aktana-bdp-" + customer + "/prod/dbt/impact"
    else:
       s3target = s3map[awsregion]
       s3target = s3target + "dbt" + "/" + devsuffix 

    return s3target


def get_transition_array(path):
  '''
    This function takes as input a user journey (string) where each state transition is marked by a >. 
    The output is an array that has an entry for each individual state transition.
  '''
  state_transition_array = path.split(">")
  initial_state = state_transition_array[0]
  
  state_transitions = []
  for state in state_transition_array[1:]:
    state_transitions.append(initial_state.strip()+' > '+state.strip())
    initial_state =  state
  
  return state_transitions


def convert_to_string_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    print("sub:" + substr)
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
       arr2.append(x.strip())

    return arr2

def convert_to_double_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
       arr2.append(float(x.strip()))

    return arr2

def get_transition_probability_graph(conn, msrscenariouid, removal_state = "null"):
  '''
  This function calculates a subset of the transition probability graph based on the state to exclude
      removal_state: channel that we want to exclude from our Transition Probability Matrix
  returns subset of the Transition Probability matrix as pandas Dataframe
  '''
  
  transition_probability_pandas_df = None
  
  # Get the transition probability graph without any states excluded if the removal_state is null
  if removal_state == "null":
    transition_probability_pandas_df =  pd.read_sql_query('''select
        trim(start_state) as start_state,
        array_agg(end_state) as next_stages,
        array_agg(transition_probability) as next_stage_transition_probabilities
      from
        msrscenario_transition_matrix 
      where msrscenariouid = \''''+msrscenariouid+'''\'
      group by
        start_state having cardinality(array_agg(end_state)) > 0''', conn);
    
  # Otherwise, get the transition probability graph with the specified channel excluded/removed
  else:
    transition_probability_pandas_df = pd.read_sql_query('''select
      sub1.start_state as start_state,
      array_agg(sub1.end_state) as next_stages,
      array_agg(transition_probability) as next_stage_transition_probabilities
      from
      (
        select
          trim(start_state) as start_state,
          case
            when end_state = \''''+removal_state+'''\' then 'Null'
            else end_state
          end as end_state,
          transition_probability
        from
          msrscenario_transition_matrix
        where  msrscenariouid = \''''+msrscenariouid+'''\' and
          start_state != \''''+removal_state+'''\'
      ) sub1  group by sub1.start_state  having cardinality(array_agg(sub1.end_state)) > 0 ''', conn)

  transition_probability_pandas_df["next_stages"] = transition_probability_pandas_df["next_stages"].map(convert_to_string_array)
  transition_probability_pandas_df["next_stage_transition_probabilities"] = transition_probability_pandas_df["next_stage_transition_probabilities"].map(convert_to_double_array)

  print(transition_probability_pandas_df)
 
  return transition_probability_pandas_df


def calculate_conversion_probability(transition_probability_pandas_df, calculated_state_conversion_probabilities, visited_states, current_state="Start"):
  '''
  This function calculates total conversion probability based on a subset of the transition probability graph
    transition_probability_pandas_df: This is a Dataframe that maps the current state to all probable next stages along with their transition probability
    removal_state: the channel that we want to exclude from our Transition Probability Matrix
    visited_states: set that keeps track of the states that have been visited thus far in our state transition graph.
    current_state: by default the start state for the state transition graph is Start state
  returns conversion probability of current state/channel 
  '''
 
  #If the customer journey ends with conversion return 1
  if current_state=="Conversion":
    return 1.0
  
  #If the customer journey ends without conversion, or if we land on the same state again, return 0.
  #Note: this step will mitigate looping on a state in the event that a customer path contains a transition from a channel to that same channel.
  elif (current_state=="Null") or (current_state in visited_states):
    return 0.0
  
  #Get the conversion probability of the state if its already calculated
  elif current_state in calculated_state_conversion_probabilities.keys():
    return calculated_state_conversion_probabilities[current_state]
  
  else:
  #Calculate the conversion probability of the new current state
    #Add current_state to visited_states
    visited_states.add(current_state)

    print("current_state:"+current_state)
    
    #Get all of the transition probabilities from the current state to all of the possible next states
    current_state_transition_df = transition_probability_pandas_df.loc[transition_probability_pandas_df.start_state==current_state]
    
    #Get the next states and the corresponding transition probabilities as a list.
    if (len(current_state_transition_df.next_stages) == 0):
       return 0.0

    next_states = current_state_transition_df.next_stages.to_list()[0]
    next_states_transition_probab = current_state_transition_df.next_stage_transition_probabilities.to_list()[0]

    #print("all next states:" + next_states)
    #print("all next states type:" + str(type(next_states)))

    #print("all_next_states_transition_probab:" + next_states_transition_probab)
    #print("all_next_states_transition_probab type:" + str(type(next_states_transition_probab)))
    
    #This will hold the total conversion probability of each of the states that are candidates to be visited next from the current state.
    current_state_conversion_probability_arr = []
    
    #Call this function recursively until all states in next_states have been incorporated into the total conversion probability
    import copy
    #Loop over the list of next states and their transition probabilities recursively
    for next_state, next_state_tx_probability in zip(next_states, next_states_transition_probab):
      #print(next_states)
      #print(next_states_transition_probab)
      #print("next:" + str(next_state))
      #print("next_prob:" + str(next_state_tx_probability))
      current_state_conversion_probability_arr.append(next_state_tx_probability * calculate_conversion_probability(transition_probability_pandas_df, calculated_state_conversion_probabilities, copy.deepcopy(visited_states), next_state))
    
    #Sum the total conversion probabilities we calculated above to get the conversion probability of the current state.
    #Add the conversion probability of the current state to our calculated_state_conversion_probabilities dictionary.
    calculated_state_conversion_probabilities[current_state] =  sum(current_state_conversion_probability_arr)
    
    #Return the calculated conversion probability of the current state.
    return calculated_state_conversion_probabilities[current_state]



def main():
    # parse arugments

    opts, args = getopt.getopt(sys.argv[1:], "", ["customer=","awsregion=","devsuffix=","scenariouids="])
    customer = CUSTOMER
    awsregion = AWS_REGION
    devsuffix = ""
    scenariouids = "default1,default2,default3,default4"
    s3staging = ""
    srcschema = ""
    s3target = ""
    tgtschema = ""

    for opt in opts:
        print (opt[0])
        if opt[0] == "--customer":
            customer = opt[1]
        elif opt[0] == "--awsregion":
            awsregion = opt[1]
        elif opt[0] == "--scenariouids":
            scenariouids = opt[1]
        elif opt[0] == "--devsuffix":
            devsuffix =  opt[1]

    if (devsuffix == "prod"):
       devsuffix = ""

    print ('CUSTOMER    :', customer)
    print ('AWSREGION   :', awsregion)
    print ('SCENARIOUIDS:', scenariouids)
    print ('DEVSUFFIX   :', devsuffix)

    s3staging = get_s3staging(customer, awsregion, devsuffix)
    s3target  = get_s3target(customer, awsregion, devsuffix)
    srcschema = get_schema(customer, devsuffix)
    tgtschema = get_schema(customer, devsuffix)

    print ('S3STAGING   :', s3staging)
    print ('S3TARGET    :', s3target)
    print ('SRCSCHEMA   :', srcschema)
    print ('TGTSCHEMA   :', tgtschema)


    conn = connect(aws_access_key_id=AWS_ACCESS_KEY,
                 aws_secret_access_key=AWS_SECRET_KEY,
                 aws_session_token=AWS_SESSION_TOKEN,
                 s3_staging_dir=s3staging,
                 region_name=awsregion,
                 schema_name=srcschema,
                 );

    start_time = time.time()

    SCENARIOUID_arr = scenariouids.split(',')

    all_scenarios_df = pd.DataFrame()
    all_scenarios_conv_df = pd.DataFrame()

    for SCENARIOUID in SCENARIOUID_arr:
        print(SCENARIOUID)
        transition_probability_pandas_df = get_transition_probability_graph(conn, SCENARIOUID);

        transition_probability_pandas_df.to_csv("transition_prob.csv")

        total_conversion_probability = calculate_conversion_probability(transition_probability_pandas_df, {}, visited_states=set(), current_state="Start");

        print(total_conversion_probability)

        total_conversion_pandas_df = pd.DataFrame({'msrscenariouid': [SCENARIOUID], 'conversion_probability': [total_conversion_probability]})

        if (all_scenarios_conv_df.empty):
           all_scenarios_conv_df = total_conversion_pandas_df
        else:
           frames = [all_scenarios_conv_df, total_conversion_pandas_df]
           all_scenarios_conv_df = pd.concat(frames)

        removal_effect_per_channel = {}
        for channel in transition_probability_pandas_df.start_state.to_list():
           if channel!="Start":
               transition_probability_subset_pandas_df = get_transition_probability_graph(conn, SCENARIOUID, removal_state=channel)
               new_conversion_probability =  calculate_conversion_probability(transition_probability_subset_pandas_df, {}, visited_states=set(), current_state="Start")
               if (total_conversion_probability > 0.0):
                   removal_effect_per_channel[channel] = round(((total_conversion_probability-new_conversion_probability)/total_conversion_probability), 2)
               else:
                   removal_effect_per_channel[channel] = 1.0

        conversion_attribution={}
 
        for channel in removal_effect_per_channel.keys():
            if (sum(removal_effect_per_channel.values()) > 0.0):
                conversion_attribution[channel] = round(removal_effect_per_channel[channel] / sum(removal_effect_per_channel.values()), 2)
            else:
                conversion_attribution[channel] = 0.0

 
        channels = list(conversion_attribution.keys())
        conversions = list(conversion_attribution.values())

        conversion_pandas_df= pd.DataFrame({'attribution_model': 
                                             ['markov_chain' for _ in range(len(channels))], 
                                            'channel':channels, 
                                            'attribution_percent': conversions})

        conversion_pandas_df['msrscenariouid'] = SCENARIOUID;

        conversion_pandas_df.to_csv("conversion_prob_"+SCENARIOUID+".csv")

        if (all_scenarios_df.empty):
            all_scenarios_df = conversion_pandas_df
        else:
            frames = [all_scenarios_df, conversion_pandas_df]
            all_scenarios_df = pd.concat(frames)

    print(all_scenarios_df)
    print(s3target)
    print(tgtschema)

        # Storing data on Data Lake
    wr.s3.to_parquet(
    df=all_scenarios_df, mode="overwrite",
         path=s3target+"/Attribution_Markov/", dataset=True, database=tgtschema, partition_cols=['msrscenariouid'], table="msrscenario_attribution_markov");
 
    wr.s3.to_parquet(
    df=all_scenarios_conv_df, mode="overwrite",
        path=s3target+"/Conversion_Probability/", dataset=True, database=tgtschema, partition_cols=['msrscenariouid'], table="msrscenario_conversion_probability");


if __name__ == "__main__":
    main()
