SELECT * FROM "genzymeus_prod_impact"."msrscenario_event_sequence" limit 10;

SELECT * FROM "genzymeus_prod_current"."msrscenario_conversion_probability" limit 10;

select * from  "genzymeus_prod_current".msrscenario_attribution_markov where attribution_percent > 0 order by msrscenariouid, attribution_percent desc;


select msrscenariouid, conversion, count(*) 
from msrscenario_event_sequence
group by msrscenariouid, conversion
order by msrscenariouid, conversion;

select msrscenariouid, conversion, count(*)
from msrscenario_markov_transitions
group by msrscenariouid, conversion
order by msrscenariouid, conversion;

select msrscenariouid, count(*) 
from msrscenario_transition_matrix
group by msrscenariouid;

-- smaller number of transitions - higher conversion probability
