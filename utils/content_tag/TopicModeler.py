import pandas as pd
import numpy as np
from top2vec import Top2Vec
import openai
import uuid
from io import StringIO


class TopicModeler:
    def __init__(self, doctype, productuid, messagedf, min_count=10):
        self.doctype = doctype
        self.productuid = productuid
        self.messagedf = messagedf

        hdbscan_args = {'min_cluster_size': 2,
                        'metric': 'euclidean',
                        'cluster_selection_method': 'eom'}

        # self.model = Top2Vec(messagedf['messagecontent'].values.astype(str), embedding_model='universal-sentence-encoder',              topic_merge_delta=0.00001, verbose=True, min_count=min_count, ngram_vocab=True, speed="deep-learn", ngram_vocab_args={"connector_words": "phrases.ENGLISH_CONNECTOR_WORDS"}, document_ids=messagedf['messageproductuid'].values.astype(str), hdbscan_args=hdbscan_args, split_documents=True)
        self.model = Top2Vec(messagedf['messagecontent'].values.astype(str),
                             embedding_model='universal-sentence-encoder-multilingual', topic_merge_delta=0.00001,
                             verbose=True, min_count=min_count, ngram_vocab=True, speed="deep-learn",
                             ngram_vocab_args={"connector_words": "phrases.ENGLISH_CONNECTOR_WORDS"},
                             document_ids=messagedf['messageproductuid'].values.astype(str), hdbscan_args=hdbscan_args,
                             split_documents=True)

    def get_model(self):
        return self.model

    def get_messagedf(self):
        return self.messagedf

    def get_vocabdf(self):
        return self.vocabdf

    def get_topicdf(self):
        return self.topicdf

    def get_topicworddf(self):
        return self.topicworddf

    def enhance_messagedf_with_model_output(self):
        s1 = pd.Series(self.model.documents)
        s2 = pd.Series(self.model.doc_top)
        s3 = pd.Series(self.model.doc_dist)

        self.messagedf['doc'] = s1
        self.messagedf['doctopic'] = s2
        self.messagedf['doctopicscore'] = s3

        self.messagedf = self.messagedf.set_index('messageproductuid')
        return self.messagedf

    def build_vocabdf_with_model_output(self):
        v1 = pd.Series(self.model.vocab)
        self.vocabdf = pd.DataFrame(data={'word': v1});
        self.vocabdf['document_type'] = self.doctype
        self.vocabdf['productuid'] = self.productuid
        return self.vocabdf;

    def build_topicdf_topicworddf_with_model_output(self):
        cols = [];
        wordcols = [];

        idx = 0;
        for x in self.model.topic_words:
            dict = {}
            words = '';
            dict['topic'] = idx
            for w in x:
                words = words + w + ",";
            dict['words'] = words;

            scores = ''

            j = 0
            for y in self.model.topic_word_scores[idx]:
                scores = scores + str(y) + ",";
                worddict = {}
                worddict['topic'] = idx
                worddict['word'] = x[j]
                worddict['score'] = y
                wordcols.append(worddict)
                j = j + 1

            dict['scores'] = scores;
            dict['size'] = self.model.topic_sizes[idx]

            cols.append(dict)
            idx = idx + 1

        self.topicdf = pd.DataFrame(cols)
        self.topicworddf = pd.DataFrame(wordcols)

        self.topicdf['document_type'] = self.doctype
        self.topicdf['productuid'] = self.productuid

        self.topicworddf['document_type'] = self.doctype
        self.topicworddf['productuid'] = self.productuid

        return (self.topicdf, self.topicworddf);

    def enhance_topicdf_with_llm_output(self):
        # list models
        models = openai.Model.list()
        # print the first model's id
        print(models.data[0].id)

        topicdescription = []

        inidx = 0
        for index, row in self.topicdf.iterrows():
            prompt = "You are a highly skilled agent that generates topic. Generate a topic that is around 10 words long. suggest a topic name for documents that contain the following key words:  " + \
                     row['words']
            # print(prompt)
            chat_completion = openai.ChatCompletion.create(model="gpt-4o-mini",
                                                           messages=[{"role": "user", "content": prompt}])
            print(chat_completion.choices[0].message.content)
            topicdescription.append(chat_completion.choices[0].message.content)

        v1 = pd.Series(topicdescription)
        self.topicdf['description'] = v1

        return self.topicdf;

    def generate_tags_for_topicdf_keywords(self):
        # list models
        models = openai.Model.list()
        # print the first model's id
        print(models.data[0].id)

        topicdescription = []

        inidx = 0
        # all_words = ""
        # for index, row in self.topicdf.iterrows():
        #    all_words = all_words + row['words']
        all_words_arr = self.topicworddf['word'].unique()
        all_words = ",".join(all_words_arr)

        prompt_prefix = "Please categorize the following comma separated phrases into 10 semantically similar categories. It is extremely necessary to come up with more than 7 categories. The comma separated phrases are: "
        prompt_suffix = ". First column should be a meaningful higher level category tag in 'Category' Column under which the category description would fall, Second Column should be category description like this 'Neurologic Toxicity Evaluation in Clinical and Subclinical Patients: A Comprehensive Review' under 'Name' column (It is necessary to have a 7-10 words description) and third column should have the clustered words of that particular category. The categorization should be done based on the semantic similarity of the phrases. Then finally provide the mapping in csv format with columns 'Category' 'Name' and 'Phrases' and semicolon as the delimiter, use comma to separate words in 'Phrases' category. There should be exactly 3 columns in this csv.  I want you to only reply with the csv output only, and nothing else. do not write explanations. do not type commands unless I instruct you to do so."
        prompt = prompt_prefix + all_words + prompt_suffix
        # print(prompt)
        chat_completion = openai.ChatCompletion.create(model="gpt-4o-mini", messages=[
            {"role": "system", "content": "you are a helpful assistant"}, {"role": "user", "content": prompt}])
        csvString = chat_completion.choices[0].message.content
        # drop backticks from gpt output
        csvString = csvString.replace("```", "")
        print(csvString)
        # df = pd.DataFrame()
        csvStringIO = StringIO(csvString)
        df = pd.read_csv(csvStringIO, sep=";", header=0)

        return df

    def get_messageset_message(self, gconst, doctype, productuid, productname, intopicdf,
                               msgset_rowoffset=0, msgsetmsg_rowoffset=0, docs_per_topic=100, is_multi_product=False):
        h_topic = []
        h_category = []
        h_topicuid = []
        h_doc_type = []
        h_productuid = []
        h_productname = []
        h_name = []
        t_topicuid = []
        t_topicname = []
        t_doc = []
        t_doc_score = []
        t_doc_id = []
        t_doc_rank = []
        t_doc_messageuid = []
        t_doc_recentflag = []

        inidx = 0
        docactualidx = 0

        num_docs = min(len(self.model.doc_top), docs_per_topic)
        print(f"[INFO] Number of documents per tag: {num_docs}")
        intopicdf.info()
        print("Intopic DF", intopicdf)

        for index, row in intopicdf.iterrows():
            # Check if 'Name' and 'Category' exist in the row
            if 'Name' not in row or 'Category' not in row:
                print(f"[WARNING] Skipping row with missing 'Name' or 'Category': {row}")
                continue

            # Retrieve topic name and category
            topic_name = row['Name']
            category_name = row['Category']
            print(f"[INFO] Processing Topic: {topic_name} | Category: {category_name}")

            # Query documents for this topic
            if topic_name:
                docs, doc_scores, doc_ids = self.model.query_documents(topic_name, num_docs, ef=100, tokenizer=None)
                print(f"[INFO] Retrieved {len(doc_ids)} documents for Topic: {topic_name}")



            if is_multi_product:
                print("multi process started")
                # For multi-product, generate a new topic_uid for each topic_name + productuid
                filtered_df = self.messagedf[self.messagedf.index.isin(doc_ids)]
                print("filtered df", filtered_df)

                productuid_df_dict = {}

                # Group by productuid and document_type
                for productuid, product_group_df in filtered_df.groupby(['document_type', 'productuid','productname']):
                    productuid_df_dict[productuid] = product_group_df.reset_index()



                print(f"[INFO] Split documents into {len(productuid_df_dict)} groups by product")
                print("product keys", productuid_df_dict.keys())
                print("product dict", productuid_df_dict)

                # Generate a new topic_uid for each topic_name + productuid
                for product_key in productuid_df_dict.keys():
                    product_doctype, product_productuid,product_productname = product_key

                    product_group_df = productuid_df_dict[product_key]

                    print("product df", product_group_df)
                    print("doctype ", product_doctype)
                    print("productproductuid 218", product_productuid)
                    print("Name",product_productname)
                    print("product df ", product_group_df)

                    topic_uuid = str(uuid.uuid4())  # Always generate new UUID for each topic_name + productuid
                    h_topicuid.append(topic_uuid)
                    h_topic.append(topic_name)
                    h_category.append(category_name)
                    h_doc_type.append(product_doctype)
                    puid = '=VLOOKUP(E' + str(
                        msgset_rowoffset + inidx + 2) + ',' + gconst.get_products_vlookup_cellrange() + ',2,false)'
                    h_productuid.append(puid)
                    h_productname.append(product_productname)
                    topic_full_name = f"{product_doctype} - {product_productname} - {topic_name}"
                    h_name.append(topic_full_name)


                    docidx = 0
                    docrank = 0

                    for _, r in product_group_df.iterrows():
                        print("Row",r)

                        doc_id = r['messageproductuid']
                        tdoctype = r['document_type']
                        tproductuid = r['productuid']
                        doctopicscore=r['doctopicscore']



                        if (product_doctype == tdoctype) & (product_productuid == tproductuid):
                            tuid = '=VLOOKUP(A' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_geninputtopic_vlookup_cellrange() + ',2,false)'
                            print("uid", tuid)

                            t_topicuid.append(tuid)
                            t_topicname.append(topic_full_name)
                            t_doc_id.append(doc_id)

                            doctext = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',6,false)'

                            t_doc.append(doctext)
                            print("docscore", doc_scores[docidx])
                            t_doc_score.append(doc_scores[docidx])
                            print("doc score", doc_scores[docidx])
                            t_doc_rank.append(docrank)

                            docuid = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',1,false)'
                            t_doc_messageuid.append(docuid)

                            print("docuid", docuid)
                            print("list of docids", t_doc_id)

                            docrecentflag = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',16,false)'
                            t_doc_recentflag.append(docrecentflag)




                            docrank += 1
                            docactualidx += 1

                        docidx += 1
                        inidx += 1
            else:
                print("single process started")
                # For single-product, generate only one unique topic_uid per topic_name + productuid
                topic_uuid = str(uuid.uuid4())  # Generate a new unique UUID for each row in single product case

                h_topicuid.append(topic_uuid)
                print("topid",h_topicuid)
                h_topic.append(topic_name)
                print("topic_name",topic_name,h_topic)
                h_category.append(category_name)
                print("catogory",h_category)
                h_doc_type.append(doctype)
                print("doctype",h_doc_type)
                h_productuid.append(productuid)
                print("productuid",h_productuid)
                h_productname.append(productname)
                print("product_name",h_productname)
                topic_full_name = f"{doctype} - {productname} - {topic_name}"
                h_name.append(topic_full_name)
                print("topic name",topic_full_name,"->",h_name)

                docidx = 0
                docrank = 0
                for doc_id in doc_ids:
                    if doc_id in self.messagedf.index:
                        row2 = self.messagedf.loc[[doc_id]]
                        tdoctype = row2['document_type'].values[0]
                        tproductuid = row2['productuid'].values[0]

                        if (doctype == tdoctype) & (productuid == tproductuid):
                            tuid = '=VLOOKUP(A' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_geninputtopic_vlookup_cellrange() + ',2,false)'
                            t_topicuid.append(tuid)
                            t_topicname.append(topic_full_name)
                            t_doc_id.append(doc_id)

                            doctext = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',6,false)'
                            t_doc.append(doctext)
                            t_doc_score.append(doc_scores[docidx])
                            t_doc_rank.append(docrank)

                            docuid = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',1,false)'
                            t_doc_messageuid.append(docuid)

                            docrecentflag = '=VLOOKUP(C' + str(
                                msgsetmsg_rowoffset + docactualidx + 2) + ',' + gconst.get_messages_vlookup_cellrange() + ',16,false)'
                            t_doc_recentflag.append(docrecentflag)

                            docrank += 1
                            docactualidx += 1

                        docidx += 1
                inidx += 1

        print(f"[INFO] Constructing output dataframes with {len(h_topic)} topics and {len(t_topicuid)} documents")

        # Construct the output dataframes
        intopic_df = pd.DataFrame({
            'topic_name': h_name,
            'topic_uid': h_topicuid,
            'tag': h_topic,
            'category': h_category,
            'document_type': h_doc_type,
            'productname': h_productname,
            'productuid': h_productuid
        })

        intopic_docs_df = pd.DataFrame({
            'topic_name': t_topicname,
            'topic_uid': t_topicuid,
            'messageproductuid': t_doc_id,
            'doc': t_doc,
            'doc_score': t_doc_score,
            'doc_uid': t_doc_messageuid,
            'doc_rank': t_doc_rank,
            'doc_recentflag': t_doc_recentflag
        })

        intopic_df['isactive'] = 'TRUE'
        intopic_docs_df['isactive'] = 'TRUE'

        intopic_df['isoverride'] = 'FALSE'
        intopic_docs_df['isoverride'] = 'FALSE'

        return intopic_df, intopic_docs_df


