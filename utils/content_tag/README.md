
1) https://denisluiz.medium.com/python-with-google-sheets-service-account-step-by-step-8f74c26ed28e - create a json file content-tag-*.json in current directory

2) https://platform.openai.com create OPENAPI key and set it as an environment variable  export OPENAI_API_KEY='xxxxx'

3) for top2vec set environment variable - export TFHUB_CACHE_DIR=$HOME/.cache/tfhub_modules

4) update requirements.txt
      - top2vec
      - top2vec[sentence_encoders]
      - gspread
      - gspread-pandas
      - gspread-formatting
      - google-auth
      - google-auth-oauthlib
      - google-auth-httplib2
      - google-api-python-client
      - beautifulsoup4
      - openai


