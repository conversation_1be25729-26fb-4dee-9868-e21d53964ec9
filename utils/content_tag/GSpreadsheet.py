
import os
from gspread_pandas import Spread, Client
from apiclient import discovery
from google.oauth2 import service_account


class GSpreadsheet:
    def __init__(self, secretfile):
        self.scope = ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/drive.file', 'https://www.googleapis.com/auth/spreadsheets']
        self.secret_file = secretfile
        self.creds = service_account.Credentials.from_service_account_file(self.secret_file, scopes=self.scope)
        self.gclient = Client(creds=self.creds)

    def open(self, title):
        return self.gclient.open(title)

    def openspread(self, url):
        self.spread = Spread(url, creds=self.creds)

        return self.spread 

    def sheet_to_df(self, sheetname):
        df = self.spread.sheet_to_df(index=0,sheet=sheetname)
        return df

    def df_to_sheet(self, sheetname, df, replacesheet=True, addindex=True):
        sheet = self.spread.find_sheet(sheetname)
        if (sheet == None):
           sheet = self.spread.create_sheet(sheetname)
        self.spread.df_to_sheet(df, sheet=sheetname, replace=replacesheet, index=addindex)
        return

    def find_sheet(self, sheetname, create_sheet=True):
        sheet = self.spread.find_sheet(sheetname)
        if ((sheet == None) & (create_sheet == True)):
           sheet = self.spread.create_sheet(sheetname)
        return sheet


    def save(self, ssname, wsname, data):
        sh = self.gclient.open(ssname)
        wshlist = sh.worksheets();

    def save(self, ssname, wsname, data):
        sh = self.gclient.open(ssname)
        wshlist = sh.worksheets();
        for wsh in wshlist:
            if (wsh.title == wsname):
               sh.values_clear(wsname + "!A2:O10000")

        wsh = sh.worksheet(wsname)
        wsh.update('A1', data, raw=False)

    def save_ss_with_fields(self, ssname, wsname, fields, data):
        insert_vals = []
        insert_vals.append(fields)
        for e in data:
             insert_data = []
             for k in range(0,len(fields)):
                 insert_data.append(str(e[k]))
             insert_vals.append(insert_data)

        self.save(ssname, wsname, insert_vals)
