import logging.config
import time
# import httplib2
import os
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
from pyathena import connect
import pandas as pd
import numpy as np
import awswrangler as wr
import getopt, sys, os
from top2vec import Top2Vec
import boto3

from utils.content_tag.TopicModeler import TopicModeler
# from GSpreadsheet import GSpreadsheet

# from gspread_formatting import *

from bs4 import BeautifulSoup

import openai

from gensim.parsing.preprocessing import remove_stopwords

log_config = {
    "version": 1,
    "root": {
        "handlers": ["console", "file"],
        "level": "ERROR"
    },
    "handlers": {
        "console": {
            "formatter": "std_out",
            "class": "logging.StreamHandler",
            "level": "ERROR"
        },
        "file": {
            "formatter": "std_out",
            "class": "logging.FileHandler",
            "level": "ERROR",
            "filename": "all_messages.log"
        }
    },
    "formatters": {
        "std_out": {
            "format": "%(levelname)s : %(module)s : %(funcName)s : %(message)s",
        }
    },
}

logging.config.dictConfig(log_config)
logger = logging.getLogger("athena_python_examples")

CUSTOMER = ""
AWS_REGION = ""
DEV_SUFFIX = ""
SCENARIOUIDS = "default,default2,default3,default4"
SCHEMA_NAME = "impact_" + CUSTOMER + "_satya"
S3_STAGING_DIR = ""

AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_SESSION_TOKEN = os.environ.get('AWS_SESSION_TOKEN')


# s3map={}
# s3map["us-east-1"] =  "s3://aktana-bdpuseks-glue/"
# s3map["eu-central-1"] =  "s3://aktana-bdpeueks-glue/"
# s3map["ap-northeast-1"] =  "s3://aktana-bdpjpeks-glue/"


class SpreadsheetConstants:
    def __init__(self):
        self.products_vlookup_cellrange = "products!$B$2:$C$1000"
        self.productname_cellrange = "=products!$B$2:$B$20"
        self.tags_cellrange = "=tags!$A$2:$A$100"
        self.geninputtopic_vlookup_cellrange = "tagmsgset!$A$2:$B$1000"
        self.geninputtopic_cellrange = "=tagmsgset!$A$2:$A$1000"
        self.messageidx_cellrange = "=messages!$A$2:$A$10000"
        self.messages_vlookup_cellrange = "messages!$A$2:$Q$10000"

    def get_products_vlookup_cellrange(self):
        return self.products_vlookup_cellrange;

    def get_productname_cellrange(self, length):
        value = self.productname_cellrange.replace("20", str(length + 1))
        return value;

    def get_tags_cellrange(self, length):
        value = self.tags_cellrange.replace("100", str(length + 1))
        return value;

    def get_geninputtopic_vlookup_cellrange(self):
        return self.geninputtopic_vlookup_cellrange;

    def get_geninputtopic_cellrange(self, length):
        value = self.geninputtopic_cellrange.replace("1000", str(length + 1))
        return value;

    def get_messageidx_cellrange(self, length):
        value = self.messageidx_cellrange.replace("10000", str(length + 1))
        return value;

    def get_messages_vlookup_cellrange(self):
        return self.messages_vlookup_cellrange;


def get_s3staging(customer, region, devsuffix):
    s3_staging_dir = "s3://aktana-bdp" + region + "-glue/athena/" + customer + '/' + devsuffix
    return s3_staging_dir


def get_schema(customer, suffix):
    schema = "impact_" + customer + "_" + suffix

    return schema


def get_s3target(customer, region, suffix):
    if suffix == 'prod':
        s3target = f"s3://aktana-bdp-{customer}/prod/dbt/impact"
    else:

        s3target = f"s3://aktana-bdp{region}-glue/dbt/{customer}/{suffix}"

    return s3target


def createList(r1, r2):
    return [item for item in range(r1, r2 + 1)]


def get_transition_array(path):
    '''
      This function takes as input a user journey (string) where each state transition is marked by a >.
      The output is an array that has an entry for each individual state transition.
    '''
    state_transition_array = path.split(">")
    initial_state = state_transition_array[0]

    state_transitions = []
    for state in state_transition_array[1:]:
        state_transitions.append(initial_state.strip() + ' > ' + state.strip())
        initial_state = state

    return state_transitions


def convert_to_string_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    print("sub:" + substr)
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
        arr2.append(x.strip())

    return arr2


def convert_to_double_array(str):
    # remove the first [ and last ]
    substr = str[1:-1]
    # convert csv to string array
    arr = substr.split(',')
    arr2 = []
    for x in arr:
        arr2.append(float(x.strip()))

    return arr2


def get_table(conn, sql):
    df = pd.read_sql_query(sql, conn)
    return df


def convert_to_csv_string(arr):
    s = ''
    for i in range(0, len(arr)):
        s = s + str(arr[i]) + ','

    return s


def convert_2darr_to_csv_string(arr):
    s = ''
    for i in range(0, len(arr)):
        for j in range(0, len(i)):
            s = s + str(arr[i][j]) + ','
        s = s + '|'
    return s


def prepare_message_df(message_df):
    inidx = 0
    messagecontentstr = []
    messagerecentflag = []
    messageid = []
    messageproductuid = []
    docmap = {}

    docidx = 0
    for index, row in message_df.iterrows():
        if row['messagecontent'] == '' or not BeautifulSoup(row['messagecontent'], 'html.parser').body:
            text = str(row['messagedescription']) + str(row['messagecontent'])
            if (text == None):
                text = ''
            text = remove_stopwords(text)
            messagecontentstr.append(text)
        else:
            soup = BeautifulSoup(row['messagecontent'], 'html.parser')
            for script in soup(["script", "style"]):
                script.extract()  # rip it out
            text = soup.body.get_text(separator=' ')
            # break into lines and remove leading and trailing space on each
            lines = (line.strip() for line in text.splitlines())
            # break multi-headlines into a line each
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            # drop blank lines
            text = '\n'.join(chunk for chunk in chunks if chunk)
            text = str(row['messagedescription']) + '\n' + text
            text = remove_stopwords(text)

            messagecontentstr.append(text)

        recent_count = row['current_month_count'] + row['month_1_count'] + row['month_2_count']
        if (recent_count > 0):
            messagerecentflag.append(1)
        else:
            messagerecentflag.append(0)

        messageid.append(docidx)

        # print(row['cms_messageuid']);
        # print(row['productuid']);
        messageproduct = row['cms_messageuid'] + "-" + row['productuid']
        messageproductuid.append(messageproduct)

        docidx = docidx + 1

    d1 = pd.Series(messagecontentstr)
    d2 = pd.Series(messagerecentflag)
    d3 = pd.Series(messageid)
    d4 = pd.Series(messageproductuid)
    message_df['messagecontent'] = d1
    message_df['messagerecentflag'] = d2
    message_df['messageid'] = d3
    message_df['messageproductuid'] = d4

    return message_df


def prepare_default_tag_df(customer):
    tag_df = pd.DataFrame()

    tagstr = []
    tagstr.append("Patient Resources")
    tagstr.append("Prescribing Information")
    tagstr.append("Brochures Forms")
    tagstr.append("Clinical Studies and Research")
    tagstr.append("Healthcare and Medical Organizations")
    tagstr.append("Patient Profiles")
    tagstr.append("Notice and Privacy")

    if (customer == 'genzymeus'):
        tagstr.append('Asthma Treatment')
        tagstr.append('Pulmonary Symptoms')
        tagstr.append('TCS Pediatric')
        tagstr.append('Urticaria rash')
        tagstr.append('Nasal Polyposis')
        tagstr.append('POA')

    if (customer == 'novartisuk'):
        tagstr.append('Statin Usage')
        tagstr.append('Ankylosing Spondylytis')
        tagstr.append('Immunoglobulin Levels')
        tagstr.append('Multiple Scelerosis')
        tagstr.append('Treatment of Psoriatic Arthritis')

    d1 = pd.Series(tagstr)
    tag_df['tag'] = d1

    return tag_df


# def save_result_to_gss(gss, product_df, all_vocabdf, all_topicdf, all_topicworddf, all_messagedf, all_inmsgsetdf, all_inmsgsetmsgdf, all_categoriesdf):
#     gss.df_to_sheet('products', product_df)
#
#     gss.df_to_sheet('vocab', all_vocabdf)
#     gss.df_to_sheet('generated_topics', all_topicdf)
#     gss.df_to_sheet('generated_topic_words', all_topicworddf)
#     gss.df_to_sheet('generated_categories', all_categoriesdf)
#     gss.df_to_sheet('messages', all_messagedf)
#
#     gss.df_to_sheet('tagmsgset', all_inmsgsetdf, addindex=False)
#     gss.df_to_sheet('tagmsgsetmsg', all_inmsgsetmsgdf, addindex=False)
#
#     return

def save_result_to_s3(s3target, tgtschema, all_inmsgsetdf, all_inmsgsetmsgdf, all_vocabdf, all_topicdf, all_topicworddf,
                      all_messagedf, all_categoriesdf, all_tags):
    # Storing data on Data Lake
    wr.s3.to_parquet(df=all_vocabdf, mode="overwrite",
                     path=s3target + "/CTAG_VOCAB/", dataset=True, database=tgtschema, table="ctag_out_vocab")

    wr.s3.to_parquet(df=all_topicdf, mode="overwrite",
                     path=s3target + "/CTAG_TOPIC/", dataset=True, database=tgtschema, table="ctag_out_topic")

    wr.s3.to_parquet(df=all_topicworddf, mode="overwrite",
                     path=s3target + "/CTAG_TOPICWORD/", dataset=True, database=tgtschema, table="ctag_out_topicword")

    wr.s3.to_parquet(df=all_messagedf, mode="overwrite",
                     path=s3target + "/CTAG_MESSAGE/", dataset=True, database=tgtschema, table="ctag_out_message",
                     dtype={'messagedescription': 'string'})

    # Not write LLM generated tag for now
    wr.s3.to_parquet(df=all_categoriesdf, mode="overwrite",
                     path=s3target + "/CTAG_GENTAG/", dataset=True, database=tgtschema, table="ctag_out_generated_tag")

    wr.s3.to_parquet(df=all_inmsgsetdf, mode="overwrite",
                     path=s3target + "/CTAG_MESSAGESET/", dataset=True, database=tgtschema, table="ctag_out_messageset")

    wr.s3.to_parquet(df=all_inmsgsetmsgdf, mode="overwrite",
                     path=s3target + "/CTAG_MESSAGESETMESSAGE/", dataset=True, database=tgtschema,
                     table="ctag_out_messagesetmessage")

    wr.s3.to_parquet(df=all_tags, mode="overwrite",
                     path=s3target + "/CTAG_TAG/", dataset=True, database=tgtschema, table="ctag_tags")

    print('written to s3')
    return


# def format_gss(gss, product_df, all_messagedf, all_inmsgsetdf, all_inmsgsetmsgdf):
#     msgset_sheet = gss.find_sheet('tagmsgset')
#     msgsetmsg_sheet = gss.find_sheet('tagmsgsetmsg')
#
#     doctype_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_LIST', ['CLM', 'RTE']),
#         showCustomUi=True
#     )
#     row_len = all_inmsgsetdf['topic_name'].count()
#     range = "D2:D10000"
#     set_data_validation_for_cell_range(msgset_sheet, range, doctype_validation_rule)
#
#     row_len = product_df['productname'].count()
#     productname_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_RANGE', [gconst.get_productname_cellrange(row_len)]),
#         showCustomUi=True
#     )
#     #row_len = all_inmsgsetdf['topic_name'].count()
#     #range = "E2:E10000"
#     #set_data_validation_for_cell_range(msgset_sheet, range, productname_validation_rule)
#
#     truefalse_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_LIST', ['TRUE', 'FALSE']),
#         showCustomUi=True
#     )
#     row_len = all_inmsgsetdf['topic_name'].count()
#     range = "E2:E10000"
#     set_data_validation_for_cell_range(msgset_sheet, range, truefalse_validation_rule)
#
#     range = "F2:F10000"
#     set_data_validation_for_cell_range(msgset_sheet, range, truefalse_validation_rule)
#
#     row_len = inputtopics_df['tag'].count()
#     inputtopic_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_RANGE', [gconst.get_tags_cellrange(row_len)]),
#         showCustomUi=True
#     )
#     #row_len = all_inmsgsetdf['topic_name'].count()
#     range = "C2:C10000"
#     set_data_validation_for_cell_range(msgset_sheet, range, inputtopic_validation_rule)
#
#
#     row_len = all_inmsgsetdf['topic_name'].count()
#     geninputtopicname_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_RANGE', [gconst.get_geninputtopic_cellrange(row_len)]),
#         showCustomUi=True
#     )
#
#     #range = "A2:A10000"
#     #set_data_validation_for_cell_range(msgsetmsg_sheet, range, geninputtopicname_validation_rule)
#
#     #row_len = all_inmsgsetmsgdf['topic_name'].count()
#     range = "D2:D10000"
#     set_data_validation_for_cell_range(msgsetmsg_sheet, range, truefalse_validation_rule)
#
#     #row_len = all_inmsgsetmsgdf['topic_name'].count()
#     range = "E2:E10000"
#     set_data_validation_for_cell_range(msgsetmsg_sheet, range, truefalse_validation_rule)
#
#     row_len = len(all_messagedf.index);
#     messageidx_validation_rule = DataValidationRule (
#         BooleanCondition('ONE_OF_RANGE', [gconst.get_messageidx_cellrange(row_len)]),
#         showCustomUi=True
#     )
#     #row_len = all_inmsgsetmsgdf['topic_name'].count()
#     #range = "C2:C10000"
#     #set_data_validation_for_cell_range(msgsetmsg_sheet, range, messageidx_validation_rule)
#
#     fmt = cellFormat(
#         backgroundColor=color(217.0/255, 217.0/255, 217.0/255)
#         )
#
#     #format_cell_range(msgset_sheet, 'F:F', fmt)
#     #format_cell_ranges(msgsetmsg_sheet, [('B:B', fmt), ('D:D', fmt), ('F:F', fmt), ('H:H', fmt)])
#
#     return

def get_ndocs_per_topic(conn):
    docs_per_topic_df = get_table(conn, "select ctag_docs_per_topic from param_msrscenario_ctag_v")
    if docs_per_topic_df is None or docs_per_topic_df.empty:
        return 100
    return docs_per_topic_df['ctag_docs_per_topic'].iloc[0]


def main():
    customer = os.environ.get('CUSTOMER')
    awsregion = os.environ.get('AWS_REGION')
    environment = os.environ.get('ENVIRONMENT')
    devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
    region = os.environ.get('REGION')
    action = "RUN_MODEL"

    print('CUSTOMER    :', customer)
    print('AWSREGION   :', awsregion)
    print('ACTION      :', action)
    print('DEVSUFFIX   :', devsuffix)

    s3staging = get_s3staging(customer, region, devsuffix or environment)
    s3target = get_s3target(customer, region, devsuffix or environment)
    srcschema = get_schema(customer, devsuffix)
    tgtschema = get_schema(customer, devsuffix)

    print('S3STAGING   :', s3staging)
    print('S3TARGET    :', s3target)
    print('SRCSCHEMA   :', srcschema)
    print('TGTSCHEMA   :', tgtschema)

    boto3.setup_default_session(region_name=awsregion)

    global conn
    conn = connect(aws_access_key_id=AWS_ACCESS_KEY,
                   aws_secret_access_key=AWS_SECRET_KEY,
                   aws_session_token=AWS_SESSION_TOKEN,
                   s3_staging_dir=s3staging,
                   region_name=awsregion,
                   schema_name=srcschema,
                   )

    # global gss
    # secret_file = os.path.join(os.getcwd(), 'content-tag-f77ada823d18.json')
    # gss = GSpreadsheet(secret_file)

    global gconst
    gconst = SpreadsheetConstants()

    #    url = 'https://docs.google.com/spreadsheets/d/1DuAHL0n2u8a9rH3OaStv1nO9ee9VAw5QhYlniv4EeN8/edit#gid=0'
    # global spread
    # gss.openspread(customer.lower() + "-content-tag")

    start_time = time.time()

    global message_df
    message_df = get_table(conn, "select * from ctag_messages_recently_used")
    
    # read nco message contents
    nco_message_df = get_table(conn, "select cms_messageuid, content as vault_content from ctag_scraped_content")
    message_df = pd.merge(message_df, nco_message_df, on=['cms_messageuid'], how='left')
    message_df['messagecontent'] = message_df['vault_content'].combine_first(message_df['messagecontent'])
    message_df = message_df.drop(columns=['vault_content'])
    
    print(message_df[message_df['document_type'] == 'NCO']['messagecontent'])
    
    message_df = prepare_message_df(message_df)

    global inputtopics_df
    inputtopics_df2 = pd.DataFrame()
    try:
        inputtopics_df2 = get_table(conn, "select * from ctag_tags")
    except pd.io.sql.DatabaseError:
        inputtopics_df2 = pd.DataFrame()

    if (inputtopics_df2.empty):
        inputtopics_df = prepare_default_tag_df(customer)
    else:
        inputtopics_df = inputtopics_df2

    global product_df
    product_df = message_df.groupby(['productname', 'productuid']).size().reset_index(name='counts')

    global docprod_df
    docprod_df = message_df.groupby(['document_type', 'productuid', 'productname']).size().reset_index(name='counts')

    global subset_df
    subset_df = message_df.copy()

    productuid_df_dict = {}
    for productuid, product_group_df in message_df.groupby(['document_type', 'productuid', 'productname']):
        productuid_df_dict[productuid] = product_group_df.reset_index(drop=True)

    all_vocabdf = pd.DataFrame()
    all_messagedf = pd.DataFrame()
    all_topicdf = pd.DataFrame()
    all_topicworddf = pd.DataFrame()
    all_categories_df = pd.DataFrame({'Category': pd.Series(dtype='string'), 'Name': pd.Series(dtype='string'),
                                      'productuid': pd.Series(dtype='string')})
    all_tags = pd.DataFrame()

    global all_inmsgsetdf
    all_inmsgsetdf = pd.DataFrame()

    global all_inmsgsetmsgdf
    all_inmsgsetmsgdf = pd.DataFrame()

    all_inmsgsetdf_s3 = pd.DataFrame({'topic_uid': pd.Series(dtype='string'), 'topic_name': pd.Series(dtype='string'),
                                      'document_type': pd.Series(dtype='string'),
                                      'productuid': pd.Series(dtype='string'), 'tag': pd.Series(dtype='string'),
                                      'category': pd.Series(dtype='string')})
    all_inmsgsetmsgdf_s3 = pd.DataFrame(
        {'topic_uid': pd.Series(dtype='string'), 'messageproductuid': pd.Series(dtype='string'), 'doc_score': [],
         'doc_rank': []})

    small_product_df=pd.DataFrame()


    # process vocab, topic, topicword, categories, msgset and msgsetmsg individually for each product
    for keys, product_group_df in productuid_df_dict.items():
        document_type, productuid, productname = keys
        print(f"Product UID: {productuid}")
        if (len(product_group_df) < 10):
            small_product_df = pd.concat([small_product_df, product_group_df], ignore_index=True)
            print(f" Processing due to less documents to cluster: {productuid}")
            print("small Product Initial", small_product_df)
            continue

        print("small Product Initial 530", small_product_df)
        try:
            model = TopicModeler('ALL', productuid, product_group_df, min_count=2)
        except ValueError as ve:
            print(f"Skipping tagging for product {productuid} since Topic modeling failed with ValueError: {ve}")
            continue
        except Exception as e:
            print(f"Skipping tagging for product {productuid} since Topic modeling failed with Exception: {e}")
            continue
        model.enhance_messagedf_with_model_output()
        model.build_vocabdf_with_model_output()
        model.build_topicdf_topicworddf_with_model_output()
        model.enhance_topicdf_with_llm_output()

        product_vocabdf = pd.DataFrame()
        product_messagedf = pd.DataFrame()
        product_topicdf = pd.DataFrame()
        product_topicworddf = pd.DataFrame()

        try:
            product_categories_df = model.generate_tags_for_topicdf_keywords()
        except pd.errors.ParserError as pe:
            print(
                f"Skipping tagging for product {productuid} since Topic modeling failed with ParseError from open ai response: {pe}")
            continue
        except Exception as e:
            print(f"Skipping tagging for product {productuid} since Topic modeling failed with Exception: {e}")
            continue

        product_categories_df = product_categories_df.assign(productuid=productuid)

        product_vocabdf = model.get_vocabdf()
        product_messagedf = model.get_messagedf()
        product_topicdf = model.get_topicdf()
        product_topicworddf = model.get_topicworddf()

        print(product_messagedf)
        print(product_vocabdf)
        print(product_topicdf)
        print(product_topicworddf)
        print(product_categories_df)

        all_vocabdf = pd.concat([all_vocabdf, product_vocabdf], ignore_index=True)
        all_messagedf = pd.concat([all_messagedf, product_messagedf], ignore_index=True)
        all_topicdf = pd.concat([all_topicdf, product_topicdf], ignore_index=True)
        all_topicworddf = pd.concat([all_topicworddf, product_topicworddf], ignore_index=True)
        all_categories_df = pd.concat([all_categories_df, product_categories_df], ignore_index=True)

        # process msgset and msgset msg
        product_inmsgsetdf = pd.DataFrame()
        product_inmsgsetmsgdf = pd.DataFrame()

        product_inmsgsetdf_s3 = pd.DataFrame(
            {'topic_uid': pd.Series(dtype='string'), 'topic_name': pd.Series(dtype='string'),
             'document_type': pd.Series(dtype='string'), 'productuid': pd.Series(dtype='string'),
             'tag': pd.Series(dtype='string')})
        product_inmsgsetmsgdf_s3 = pd.DataFrame(
            {'topic_uid': pd.Series(dtype='string'), 'messageproductuid': pd.Series(dtype='string'), 'doc_score': [],
             'doc_rank': []})

        inidx = 0
        msgset_rowoffset = 0
        msgsetmsg_rowoffset = 0
        ndocs_per_topic = get_ndocs_per_topic(conn)
        doctype = document_type

        counts = len(product_group_df)
        msgsetdf, msgsetmsgdf = model.get_messageset_message(gconst, doctype, productuid, productname,
                                                             product_categories_df, msgset_rowoffset,
                                                             msgsetmsg_rowoffset,
                                                             docs_per_topic=ndocs_per_topic,is_multi_product=False)

        if (msgsetmsgdf.doc_uid.count() > 0):
            product_inmsgsetdf = msgsetdf
            product_inmsgsetmsgdf = msgsetmsgdf

        # replace all the vlookups with actual uids

        if 'productuid' in product_inmsgsetdf.columns:
            product_inmsgsetdf_s3 = product_inmsgsetdf.drop(columns=['productuid'])
        if 'productname' in product_inmsgsetdf_s3.columns and 'productname' in product_df.columns and 'productuid' in product_df.columns:
            product_inmsgsetdf_s3 = pd.merge(product_inmsgsetdf_s3,product_df[['productname', 'productuid']],on='productname',how='inner').drop(columns=['productname'])

        if 'topic_uid' in product_inmsgsetmsgdf.columns:
            product_inmsgsetmsgdf_s3 = product_inmsgsetmsgdf.drop(columns=['topic_uid'])

        if 'topic_name' in product_inmsgsetmsgdf_s3.columns and 'topic_name' in product_inmsgsetdf.columns and 'topic_uid' in product_inmsgsetdf.columns:
            product_inmsgsetmsgdf_s3 = pd.merge(
                product_inmsgsetmsgdf_s3,
                product_inmsgsetdf[['topic_name', 'topic_uid']],
                on='topic_name',
                how='inner'
            ).drop(columns=['topic_name'])

        # Check if 'doc', 'doc_uid', and 'doc_recentflag' exist before dropping them
        columns_to_drop = ['doc', 'doc_uid', 'doc_recentflag']
        existing_columns_to_drop = [col for col in columns_to_drop if col in product_inmsgsetmsgdf_s3.columns]

        if existing_columns_to_drop:
            product_inmsgsetmsgdf_s3 = product_inmsgsetmsgdf_s3.drop(columns=existing_columns_to_drop)

        if 'messageproductuid' in product_inmsgsetmsgdf_s3.columns and 'messageproductuid' in message_df.columns:
            product_inmsgsetmsgdf_s3 = pd.merge(
                product_inmsgsetmsgdf_s3,
                message_df[['messageproductuid']],
                left_on=['messageproductuid'],
                right_on=['messageproductuid'],
                how='inner'
            )
        all_inmsgsetdf_s3 = pd.concat([all_inmsgsetdf_s3, product_inmsgsetdf_s3], ignore_index=True)
        all_inmsgsetmsgdf_s3 = pd.concat([all_inmsgsetmsgdf_s3, product_inmsgsetmsgdf_s3], ignore_index=True)
        #all_inmsgsetmsgdf_s3 = all_inmsgsetmsgdf_s3.drop_duplicates(subset=['topic_uid'])

        print(all_inmsgsetdf_s3)
        print(all_inmsgsetmsgdf_s3)


    if not small_product_df.empty:
        try:
            model = None
            model = TopicModeler('ALL', "combined_small_productID", small_product_df, min_count=2)
        except ValueError as ve:
            print(f"Skipping tagging for product {productuid} since Topic modeling failed with ValueError: {ve}")

        except Exception as e:
            print(f"Skipping tagging for product {productuid} since Topic modeling failed with Exception: {e}")

        if model:
            model.enhance_messagedf_with_model_output()
            model.build_vocabdf_with_model_output()
            model.build_topicdf_topicworddf_with_model_output()
            model.enhance_topicdf_with_llm_output()

            small_product_vocabdf = pd.DataFrame()
            small_product_messagedf = pd.DataFrame()
            small_product_topicdf = pd.DataFrame()
            small_product_topicworddf = pd.DataFrame()

            small_product_categories_df = pd.DataFrame()
            try:
                small_product_categories_df = model.generate_tags_for_topicdf_keywords()
            except pd.errors.ParserError as pe:
                print(
                    f"Skipping tagging for product {productuid} since Topic modeling failed with ParseError from open ai response: {pe}")

            except Exception as e:
                print(f"Skipping tagging for product {productuid} since Topic modeling failed with Exception: {e}")

            if not small_product_categories_df.empty:
                small_product_categories_df = small_product_categories_df.assign(productuid=productuid)

                small_product_vocabdf = model.get_vocabdf()
                small_product_messagedf = model.get_messagedf()
                small_product_topicdf = model.get_topicdf()
                small_product_topicworddf = model.get_topicworddf()

                print(small_product_messagedf)
                print(small_product_vocabdf)
                print(small_product_topicdf)
                print(small_product_topicworddf)
                print(small_product_categories_df)

                all_vocabdf = pd.concat([all_vocabdf, small_product_vocabdf], ignore_index=True)
                all_messagedf = pd.concat([all_messagedf, small_product_messagedf], ignore_index=True)
                all_topicdf = pd.concat([all_topicdf, small_product_topicdf], ignore_index=True)
                all_topicworddf = pd.concat([all_topicworddf, small_product_topicworddf], ignore_index=True)
                all_categories_df = pd.concat([all_categories_df, small_product_categories_df], ignore_index=True)

                # process msgset and msgset msg
                small_product_inmsgsetdf = pd.DataFrame()
                small_product_inmsgsetmsgdf = pd.DataFrame()

                small_product_inmsgsetdf_s3 = pd.DataFrame(
                    {'topic_uid': pd.Series(dtype='string'), 'topic_name': pd.Series(dtype='string'),
                    'document_type': pd.Series(dtype='string'), 'productuid': pd.Series(dtype='string'),
                    'tag': pd.Series(dtype='string')})
                small_product_inmsgsetmsgdf_s3 = pd.DataFrame(
                    {'topic_uid': pd.Series(dtype='string'), 'messageproductuid': pd.Series(dtype='string'), 'doc_score': [],
                    'doc_rank': []})

                inidx = 0
                msgset_rowoffset = 0
                msgsetmsg_rowoffset = 0
                ndocs_per_topic = get_ndocs_per_topic(conn)
                doctype = document_type

                counts = len(small_product_df)

                small_msgsetdf, small_msgsetmsgdf = model.get_messageset_message(gconst, doctype, productuid, productname,
                                                                    small_product_categories_df, msgset_rowoffset,
                                                                    msgsetmsg_rowoffset,
                                                                    docs_per_topic=ndocs_per_topic,is_multi_product=True)

                if (small_msgsetmsgdf.doc_uid.count() > 0):
                    small_product_inmsgsetdf = small_msgsetdf
                    small_product_inmsgsetmsgdf = small_msgsetmsgdf


                # replace all the vlookups with actual uids
                if 'productuid' in small_product_inmsgsetdf.columns:
                    small_product_inmsgsetdf_s3 = small_product_inmsgsetdf.drop(columns=['productuid'])


                if 'productname' in small_product_inmsgsetdf_s3.columns and 'productname' in product_df.columns and 'productuid' in product_df.columns:
                    small_product_inmsgsetdf_s3 = pd.merge(
                        small_product_inmsgsetdf_s3,
                        product_df[['productname', 'productuid']],
                        on='productname',
                        how='inner'
                    ).drop(columns=['productname'])

                #small_product_inmsgsetdf_s3['productuid']="combined_small_productID"


                if 'topic_uid' in small_product_inmsgsetmsgdf.columns:
                    small_product_inmsgsetmsgdf_s3 = small_product_inmsgsetmsgdf.drop(columns=['topic_uid'])

                if 'topic_name' in small_product_inmsgsetmsgdf_s3.columns and 'topic_name' in small_product_inmsgsetdf.columns and 'topic_uid' in small_product_inmsgsetdf.columns:
                    small_product_inmsgsetmsgdf_s3 = pd.merge(
                        small_product_inmsgsetmsgdf_s3,
                        small_product_inmsgsetdf[['topic_name', 'topic_uid']],
                        on='topic_name',
                        how='inner'
                    ).drop(columns=['topic_name'])

                columns_to_drop = ['doc', 'doc_uid', 'doc_recentflag']
                existing_columns_to_drop = [col for col in columns_to_drop if col in small_product_inmsgsetmsgdf_s3.columns]

                if existing_columns_to_drop:
                    small_product_inmsgsetmsgdf_s3 = small_product_inmsgsetmsgdf_s3.drop(columns=existing_columns_to_drop)

                if 'messageproductuid' in small_product_inmsgsetmsgdf_s3.columns and 'messageproductuid' in message_df.columns:
                    small_product_inmsgsetmsgdf_s3 = pd.merge(
                        small_product_inmsgsetmsgdf_s3,
                        message_df[['messageproductuid']],
                        left_on=['messageproductuid'],
                        right_on=['messageproductuid'],
                        how='inner'
                    )

                all_inmsgsetdf_s3 = pd.concat([all_inmsgsetdf_s3, small_product_inmsgsetdf_s3], ignore_index=True)
                all_inmsgsetmsgdf_s3 = pd.concat([all_inmsgsetmsgdf_s3, small_product_inmsgsetmsgdf_s3], ignore_index=True)
                if 'messageproductuid' in all_inmsgsetmsgdf_s3.columns and 'doc_score' in all_inmsgsetmsgdf_s3.columns:
                    all_inmsgsetmsgdf_s3['topic_rank'] = all_inmsgsetmsgdf_s3.groupby('messageproductuid')[
                        'doc_score'].rank(method='first', ascending=False)
                    all_inmsgsetmsgdf_s3 = all_inmsgsetmsgdf_s3[
                        (all_inmsgsetmsgdf_s3['topic_rank'] == 1) | (
                                    all_inmsgsetmsgdf_s3['doc_score'] > 0.3)]
                    all_inmsgsetmsgdf_s3 = all_inmsgsetmsgdf_s3.drop(columns=['topic_rank'])


                #all_inmsgsetmsgdf_s3 = all_inmsgsetmsgdf_s3.drop_duplicates(subset=['topic_uid'])

                print(all_inmsgsetdf_s3)
                print(all_inmsgsetmsgdf_s3)

    # copy llm generated categories to all_tags to copy to s3 after renaming 'name' column to 'tags'
    all_tags = all_categories_df[['Category', 'Name', 'productuid']].copy()
    all_tags.columns = ['category', 'tags', 'productuid']

    try:
        save_result_to_s3(s3target, tgtschema, all_inmsgsetdf_s3, all_inmsgsetmsgdf_s3, all_vocabdf, all_topicdf,
                          all_topicworddf, all_messagedf, all_categories_df, all_tags)
    except Exception as e:
        print(f"An error occurred in the saving process: {e}")


if __name__ == "__main__":
    main()
