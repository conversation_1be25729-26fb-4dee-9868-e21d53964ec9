#!/bin/bash

# Update the package list and install system dependencies
sudo apt-get update && sudo apt-get install -y \
    tesseract-ocr \
    libtesseract-dev \
    libleptonica-dev \
    poppler-utils \
    libgl1-mesa-glx \
    build-essential \
    libpoppler-cpp-dev \
    git

# Check if virtual environment folder exists, if not create it
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate the virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python dependencies
pip install boto3 PyAthena pandas numpy pytesseract Pillow datetime beautifulsoup4 awswrangler pdf2image PyPDF2 transformers torch

# Deactivate the virtual environment
deactivate

echo "Setup complete. Virtual environment 'venv' is ready with all dependencies installed."
