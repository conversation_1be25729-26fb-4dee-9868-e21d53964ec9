# Update the package list and install system dependencies
apt-get update && apt-get install -y \
    tesseract-ocr \
    libtesseract-dev \
    libleptonica-dev \
    poppler-utils \
    libgl1-mesa-glx \
    build-essential \
    libpoppler-cpp-dev

#    git

# Upgrade pip
pip install --upgrade pip

pip list

#pip install boto3==1.34.151 PyAthena==3.8.3 pandas==2.2.2 numpy==1.26.4 pytesseract==0.3.10 Pillow==10.4.0 \
#    DateTime==5.5 beautifulsoup4==4.12.3 awswrangler==3.9.0 pdf2image==1.17.0 PyPDF2==3.0.1 transformers==4.43.3 torch==2.2.2

# Install Python dependencies
pip install lxml PyAthena==3.8.3 pytesseract==0.3.10 Pillow==10.4.0 \
    DateTime==5.5 beautifulsoup4==4.12.3 pdf2image==1.17.0 PyPDF2==3.0.1 transformers==4.43.3 torch==2.2.2

echo "Setup complete. Virtual environment 'venv' is ready with all dependencies installed."

# Directory of this script is the CG Install dir
CONTENT_TAGGING_INSTALL_DIR=`dirname $0`
#CONTENT_TAGGING_INSTALL_DIR=`readlink -f $CG_INSTALL_DIR`
echo "Content-tagging Install directory = $CONTENT_TAGGING_INSTALL_DIR"

python3 $CONTENT_TAGGING_INSTALL_DIR/prepare_vault_contents.py
