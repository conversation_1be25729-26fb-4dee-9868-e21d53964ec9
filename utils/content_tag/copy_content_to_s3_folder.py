import boto3
import re
from urllib.parse import urlparse

def extract_key_part(filename):
    # Extract ADC-xxx pattern (modify if needed)
    match = re.search(r'(ADC-\d+)', filename)
    return match.group(1) if match else None

def copy_s3_object(s3_client, source_bucket, target_bucket, old_key, new_key):
    # Copy from source bucket to target bucket
    s3_client.copy_object(
        Bucket=target_bucket,
        CopySource={'Bucket': source_bucket, 'Key': old_key},
        Key=new_key
    )

def process_s3_files(source_s3_root, target_s3_root):
    # Parse S3 URLs
    src = urlparse(source_s3_root)
    tgt = urlparse(target_s3_root)
    source_bucket = src.netloc
    source_prefix = src.path.lstrip('/')
    target_bucket = tgt.netloc
    target_prefix = tgt.path.lstrip('/')

    s3 = boto3.client('s3')

    paginator = s3.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=source_bucket, Prefix=source_prefix)

    for page in pages:
        for obj in page.get('Contents', []):
            key = obj['Key']
            if re.search(r'(/ADC-\d+/)', key):
                continue
            filename = key.split('/')[-1]

            part = extract_key_part(filename)
            if not part:
                print(f"No match in {filename}")
                continue

            # Target key: target_prefix/ADC-xxx/content/<filename>
            new_key = f"{target_prefix}{part}/content/{filename}"

            print(f"📦 Copying s3://{source_bucket}/{key} → s3://{target_bucket}/{new_key}")
            copy_s3_object(s3, source_bucket, target_bucket, key, new_key)

if __name__ == '__main__':
    # Example: source and target buckets differ
    source_path = 's3://aktana-bdp-abbottus/prod/content/'
    target_path = 's3://aktana-externalfiles-abbottus/prod/content/'

    process_s3_files(source_path, target_path)
