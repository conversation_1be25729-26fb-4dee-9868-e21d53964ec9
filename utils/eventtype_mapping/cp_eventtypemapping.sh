S3_CUSTOMER=$1
S3_BUCKET="s3://aktana-bdp-${S3_CUSTOMER}/prod/tempdata/event_mapping/eventtype_category_mapping.csv"

echo "S3_CUSTOMER: $S3_CUSTOMER"
echo "S3_BUCKET: $S3_BUCKET"

FILEEXISTS=$(aws s3 ls $S3_BUCKET)
if [ -z "$FILEEXISTS" ]; then
  echo "File does not exist"
  grep ${S3_CUSTOMER} ./utils/eventtype_mapping/eventtype_mapping.csv > ./utils/eventtype_mapping/customer_event_map.csv
  if [ -s ./utils/eventtype_mapping/customer_event_map.csv ]; then
    sed -e "s/${S3_CUSTOMER},//g" -i.backup ./utils/eventtype_mapping/customer_event_map.csv
  else
    grep default ./utils/eventtype_mapping/eventtype_mapping.csv > ./utils/eventtype_mapping/customer_event_map.csv
    sed -e "s/default,//g" -i.backup ./utils/eventtype_mapping/customer_event_map.csv
  fi
  aws s3 cp ./utils/eventtype_mapping/customer_event_map.csv $S3_BUCKET
else
  echo "File already exists"
fi
echo "Done"