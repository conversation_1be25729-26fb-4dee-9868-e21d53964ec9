#!/bin/sh

# Get CUSTOMER and ENVIRONMENT from env vars
#CUSTOMER=$1
#ENVIRONMENT=$2
SCHEMASUFFIX=$2
BASEDIR=$(dirname "$0")
#. ${BASEDIR}/venv/bin/activate

#if [ -z $SCHEMASUFFIX ];
#then
#    SCHEMASUFFIX=$ENVIRONMENT
#fi

python ${BASEDIR}/copy_tempdata.py --action=tempcopy --customer=${CUSTOMER} --environment=${ENVIRONMENT} --region=${REGION} --awsregion=${AWS_REGION} --devsuffix=${SCHEMASUFFIX}
