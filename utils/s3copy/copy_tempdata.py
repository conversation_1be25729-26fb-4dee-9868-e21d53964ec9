#!/bin/python
import getopt, sys, os
import requests
import mysql.connector
import re
import logging.config
import time
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
#from pyathena import connect
import os
import pandas as pd
import awswrangler as wr

log_config = {
    "version":1,
    "root":{
        "handlers" : ["console", "file"],
        "level": "DEBUG"
    },
    "handlers":{
        "console":{
            "formatter": "std_out",
            "class": "logging.StreamHandler",
            "level": "DEBUG"
        },
        "file":{
            "formatter":"std_out",
            "class":"logging.FileHandler",
            "level":"INFO",
            "filename":"all_messages.log"
        }
    },
    "formatters":{
        "std_out": {
            "format": "%(levelname)s : %(module)s : %(funcName)s : %(message)s",
        }
    },
}

logging.config.dictConfig(log_config)
logger = logging.getLogger("copy_tempdata")

#AWS_ACCESS_KEY=os.environ['AWS_ACCESS_KEY_ID']
#AWS_SECRET_KEY=os.environ['AWS_SECRET_ACCESS_KEY']
#AWS_SESSION_TOKEN=os.environ['AWS_SESSION_TOKEN']

SCHEMA_NAME = "impact_sanofius_satya"
S3_STAGING_DIR = "s3://aktana-bdpuseks-glue/athena/"
AWS_REGION = "us-east-1"


class S3Writer:
    def __init__(self, bucket, prefix, database):
        self.bucket = bucket;
        self.prefix = prefix;
        self.database = database;

    def write_parquet(self, df, tablename, mode):
        path = self.bucket + self.prefix
        wr.s3.to_parquet(df=df, mode=mode, path=path, dataset=True, database=self.database, table=tablename);


class MetaDB:
    def __init__(self, host, user, password, database=""):
        self.dbconn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )

    def get_customer_rds(self, customer, environment):
        sql = """
        select  substring(c.regionName,1,2) as region, c.customerName as customer, ce.rdsServer as rdsHost, 3306 as rdsPort, ce.rdsUserName, ce.rdsUserPassword, ce.enginedbName as databaseName, ce.copystormdbName as databaseName_cs, ce.stagedbName as databaseName_stage, c.customerId as customerid
        from Customer c inner join CustomerEnvironment ce on c.customerName = %s and c.customerId = ce.customerId
        and ce.envName = %s where c.isActive = 1 and c.customerDescription not like '%Parent%' order by c.`regionName`
        """
        cursor = self.dbconn.cursor()
        cursor.execute(sql, [customer, environment])
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        results = []
        for rds in records:
            row = { 'region': rds[0], 'customer': rds[1], 'rdshost': rds[2], 'rdsport': rds[3], 'rdsuser': rds[4], 'rdspassword' : rds[5], 'database': rds[6], 'database_cs': rds[7], 'database_stage': rds[8], 'customerid': rds[9] }
            results.append(row)

        return results

    def get_company_rds(self, company, environment):
        sql = """
        select  substring(c.regionName,1,2) as region, c.customerName as customer, case when c.regionName = 'cn' then 'cnbastion.aktanachina.com'  when ce.rdsForwardUrl is NOT NULL then ce.rdsForwardUrl  else concat('bdp',c.regionName,'001.aktana.com') end rdsHost, case when ce.rdsForwardPort is not null then ce.rdsForwardPort else 33000+c.customerId end  as rdsPort, 'appadmin' as rdsUser,  c.rdsUserPassword, ce.enginedbName as databaseName, ce.copystormdbName as databaseName_cs, ce.stagedbName as databaseName_stage, c.customerId as customerid
        from Customer c inner join CustomerEnvironment ce on c.customerName like %s and c.customerId = ce.customerId
        and ce.envName = %s where c.isActive = 1 and c.customerId < 1000 and c.customerDescription not like '%Parent%' order by c.`regionName`
        """
        cursor = self.dbconn.cursor()
        cursor.execute(sql, ['%'+company+'%', environment])
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        results = []
        for rds in records:
            row = { 'region': rds[0], 'customer': rds[1], 'rdshost': rds[2], 'rdsport': rds[3], 'rdsuser': rds[4], 'rdspassword' : rds[5], 'database': rds[6], 'database_cs': rds[7], 'database_stage': rds[8], 'customerid': rds[9] }
            results.append(row)

        return results


class CustomerDB:
    def __init__(self, host, user, password, database="", port="3306"):
        print(database)
        self.dbconn = mysql.connector.connect(
            host=host,
            user=user,
            port=port,
            password=password,
            database=database
        )

    def get_customer_sql(self, sql):
        print(sql, flush=True)
        cursor = self.dbconn.cursor()
        cursor.execute("SET SESSION group_concat_max_len = 10000000;")
        cursor.execute(sql, [])
        fields = cursor.description
        names = [ x[0] for x in cursor.description]
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        return pd.DataFrame(records, columns=names)
        #return fields, records

    def fill_na(self, df):
        for c in df:
            if str(df[c].dtype) in ('object', 'string_', 'unicode_'):
                  df[c].fillna(value='', inplace=True)
        return df


    def get_productcatalog(self):
        sql = """
              SELECT * from AKT_ProductCatalog
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_aktaccounts(self):
        sql = """
              SELECT * from AKT_Accounts
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_repengagement(self):
        sql = """
              SELECT * from RPT_Rep_Engagement_Segmentation
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_productmap(self):
        sql = """
              SELECT * from AKT_ProductMap
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_marketbasket_product(self):
        sql = """
              SELECT * from AKT_MarketBasket_Product
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_apptracking(self):
        sql = """
              SELECT * from AKT_AppTracking
        """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_account_segment_mapping(self, dbovd):
        sql = """ SELECT coalesce(accountId, -9999) accountId,
                    coalesce(accountUID, '-9999') accountUID,
                    coalesce(productId, -9999) productId,
                    coalesce(productUID, '-9999') productUID,
                    coalesce(hcpSegmentIndex, -1) hcpSegmentIndex,
                    coalesce(hcpSegmentName, 'notier') hcpSegmentName
                    from """ + dbovd + """.AKT_Account_Segment_Mapping_V """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df
        
    def get_analytics_product_group(self, dbovd):
        sql = """ SELECT * from """ + dbovd + """.Analytics_Product_Group_vod__c """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df
    
    def get_rpt_stage_customer_impact_agg(self, dbovd):
        sql = """ SELECT * from """ + dbovd + """.RPT_Stage_Customer_Impact_Agg """
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df
    
    def get_rep_team(self, dbovd):
        sql = """ SELECT repTeamId, repTeamName, seConfigId from """ + dbovd + """.RepTeam where isDeleted = 0"""
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_eventtype(self, dbovd):
        sql = """ SELECT * from """ + dbovd + """.EventType"""
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_labeltype(self, dbovd):
        sql = """ SELECT * from """ + dbovd + """.LabelType"""
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

    def get_labeldefinition(self, dbovd):
        sql = """ SELECT * from """ + dbovd + """.LabelDefinition"""
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df



def get_schema(customer, devsuffix):
    schema = customer + "_" + devsuffix + "_" + "current"
    return schema

def get_s3target(customer, region, devsuffix):
    s3target = "s3://aktana-bdp" + region + "-glue/" + "dbt" + "/tempdata/" + customer + "/" + devsuffix + "/"
    return s3target

def main():
    # parse arugments
    opts, args = getopt.getopt(sys.argv[1:], "", ["action=","customer=","company=","executionid=","region=","startdate=","enddate=","awsregion=","devsuffix=","environment="])
    customer = ""
    company = ""
    region = ""
    awsregion = ""
    executionid = ""
    action = ""
    startdt = ""
    enddt = ""
    devsuffix = ""
    environment = "prod"
    for opt in opts:
        print (opt[0])
        if opt[0] == "--customer":
            customer = opt[1]
        elif opt[0] == "--company":
            company = opt[1]
        elif opt[0] == "--executionid":
            executionid = opt[1]
        elif opt[0] == "--region":
            region = opt[1]
        elif opt[0] == "--awsregion":
            awsregion = opt[1]
        elif opt[0] == "--action":
            action =  opt[1]
        elif opt[0] == "--startdate":
            startdt =  opt[1]
        elif opt[0] == "--enddate":
            enddt =  opt[1]
        elif opt[0] == "--devsuffix":
            devsuffix =  opt[1]
        elif opt[0] == "--environment":
            environment =  opt[1]

    print ('ACTION      :', action)
    print ('COMPANY     :', company)
    print ('CUSTOMER    :', customer)
    print ('REGION      :', region)
    print ('AWSREGION   :', awsregion)
    print ('EXECUTIONID :', executionid)
    print ('STARTDT     :', startdt)
    print ('ENDDT       :', enddt)
    print ('DEVSUFFIX   :', devsuffix)
    print ('ENVIRONMENT :', environment)

    # create db connection
    matadb = MetaDB(os.environ.get("METADATA_HOST"), os.environ.get("METADATA_USER"), os.environ.get("METADATA_PWD"), 'pagerduty_analysis')
    # get job list need to analyze
    rundeck_info = []
    rds_info = []

    if (action == 'tempcopy'):
       print("Connecting to metadata db", flush=True)
       matadb2 = MetaDB(os.environ.get("METADATA_HOST"), os.environ.get("METADATA_USER"), os.environ.get("METADATA_PWD"), 'aktanameta')
       print("Connected to metadata db", flush=True)
       if customer != "":
        rds_info = matadb2.get_customer_rds(customer, environment)
       elif region != "":
        rds_info = matadb2.get_region_rds(region)
       else:
        rds_info = matadb2.get_all_rds()

       dbtype="database_stage"

       for row in rds_info:
           print(row['customer'])
           customer = row['customer']
           s3target = get_s3target(customer, region, (devsuffix or environment))
           tgtschema = get_schema(customer, (devsuffix or environment))
           try:
               print(row)
               print("Connecting to customer db", flush=True)
               cdb = CustomerDB(row['rdshost'], row['rdsuser'], row['rdspassword'], row[dbtype],  row['rdsport'])
               print("Connected to customer db", flush=True)

               #Create database if it doesn't already exist
               wr.catalog.create_database(name=tgtschema, exist_ok=True)

               try:
                  df_aktaccounts = cdb.get_aktaccounts()
                  df_aktaccounts['LastModifiedDate']=df_aktaccounts['LastModifiedDate'].astype(str)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_accounts')
                  wr.s3.to_csv(df=df_aktaccounts, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_Accounts", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_accounts");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_Accounts ' + 'skipped')

               try:
                  df_productcatalog = cdb.get_productcatalog()
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_productcatalog')
                  wr.s3.to_csv(df=df_productcatalog, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_ProductCatalog", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_productcatalog");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_ProductCatalog ' + 'skipped')


               try:
                  df_repengagement = cdb.get_repengagement()
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_rpt_rep_engagement_segmentation')
                  wr.s3.to_csv(df=df_repengagement, mode="overwrite", index=False, dataset=True, path=s3target+"RPT_Rep_Engagement_Segmentation", sep=",", na_rep='',  database=tgtschema, table="tempdata_rpt_rep_engagement_segmentation");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' RPT_Rep_Engagement_Segmentation ' + 'skipped')

               try:
                  df_apptracking = cdb.get_apptracking()
                  print("write to csv")
                  print(tgtschema)
                  print(s3target)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_apptracking')
                  wr.s3.to_csv(df=df_apptracking, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_AppTracking", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_apptracking");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_AppTracking ' + 'skipped')


               try:
                  df_productmap = cdb.get_productmap()
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_productmap')
                  wr.s3.to_csv(df=df_repengagement, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_ProductMap", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_productmap");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_ProductMap ' + 'skipped')


               try:
                  df_marketbasket_product = cdb.get_marketbasket_product()
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_marketbasket_product')
                  wr.s3.to_csv(df=df_marketbasket_product, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_MarketBasket_Product", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_marketbasket_product");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_MarketBasket_Product ' + 'skipped')


               try:
                  dbovd =  row["database_cs"]
                  df_analytics_product_group = cdb.get_analytics_product_group(dbovd)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_analytics_product_group')
                  wr.s3.to_csv(df=df_analytics_product_group, mode="overwrite", index=False, dataset=True, path=s3target+"Analytics_Product_Group_vod__c", sep=",", na_rep='',  database=tgtschema, table="tempdata_analytics_product_group");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' Analytics_Product_Group_vod__c ' + 'skipped')
               
               try:
                  dbovd =  row["database_stage"]
                  df_account_segment_mapping = cdb.get_account_segment_mapping(dbovd)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_akt_account_segment_mapping')
                  wr.s3.to_csv(df=df_account_segment_mapping, mode="overwrite", index=False, dataset=True, path=s3target+"AKT_Account_Segment_Mapping", sep=",", na_rep='',  database=tgtschema, table="tempdata_akt_account_segment_mapping");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' AKT_Account_Segment_Mapping ' + 'skipped')


               try:
                  print("Processing get_rpt_stage_customer_impact_agg", flush=True)
                  dbovd =  row["database_stage"]
                  df_rpt_stage_customer_impact_agg = cdb.get_rpt_stage_customer_impact_agg(dbovd)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_rpt_stage_customer_impact_agg')
                  wr.s3.to_csv(df=df_rpt_stage_customer_impact_agg, mode="overwrite", index=False, dataset=True, path=s3target+"RPT_Stage_Customer_Impact_Agg", sep=",", na_rep='',  database=tgtschema, table="tempdata_rpt_stage_customer_impact_agg");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' RPT_Stage_Customer_Impact_Agg ' + 'skipped')
                

               try:
                  print("Processing get_rep_team", flush=True)
                  dbovd =  row["database"]
                  df_rep_team = cdb.get_rep_team(dbovd)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target, flush=True)
                  wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_rep_team')
                  wr.s3.to_csv(df=df_rep_team, mode="overwrite", index=False, dataset=True, path=s3target+"Rep_Team", sep=",", na_rep='',  database=tgtschema, table="tempdata_rep_team");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' Rep_Team ' + 'skipped')


               #cdb.close();
           except Exception as e:
               print (e)
               print (row['customer'] + ' skipped')


               try:
                  dbovd =  row["database"]
                  df_eventtype = cdb.get_eventtype(dbovd)
                  print("write to csv")
                  print(tgtschema)
                  print(s3target)

                  if (len(df_eventtype) > 0):
                      wr.catalog.delete_table_if_exists(database=tgtschema, table='tempdata_eventtype')
                      wr.s3.to_csv(df=df_eventtype, mode="overwrite", index=False, dataset=True, path=s3target+"EventType", sep=",", na_rep='',  database=tgtschema, table="tempdata_eventtype");
               except Exception as ex:
                  print(ex)
                  print(row['customer'] + ' ' + ' EventType' + 'skipped')

              
if __name__ == "__main__":
    main()



