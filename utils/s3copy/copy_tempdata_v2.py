#!/bin/python
import argparse
import mysql.connector
import logging.config
import os
import pandas as pd
import awswrangler as wr

log_config = {
    "version":1,
    "root":{
        "handlers" : ["console", "file"],
        "level": "DEBUG"
    },
    "handlers":{
        "console":{
            "formatter": "std_out",
            "class": "logging.StreamHandler",
            "level": "DEBUG"
        },
        "file":{
            "formatter":"std_out",
            "class":"logging.FileHandler",
            "level":"INFO",
            "filename":"all_messages.log"
        }
    },
    "formatters":{
        "std_out": {
            "format": "%(levelname)s : %(module)s : %(funcName)s : %(message)s",
        }
    },
}

logging.config.dictConfig(log_config)
logger = logging.getLogger("copy_tempdata")

s3map={}
s3map["us-east-1"] = "s3://aktana-bdpuseks-glue/"
s3map["eu-central-1"] = "s3://aktana-bdpeueks-glue/"
s3map["ap-northeast-1"] = "s3://aktana-bdpjpeks-glue/"

class TargetTable:
    def __init__(self, name, database_type, temp_table):
        self.name = name
        self.database_type = database_type
        self.temp_table = temp_table

targetTables = []


class MetaDB:
    def __init__(self, host, user, password, database=""):
        self.dbconn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )

    def get_customer_rds(self, customer, env):
        sql = """
        select  substring(c.regionName,1,2) as region, c.customerName as customer, case when c.regionName = 'cn' then 'cnbastion.aktanachina.com' when ce.rdsForwardUrl is NOT NULL then ce.rdsForwardUrl  else concat('bdp',c.regionName,'001.aktana.com') end rdsHost, case when ce.rdsForwardPort is not null then ce.rdsForwardPort else 33000+c.customerId end  as rdsPort, 'appadmin' as rdsUser,  c.rdsUserPassword, ce.enginedbName as databaseName, ce.copystormdbName as databaseName_cs, ce.stagedbName as databaseName_stage, c.customerId as customerid
        from Customer c inner join CustomerEnvironment ce on c.customerName = %s and c.customerId = ce.customerId
        and ce.envName = %s where c.isActive = 1 and c.customerId < 1000 and c.customerDescription not like '%Parent%' order by c.`regionName`
        """
        cursor = self.dbconn.cursor()
        cursor.execute(sql, [customer, env])
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        results = []
        for rds in records:
            row = { 'region': rds[0], 'customer': rds[1], 'rdshost': rds[2], 'rdsport': rds[3], 'rdsuser': rds[4], 'rdspassword' : rds[5], 'database': rds[6], 'database_cs': rds[7], 'database_stage': rds[8], 'customerid': rds[9] }
            results.append(row)

        return results

    def get_company_rds(self, company, env):
        sql = """
        select  substring(c.regionName,1,2) as region, c.customerName as customer, case when c.regionName = 'cn' then 'cnbastion.aktanachina.com'  when ce.rdsForwardUrl is NOT NULL then ce.rdsForwardUrl  else concat('bdp',c.regionName,'001.aktana.com') end rdsHost, case when ce.rdsForwardPort is not null then ce.rdsForwardPort else 33000+c.customerId end  as rdsPort, 'appadmin' as rdsUser,  c.rdsUserPassword, ce.enginedbName as databaseName, ce.copystormdbName as databaseName_cs, ce.stagedbName as databaseName_stage, c.customerId as customerid
        from Customer c inner join CustomerEnvironment ce on c.customerName like %s and c.customerId = ce.customerId
        and ce.envName = %s where c.isActive = 1 and c.customerId < 1000 and c.customerDescription not like '%Parent%' order by c.`regionName`
        """
        cursor = self.dbconn.cursor()
        cursor.execute(sql, ['%'+company+'%'], env)
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        results = []
        for rds in records:
            row = { 'region': rds[0], 'customer': rds[1], 'rdshost': rds[2], 'rdsport': rds[3], 'rdsuser': rds[4], 'rdspassword' : rds[5], 'database': rds[6], 'database_cs': rds[7], 'database_stage': rds[8], 'customerid': rds[9] }
            results.append(row)

        return results

class CustomerDB:
    def __init__(self, host, user, password, database="", port="3306"):
        print(database)
        self.dbconn = mysql.connector.connect(
            host=host,
            user=user,
            port=port,
            password=password,
            database=database
        )

    def get_customer_sql(self, sql):
        cursor = self.dbconn.cursor()
        cursor.execute("SET SESSION group_concat_max_len = 10000000;")
        print(sql)
        cursor.execute(sql, [])
        fields = cursor.description
        names = [ x[0] for x in cursor.description]
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        return pd.DataFrame(records, columns=names)
        #return fields, records

    def fill_na(self, df):
        for c in df:
            if str(df[c].dtype) in ('object', 'string_', 'unicode_'):
                  df[c].fillna(value='', inplace=True)
        return df

    def get_table(self, database_name, table_name):
        sql = f"SELECT * from {database_name}.{table_name}"
        df = self.get_customer_sql(sql)
        df = self.fill_na(df)
        return df

def get_main_arguments():
    parser = argparse.ArgumentParser(description='Copy Temporary Data arguments')
    parser.add_argument('--action', required=True)
    parser.add_argument('--env', required=False, default='prod')
    parser.add_argument('--customer', required=True)
    parser.add_argument('--company', required=False)
    parser.add_argument('--region', required=False)
    parser.add_argument('--awsregion', required=True)
    parser.add_argument('--devsuffix', required=True)
    parser.add_argument('--dbmetahost', required=True)
    parser.add_argument('--dbmetauser', required=True)
    parser.add_argument('--dbmetapass', required=True)
    parser.add_argument('--sourceTablesList', required=True, nargs='*', help="List of Target Tables in format: schema.table_name:temp_table_name")
    return parser.parse_args()

def get_s3staging(customer, awsregion, devsuffix):
    s3bucket = s3map[awsregion]
    s3bucket = s3bucket + "/" + "athena" + "/" + "customer"
    return s3bucket;

def get_schema(customer, devsuffix):
    schema = ""
    if (devsuffix == ""):
       schema = customer + "_prod_" + "current"
    else:
       schema = "impact_" + customer + "_current_" + devsuffix

    return schema

def get_s3target(customer, awsregion, devsuffix):
    s3target = ""
    if (devsuffix == ""):
       s3target = s3map[awsregion]
       s3target = s3target + "dbt" + "/tempdata/" + customer + "/" + "prod" + "/"
#      s3target = "s3://aktana-bdp-" + customer + "/prod/tempdata/"
    else:
       s3target = s3map[awsregion]
       s3target = s3target + "dbt" + "/tempdata/" + customer + "/" + devsuffix + "/"
    return s3target

def main():
    args = get_main_arguments()
    print(f"Input parameters: {args}")

    for i in range(len(args.sourceTablesList)):
        targetTables.append(TargetTable(args.sourceTablesList[i].split(':')[0].split('.')[1], args.sourceTablesList[i].split(':')[0].split('.')[0], args.sourceTablesList[i].split(':')[1]))

    # get job list need to analyze
    rds_info = []

    if (args.action == 'tempcopy'):
        #pass info somehow to this connection
        matadb2 = MetaDB(args.dbmetahost, args.dbmetauser, args.dbmetapass, 'aktanameta')
        if args.customer != "":
            rds_info = matadb2.get_customer_rds(args.customer, args.env)
        elif args.region != "":
            rds_info = matadb2.get_region_rds(args.region)
        else:
            rds_info = matadb2.get_all_rds()

    dbtype = "database_stage"

    print(rds_info)

    for row in rds_info:
        print(row['customer'])
        customer = row['customer']
        s3target = get_s3target(customer, args.awsregion, args.devsuffix)
        tgtschema = get_schema(customer, args.devsuffix)

        try:
            print(row)
            cdb = CustomerDB(row['rdshost'], row['rdsuser'], row['rdspassword'], row[dbtype],  row['rdsport'])

            for tab in targetTables:
                try:
                    df_table = cdb.get_table(row[tab.database_type], tab.name)
                    if 'LastModifiedDate' in df_table.columns:
                        df_table['LastModifiedDate'] = df_table['LastModifiedDate'].astype(str)
                    print(df_table.count())
                    wr.s3.to_csv(df=df_table,
                                 mode="overwrite",
                                 index=False,
                                 dataset=True,
                                 path=s3target + tab.name,
                                 sep=",",
                                 na_rep='',
                                 database=tgtschema,
                                 table=tab.temp_table)
                except Exception as ex:
                    print(ex)
                    print(row['customer'] + ' ' + tab.name + ' skipped')

        except Exception as e:
            print (e)
            print (row['customer'] + ' skipped')

if __name__ == "__main__":
    main()