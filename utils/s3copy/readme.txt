1. create a virtual environment using
   mkdir -p capacity-plan
   cd capacity-plan
   virtualenv -p python venv

2. envsetup.sh - initializes the virtualenv and some credentials

3. create_dsecore_region.sh is scheduled to populate dsecore database from local databases

4. create_dsecorearchive.sh snapshots dsecore to dsecore-archive schema

5. create_capacity_region.sh captures capacity details from different databases

6. create_viewanalysis_region.sh captures view details from different databases
~
