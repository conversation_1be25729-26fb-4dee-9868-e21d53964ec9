import mysql.connector
import pprint
import os

# Global variables
rdshost=os.environ.get("RDS_HOST")
rdsuser=os.environ.get("RDS_USERNAME")
rdspwd=os.environ.get("RDS_PASSWORD")

metadatahost=os.environ.get("METADATA_HOST")
metadatauser=os.environ.get("METADATA_USERNAME")
metadatapwd=os.environ.get("METADATA_PASSWORD")

customer = os.environ.get('CUSTOMER')
environment = os.environ.get('ENVIRONMENT')

def mysql_connection(vhost,vusr,vpwd,vdb):
    db = mysql.connector.connect(
    host=vhost,
    user=vusr,
    password=vpwd,
    database=vdb)

    print(f"Connected to {db}\n")   
    return db 

def extract_data(sql,vhost,vusr,vpwd,vdb):
    db = mysql_connection(vhost,vusr,vpwd,vdb)
    print(db)
    cursor = db.cursor(dictionary=True)
    print(f"\nExecuting following query \n {sql}")
    cursor.execute(sql)
    result = cursor.fetchall()
    pp = pprint.PrettyPrinter(indent=4)
    print("\nFollowing rows are returned by the query : \n")
    pp.pprint(result)
    return result

def main():
    print("\n ######################################### \n")
    print(" Start main() \n")
    init_metrics_yml = "\nversion: 2\n\nmetrics:"
    init_metrics_filter_yml = "    filters: \n"
    ymldata = ""
    
    metadata_sql_string = """
    select sccdbName  from CustomerEnvironment ce 
    where ce.customerId = (select customerId from Customer where customerName = '{customer}')
    and envName = '{environment}';
    """
    print("\nConnect to metadata database and extract rds database name\n")
    metadata_result = extract_data(metadata_sql_string.format(customer=customer,environment=environment),metadatahost,metadatauser,metadatapwd,"aktanameta")
    rdsdb=metadata_result[0]['sccdbName']
    
    sql_string = """ 
    
    SELECT kt.modelType,
           kt.kpiTypeName,
           kt.kpiTypeLabel,
           kt.calculationMethod,
           kt.formula,
           kt.filterCondition,
           kt.description 
    FROM KPIType kt
    WHERE UPPER(kt.modelType) = 'EVENT' ;
    
    """  
    
    print("\nConnect to database and extract data\n")
    kpiTypes = extract_data(sql_string,rdshost,rdsuser,rdspwd,rdsdb)

    CUR_DIR = os.getcwd()
    print(f"\nCurrent directory is {CUR_DIR}\n")
    
    with open(r'./models/intermediate/kpievent/event_metrics.yml.template', 'r') as file: 
        metricsTemplateFile = file.read()
    file.close()
    
    with open(r'./models/intermediate/kpievent/event_metrics_filter.yml.template', 'r') as file: 
        metricsFilterTemplateFile = file.read()
    file.close()
    
    for kpiType in kpiTypes:
        if kpiType["kpiTypeLabel"] == 'calls_completed':
            product_dim=""
            kpimodel="kpi_event_interaction_factorlevel_v"
        elif kpiType["kpiTypeLabel"].startswith('sales_'):
            product_dim = "\n      - product_id\n      - product_uid\n      - product_name"
            kpimodel="kpi_event_sales_data"
        elif kpiType["kpiTypeLabel"].startswith('apmetric_'):
            product_dim = "\n      - product_id\n      - product_uid\n      - product_name"
            kpimodel="kpi_event_apmetric_data"
        else:
            product_dim = "\n      - product_id\n      - product_uid\n      - product_name"
            kpimodel="kpi_event_interaction_explode"
        data = metricsTemplateFile.format(kpiTypeName=kpiType["kpiTypeName"], 
                                          kpiTypeLabel=kpiType["kpiTypeLabel"],
                                          description=kpiType["description"],
                                          kpiCalculation=kpiType["calculationMethod"],
                                          kpiExpression=kpiType["formula"],
                                          kpiProductDim=product_dim,
                                          kpiModel=kpimodel)
        filterdata = ""
        if kpiType["kpiTypeLabel"] == 'journey_progression':
            data = data + "      - eventlabel\n"
        if bool(kpiType.get("filterCondition")):
            print("Filter exists for kpiTypeLabel " + kpiType["kpiTypeLabel"] + " : " + kpiType["filterCondition"])
            for filterString in  kpiType["filterCondition"].split(";"):
                filterdata = filterdata + metricsFilterTemplateFile.format(filterField = filterString.split("|")[0],
                                                              filterOperator = filterString.split("|")[1],
                                                              filterValue = filterString.split("|")[2])
            filterdata =  init_metrics_filter_yml + filterdata   
        
        ymldata = ymldata + data + filterdata + "\n"
 
    print("\ncreating metrics yml from template \n")
    with open(r'./models/intermediate/kpievent/metrics.yml', 'w') as file: 
        ymldata = init_metrics_yml + ymldata
        file.write(ymldata)
    file.close()   
    print("Completed creating metrics.yml file")  
    print(ymldata)

    
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Exception from main : {e}")
        exit(1)
