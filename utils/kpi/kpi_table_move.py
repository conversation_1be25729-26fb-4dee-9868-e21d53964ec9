import getopt
import sys
import os
import boto3
import pandas as pd
from datetime import datetime
from pyathena import connect
from pyathena.pandas.cursor import PandasCursor
from io import BytesIO
from sqlalchemy import create_engine
import mysql.connector
from sqlalchemy.types import *


def get_s3staging(customer, region, devsuffix):
    s3_staging_dir = "s3://aktana-bdp" + region + "-glue/athena/" + customer + '/' + devsuffix
    return s3_staging_dir

def get_schema(customer, suffix):
    schema = "impact_" + customer + "_" + suffix
    return schema

def get_table(conn, sql):
    df = pd.read_sql_query(sql, conn)
    return df

class CustomerDB:
    def __init__(self, host, port, user, password, database=""):
        self.dbconn = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )

    def execute_operation(self, operation):
        cursor = self.dbconn.cursor()
        cursor.execute(operation)
        cursor.close()

class MetaDB:
    def __init__(self, host, user, password, database=""):
        self.dbconn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )
    
    def get_customer_rds(self, customer, env):
        sql = """
        select
            substring(c.regionName,1,2) as region,
            c.customerName as customer,
            ce.rdsServer as rdsHost,
            3306 as rdsPort,
            'appadmin' as rdsUser,
            ce.rdsUserPassword,
            ce.sccdbName as sccdbName,
            c.customerId as customerid
        from Customer c
        inner join CustomerEnvironment ce
            on c.customerName = %s
           and c.customerId = ce.customerId
           and ce.envName = %s
        where c.isActive = 1
        """
        cursor = self.dbconn.cursor()
        cursor.execute(sql, [customer, env])
        records = cursor.fetchall()
        print("Total number of rows: ", cursor.rowcount)
        cursor.close()
        results = []
        for rds in records:
            row = { 'region': rds[0], 'customer': rds[1], 'rdshost': rds[2], 'rdsport': rds[3], 'rdsuser': rds[4], 'rdspassword' : rds[5], 'database': rds[6], 'customerid': rds[7] }
            results.append(row)

        return results

def main():
    source_table_name = context.arguments.get('source_table_name')
    target_table_name = context.arguments.get('target_table_name')
    post_load_operations = context.arguments.get('post_load_operations')
    
    customer = os.environ.get('CUSTOMER')
    environment = os.environ.get('ENVIRONMENT')
    devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
    region = os.environ.get('REGION')
    
    aws_access_key = os.environ.get('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
    aws_session_token = os.environ.get('AWS_SESSION_TOKEN')
    aws_region = os.environ.get('AWS_REGION')

    s3_staging = get_s3staging(customer, region, devsuffix or environment)
    src_schema = get_schema(customer, devsuffix)

    boto3.setup_default_session(region_name = aws_region)
    global conn
    conn = connect(aws_access_key_id=aws_access_key, \
                aws_secret_access_key=aws_secret_key, \
                aws_session_token=aws_session_token, \
                s3_staging_dir=s3_staging, \
                region_name=aws_region, \
                schema_name=src_schema, \
                )

    matadb = MetaDB(os.environ.get("METADATA_HOST"), \
                os.environ.get("METADATA_USER"), \
                os.environ.get("METADATA_PWD"), \
                'aktanameta')


    rds_info = matadb.get_customer_rds(customer, environment)

    
    
    schema_df = get_table(conn, f"""SELECT column_name, data_type
                                    FROM information_schema.columns
                                    WHERE table_name = '{source_table_name}'
                                      AND table_schema = '{src_schema}'
                                   ORDER BY ordinal_position asc""")
    
    df_schema = {}
    
    print(f'[Info] Starting Data Type Conversion')

    for index, row in schema_df.iterrows():
        if row['data_type'] == "integer":
            data_type = INTEGER
        elif row['data_type'] == "bigint":
            data_type = BIGINT
        elif row['data_type'].find("varchar") >= 0:
            if row['column_name'].lower() in ("productuid", "repteamuid", "repuid", "countrycode"):
                data_type = VARCHAR(50)
            elif row['column_name'].lower() == "additionalsetting":
                data_type = VARCHAR(200)
            else:
                data_type = VARCHAR(256)
        elif row['data_type'] == "date":
            data_type = DATE
        elif row['data_type'] in ("double", "float", "real") or row['data_type'].find("decimal") >= 0:
            data_type = DECIMAL(20,5)
        elif row['data_type'].find("time") >= 0:
            data_type = TIMESTAMP
        else:
            data_type = TEXT
        
        print(f'[Info] Column Name: {row["column_name"]}, Original Data Type: {row["data_type"]}, Converted Data Type: {data_type}')
        
        df_schema[row['column_name']] = data_type
    
    # Connect to RDS
    conn_str = f'mysql+pymysql://{rds_info[0]["rdsuser"]}:{rds_info[0]["rdspassword"]}@{rds_info[0]["rdshost"]}:{rds_info[0]["rdsport"]}/{rds_info[0]["database"]}'
    engine = create_engine(conn_str)
    print(f'[Info] Connection to RDS host {rds_info[0]["rdshost"]}', flush=True)
    
    cursor = connect(aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                aws_session_token=aws_session_token,
                s3_staging_dir=s3_staging,
                region_name=aws_region,
                schema_name=src_schema,
                 cursor_class=PandasCursor).cursor()
    
    print(f"[Info] Starting data read {datetime.now()}", flush=True)
    
    source_df = cursor.execute(f"select * from {source_table_name}", chunksize=500_000).as_pandas()

    print(f"[Info] Ending data read {datetime.now()}", flush=True)

    chunk_indx = 0
    for df in source_df:
        # Write the DataFrame to RDS
        print(f"[Info] Starting processing chunk {chunk_indx}: {datetime.now()}", flush=True)
        df.to_sql(target_table_name,
                         con=engine,
                         if_exists='replace' if chunk_indx == 0 else 'append',
                         index=False,
                         dtype=df_schema)
        chunk_indx+=1
    # Closing the connection
    engine.dispose()

    if post_load_operations:
        custDB = CustomerDB(rds_info[0]["rdshost"], \
                        rds_info[0]["rdsport"], \
                        rds_info[0]["rdsuser"], \
                        rds_info[0]["rdspassword"], \
                        rds_info[0]["database"])
        for i in range(len(post_load_operations)):
            custDB.execute_operation(post_load_operations[i])

if __name__ == "__main__":
    main()