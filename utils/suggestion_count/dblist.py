import boto3
from string import Template

#client = boto3.client('glue',region_name='us-east-1')
#client = boto3.client('glue',region_name='eu-central-1')
client = boto3.client('glue',region_name='ap-northeast-1')

responseGetDatabases = client.get_databases()

databaseList = responseGetDatabases['DatabaseList']

for databaseDict in databaseList:
    template = Template('''
    CREATE EXTERNAL TABLE `${customer}_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-${customer}/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-${customer}/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=$${updatedatyear}/updatedatmonth=$${updatedatmonth}/updatedatday=$${updatedatday}/'    
    );
    ''');
    databaseName = databaseDict['Name']
    if ("_prod" in databaseName) and ("_historical" not in databaseName) and ("_impact" not in databaseName):
#       print('\ndatabaseName: ' + databaseName)
        dbnamearr = databaseName.split('_');
        print('drop table ' + dbnamearr[0] + '_rpt_suggestion_delivered_stg')
        print(template.substitute(customer=dbnamearr[0]))
