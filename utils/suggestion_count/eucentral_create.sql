drop table astellasit_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `astellasit_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-astellasit/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-astellasit/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table bayerit_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `bayerit_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-bayerit/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-bayerit/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table biogeneu_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `biogeneu_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-biogeneu/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-biogeneu/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table biogenit_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `biogenit_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-biogenit/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-biogenit/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table leoeu_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `leoeu_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-leoeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-leoeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table msdde_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `msdde_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-msdde/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-msdde/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table msdfr_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `msdfr_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-msdfr/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-msdfr/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table msdgb_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `msdgb_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-msdgb/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-msdgb/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table msdit_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `msdit_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-msdit/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-msdit/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisde_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisde_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisde/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisde/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartises_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartises_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartises/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartises/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisfr_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisfr_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisfr/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisfr/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisit_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisit_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisit/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisit/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisuk_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisuk_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisuk/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisuk/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novonordiskeu_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novonordiskeu_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novonordiskeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novonordiskeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table pfizerde_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `pfizerde_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-pfizerde/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-pfizerde/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table pfizereu_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `pfizereu_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-pfizereu/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-pfizereu/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table pfizeruk_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `pfizeruk_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-pfizeruk/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-pfizeruk/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table trainingeu_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `trainingeu_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-trainingeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-trainingeu/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
