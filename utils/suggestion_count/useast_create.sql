drop table almirallus_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `almirallus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-almirallus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-almirallus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table antaresus_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `antaresus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-antaresus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-antaresus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table axsomeus_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `axsomeus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-axsomeus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-axsomeus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    

drop table biogenap_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `biogenap_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-biogenap/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-biogenap/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table biogeneu2_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `biogeneu2_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-biogeneu2/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-biogeneu2/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table biogenna_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `biogenna_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-biogenna/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-biogenna/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table emdseronous_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `emdseronous_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-emdseronous/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-emdseronous/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table genentechca_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `genentechca_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-genentechca/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-genentechca/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    

drop table genzymeus_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `genzymeus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-genzymeus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-genzymeus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    

drop table leous_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `leous_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-leous/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-leous/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    

    
drop table novartisbr_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisbr_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisbr/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisbr/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisca_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisca_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisca/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisca/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
drop table novartisus_rpt_suggestion_delivered_stg

    CREATE EXTERNAL TABLE `novartisus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-novartisus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-novartisus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );
    
