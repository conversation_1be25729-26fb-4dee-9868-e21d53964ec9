DROP  TABLE tempdata_AKT_Accounts;

CREATE EXTERNAL TABLE tempdata_AKT_Accounts
(
 `account_vod__c` varchar(18),
  `customerid` varchar(20),
  `lastmodifieddate` varchar(20),
  `configcountrycode` varchar(255),
  `accounttypename` varchar(60)
)
ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'
LOCATION 's3://aktana-bdp-genzymeus/prod/tempdata/AKT_Accounts'
TBLPROPERTIES ('skip.header.line.count'='1')
;

DROP  TABLE tempdata_AKT_ProductCatalog;

CREATE EXTERNAL TABLE tempdata_AKT_ProductCatalog
(
  `id` varchar(18),
  `name` varchar(80),
  `product_type_vod__c` varchar(255),
  `external_id_vod__c` varchar(120),
  `isactive` INT,
  `iscompetitor` INT,
  `configcountrycode` varchar(255)
)
ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'
LOCATION 's3://aktana-bdp-genzymeus/prod/tempdata/AKT_ProductCatalog/'
TBLPROPERTIES ('skip.header.line.count'='1')
;


DROP  TABLE tempdata_AKT_ProductMap;

CREATE EXTERNAL TABLE tempdata_AKT_ProductMap
(
  `parent_product` varchar(50),
  `child_product` varchar(50),
  `diagnosis_group` varchar(50)
)
ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'
LOCATION 's3://aktana-bdp-genzymeus/prod/tempdata/AKT_ProductMap/'
TBLPROPERTIES ('skip.header.line.count'='1')
;


DROP  TABLE ext_sales_snsob_ad_core_wk;

CREATE EXTERNAL TABLE ext_sales_snsob_ad_core_wk
    (
 `CLIENT_NUMBER` varchar(15),
  `REPORT_NUMBER` varchar(15),
  `CHANNEL_INDICATOR` varchar(15),
  `PRESC_ID` varchar(15),
  `FLEXIBLE_FIELD_1` varchar(15),
  `SPECIALTY_ABBREVIATION` varchar(15),
  `FLEXIBLE_FIELD_8` varchar(15),
  `DIAGNOSIS_GROUP` varchar(100),
  `FLEXIBLE_FIELD_2` varchar(15),
  `FLEXIBLE_FIELD_3` varchar(15),
  `FLEXIBLE_FIELD_4` varchar(15),
  `FLEXIBLE_FIELD_5` varchar(15),
  `FLEXIBLE_FIELD_6` varchar(15),
  `FLEXIBLE_FIELD_7` varchar(15),
  `PROD_GRP` varchar(50),
  `WEEK_ID` varchar(50),
  `XPO_NRX` double,
  `XPO_TRX` double,
  `XPO_UNKNRX` double,
  `XPO_UNKTRX` double,
  `XPO_RRX` double,
  `XPO_UNKRRX` double,
  `XPO_NTS` double,
  `XPO_CN` double,
  `XPO_SW` double,
  `XPO_AO` double,
  `RSN` double,
  `RD` double, 
  `XPO_CR` double,
  `RSR` double,
  `XPO_SWF` double,
  `RDF` double,
  `XPO_AT` double,
  `NBRX` double,
  `DRX` double,
  `CURR_CCT_ID` varchar(10),
  `FRZ_CCT_ID` varchar(10),
  `FRZ_CCT_ID_QTR` varchar(10)
)
ROW FORMAT DELIMITED
      FIELDS TERMINATED BY '|'
      ESCAPED BY '\\'
      LINES TERMINATED BY '\n'
LOCATION 's3://aktana-externalfiles-sanofius/prod/repository/20221015/sales/ad/'
TBLPROPERTIES ('skip.header.line.count'='1')
;

DROP  TABLE ext_sales_snsob_as_core_wk;


CREATE EXTERNAL TABLE ext_sales_snsob_as_core_wk
    (
 `CLIENT_NUMBER` varchar(15),
  `REPORT_NUMBER` varchar(15),
  `CHANNEL_INDICATOR` varchar(15),
  `PRESC_ID` varchar(15),
  `FLEXIBLE_FIELD_1` varchar(15),
  `SPECIALTY_ABBREVIATION` varchar(15),
  `FLEXIBLE_FIELD_8` varchar(15),
  `DIAGNOSIS_GROUP` varchar(100),
  `FLEXIBLE_FIELD_2` varchar(15),
  `FLEXIBLE_FIELD_3` varchar(15),
  `FLEXIBLE_FIELD_4` varchar(15),
  `FLEXIBLE_FIELD_5` varchar(15),
  `FLEXIBLE_FIELD_6` varchar(15),
  `FLEXIBLE_FIELD_7` varchar(15),
  `PROD_GRP` varchar(50),
  `WEEK_ID` varchar(50),
  `XPO_NRX` double,
  `XPO_TRX` double,
  `XPO_UNKNRX` double,
  `XPO_UNKTRX` double,
  `XPO_RRX` double,
  `XPO_UNKRRX` double,
  `XPO_NTS` double,
  `XPO_CN` double,
  `XPO_SW` double,
  `XPO_AO` double,
  `RSN` double,
  `RD` double, 
  `XPO_CR` double,
  `RSR` double,
  `XPO_SWF` double,
  `RDF` double,
  `XPO_AT` double,
  `NBRX` double,
  `DRX` double,
  `CURR_CCT_ID` varchar(10),
  `FRZ_CCT_ID` varchar(10),
  `FRZ_CCT_ID_QTR` varchar(10)
)
ROW FORMAT DELIMITED
      FIELDS TERMINATED BY '|'
      ESCAPED BY '\\'
      LINES TERMINATED BY '\n'
LOCATION 's3://aktana-externalfiles-sanofius/prod/repository/20221015/sales/as/'
TBLPROPERTIES ('skip.header.line.count'='1')

CREATE EXTERNAL TABLE tempdata_RPT_Rep_Engagement_Segmentation
(
  `segment_id` varchar(255),
  `repid` int,
  `repuid` varchar(20),
  `repname` varchar(50),
  `configname` varchar(60),
  `periodvalue` varchar(20),
  `engagementrate` double,
  `engagementsegment` varchar(60),
  `createdat` varchar(20),
  `updatedat` varchar(20)
)  
ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'
LOCATION 's3://aktana-bdp-genzymeus/prod/tempdata/RPT_Rep_Engagement_Segmentation/'
TBLPROPERTIES ('skip.header.line.count'='1')
;

CREATE EXTERNAL TABLE tempdata_MessageToMessageTopicMap
(
 `messageId` bigint,
  `externalId` varchar(40),
  `productId` bigint,
  `messageChannelId` bigint,
  `messageName` varchar(255),
  `messageDescription` varchar(255),
  `configCountryCode` varchar(255),
  `channelId` bigint,
  `messageTopic` varchar(255)
)
ROW FORMAT SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'
LOCATION 's3://aktana-bdp-genzymeus/prod/tempdata/MessageToMessageTopicMap/'
TBLPROPERTIES ('skip.header.line.count'='1');



DROP  TABLE ext_sales_akt_trxnrx_data;

CREATE EXTERNAL TABLE ext_sales_akt_trxnrx_data
    (
  `customerId` varchar(10),
  `productName` varchar(25),
  `prev5to13w_TRx_akt` double ,
  `cur4w_TRx_akt` double ,
  `prev4w_TRx` double ,
  `cur13w_TRx_akt` double ,
  `prev13w_TRx` double ,
  `cur1w_TRx_akt` double ,
  `prev2to104w_TRx_akt` double ,
  `prev7to14w_TRx_akt` double ,
  `cur6w_TRx_akt` double
)
PARTITIONED BY (
  `filedate` string
)
ROW FORMAT DELIMITED
      FIELDS TERMINATED BY '|'
      ESCAPED BY '\\'
      LINES TERMINATED BY '\n'
LOCATION 's3://aktana-externalfiles-sanofius/prod/repository/sales/AKT_TRXNRX_DATA/'
TBLPROPERTIES ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='********,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS');

DROP  TABLE ext_sales_akt_nbrx_data;

CREATE EXTERNAL TABLE ext_sales_akt_nbrx_data
    (
  `customerid` varchar(10),
  `productname` varchar(25),
  `cur4w_nbrx_akt` double ,
  `cur13w_nbrx_akt` double ,
  `cur1w_nbrx_akt` double ,
  `prev2_104w_nbrx_akt` double,
  `prev5_13w_nbrx_akt` double
)
PARTITIONED BY (
  `filedate` string
)
ROW FORMAT DELIMITED
      FIELDS TERMINATED BY '|'
      ESCAPED BY '\\'
      LINES TERMINATED BY '\n'
LOCATION 's3://aktana-externalfiles-sanofius/prod/repository/sales/AKT_NBRX_DATA/'
TBLPROPERTIES ('skip.header.line.count'='1', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='********,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')



CREATE EXTERNAL TABLE `param_msrscenario`(
  `uid` string COMMENT 'from deserializer', 
  `name` string COMMENT 'from deserializer', 
  `type` string COMMENT 'from deserializer', 
  `period_definitions` array<struct<uid:string,name:string,yearquarter_begin:string,yearquarter_end:string,dt_begin:string,dt_end:string>> COMMENT 'from deserializer', 
  `repaccountassignment_asofdate` string COMMENT 'from deserializer', 
  `interaction_events` array<string> COMMENT 'from deserializer', 
  `usecase_level_interactions` array<string> COMMENT 'from deserializer', 
  `topic_level_interactions` array<string> COMMENT 'from deserializer', 
  `conversion_events` array<string> COMMENT 'from deserializer',
  `analyze_driven_events` boolean,
  `analyze_aligned_events` boolean,
  `factorusecasename_map_override` array<struct<factor_name:string,usecase_name:string>>,
  `remove_same_state_transitions` boolean)
ROW FORMAT SERDE 
  'org.openx.data.jsonserde.JsonSerDe' 
LOCATION
  's3://aktana-bdp-genzymeus/prod/tempdata/MSRScenario';


CREATE EXTERNAL TABLE suggestion_candidates
(
  `id` string, 
  `actorid` struct<id:int,externalId:string>, 
  `accountid` struct<id:int,externalId:string>, 
  `channelinfo` struct<channel:struct<id:int,externalId:string>,actionType:struct<id:int,externalId:string>,actorType:struct<id:int,externalId:string,name:string>>, 
  `isoptimizedbyai` boolean, 
  `suggesteddate` string, 
  `products` array<struct<productId:struct<id:int,externalId:string>,factorUID:string,detailedRepActionType:struct<id:int,externalId:string>,messageInfo:struct<messageSet:struct<id:int,externalId:string>,messageTopic:struct<id:int,externalId:string>,messages:array<struct<id:int,externalId:string>>>>>, 
  `source` struct<engine:string,type:string>, 
  `accountimportance` struct<isCritical:boolean,score:double>, 
  `suggestionimportance` struct<isCritical:boolean,score:double>, 
  `dismissalfollowuptype` string, 
  `primaryfactor` struct<factorId:string,name:string,order:int,tags:array<struct<tagId:int,tagName:string,params:array<struct<tagParamId:int,tagParamName:string,value:string>>>>,segmentFactorIds:array<string>>, 
  `secondaryfactors` array<struct<factorId:string,name:string,order:int,tags:array<struct<tagId:int,tagName:string,params:array<struct<tagParamId:int,tagParamName:string,value:string>>>>,segmentFactorIds:array<string>>>, 
  `dco` struct<channelPrefScore:double,preferred:boolean>, 
  `metadata` struct<runDate:string,configId:int,configName:string,configVersionId:int,runGroupId:int,runUID:string,countryCode:string>, 
  `reasons` array<struct<crmFieldName:string,reasonRank:int,text:string,factorUID:string>>, 
  `suggestedinsight` struct<text:string,rank:int,isCritical:boolean>, 
  `suggestedenhancedinsight` struct<text:string,rank:int,isCritical:boolean>, 
  `suggestionreference` struct<suggestionReferenceId:string,internalSuggestionReferenceId:string>, 
  `surveyuid` string, 
  `suggestiondeliverymode` struct<id:int,externalId:string>
)
PARTITIONED BY (
  `rundate` string,
  `configid` string
)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
LOCATION 's3://aktana-bdp-genzymeus/env=prod/datatype=suggestionCandidates/'
TBLPROPERTIES ( 
'projection.rundate.type'='date','projection.enabled'='true',
'projection.rundate.range'='2022-01-01,NOW','projection.rundate.format'='yyyy-MM-dd',
'projection.rundate.interval'='1','projection.rundate.interval.unit'='DAYS',
'projection.configid.type'='integer', 'projection.configid.range'='1,10',
'projection.configid.format'='yyyy-MM-dd','projection.configid.interval'='1',
'storage.location.template' = 's3://aktana-bdp-genzymeus/env=prod/datatype=suggestionCandidates/rundate=${rundate}/configid=${configid}'
)




/* Sample


    CREATE EXTERNAL TABLE `almirallus_rpt_suggestion_delivered_stg`(
      `externalid` string, 
      `suggestionlifecycleid` bigint, 
      `runid` bigint, 
      `runuid` string, 
      `repteamid` int, 
      `repteamuid` string, 
      `repteamname` string, 
      `seconfigid` int, 
      `seconfigname` string, 
      `rungroupid` int, 
      `suggesteddate` date, 
      `startdatelocal` date, 
      `repid` int, 
      `repuid` string, 
      `repname` string, 
      `repcreatedat` timestamp, 
      `accountid` int, 
      `accountuid` string, 
      `runrepdatesuggestionid` string, 
      `accountname` string, 
      `detailrepactiontypeid` int, 
      `detailrepactiontypeuid` string, 
      `detailrepactionname` string, 
      `runrepdatesuggestiondetailid` string, 
      `productid` int, 
      `productuid` string, 
      `productname` string, 
      `messageid` int, 
      `messageuid` string, 
      `messagename` string, 
      `suggestionuid` string, 
      `suggestionreferenceid` string, 
      `lastviewedat` timestamp, 
      `viewedat` timestamp, 
      `viewedduration` bigint, 
      `actiontaken` string, 
      `actiontaken_dt` string, 
      `issuggestioncompleted` int, 
      `issuggestioncompleteddirect` int, 
      `issuggestioncompletedinfer` int, 
      `issuggestiondismissed` int, 
      `dismissreasontype` string, 
      `dismissreason` string, 
      `dismissreason_dt` string, 
      `issuggestionactive` int, 
      `facilitygeolocationstring` string, 
      `replatitude` double, 
      `replongitude` double, 
      `territorycityname` string, 
      `districtname` string, 
      `territoryid` string, 
      `territoryname` string, 
      `regionname` string, 
      `regiongroup` string, 
      `dismissedat` timestamp, 
      `dismisscount` bigint, 
      `lastpublishedat` timestamp, 
      `createdat` timestamp, 
      `updatedat` timestamp, 
      `reportedinteractionuid` string, 
      `inferredinteractionuid` string, 
      `interactionuid` string, 
      `completedat` timestamp, 
      `inferredat` timestamp, 
      `isfirstsuggesteddate` int, 
      `islastsuggesteddate` int, 
      `suggestiondriver` string, 
      `timeoffday` date, 
      `ishighestrunidforday` int, 
      `holiday_weekend_flag` string, 
      `crmfieldname` string, 
      `reasontext` string, 
      `reasonrank` int, 
      `runrepdatesuggestionreasonid` string, 
      `reprole` string, 
      `repbag` string, 
      `dmuid` string, 
      `dmname` string, 
      `interactionid` int, 
      `iscompleted` int, 
      `startdatetime` timestamp, 
      `isremix` string, 
      `isdsespark` int, 
      `internalsuggestionreferenceid` string, 
      `arc_createdat` timestamp, 
      `arc_updatedat` timestamp)
    PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
    ROW FORMAT SERDE 
      'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
    STORED AS INPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
    OUTPUTFORMAT 
      'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
    LOCATION
      's3://aktana-bdp-almirallus/prod/data/archive_rds/rpt_suggestion_delivered_stg/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-almirallus/prod/data/archive_rds/rpt_suggestion_delivered_stg/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );

*/


CREATE EXTERNAL TABLE `xc_call2_vod__c`(
  `id` string, 
  `ownerid` string, 
  `isdeleted` boolean, 
  `account_vod__c` string,
  `parent_address_vod__c` string,
  `call_date_vod__c` date, 
  `call_datetime_vod__c` timestamp,
  `status_vod__c` string,
  `call_channel_vod__c` string,
  `call_type_vod__c` string
)
PARTITIONED BY ( 
  `sysmodstampyear` string, 
  `sysmodstampmonth` string, 
  `sysmodstampday` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
LOCATION
  's3://aktana-bdp-pfizerus/prod/data/archive_rds/call2_vod__c/'
TBLPROPERTIES (
        'projection.sysmodstampyear.type'='integer','projection.enabled'='true','projection.sysmodstampyear.range'='2018,2022',
        'projection.sysmodstampmonth.type'='integer',
        'projection.sysmodstampmonth.range'='1,12',
        'projection.sysmodstampday.type'='integer',
        'projection.sysmodstampday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-pfizerus/prod/data/archive_rds/call2_vod__c/sysmodstampyear=${sysmodstampyear}/sysmodstampmonth=${sysmodstampmonth}/sysmodstampday=${sysmodstampday}/'    
)



CREATE EXTERNAL TABLE `pfizerus_facility`(
  `facilityid` integer, 
  `externalid` string, 
  `latitude` double, 
  `longitude` double,
  `geolocationstring` string
)
PARTITIONED BY ( 
      `updatedatyear` string, 
      `updatedatmonth` string, 
      `updatedatday` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
LOCATION
  's3://aktana-bdp-pfizerus/prod/data/archive_rds/facility/'
    TBLPROPERTIES ('projection.updatedatyear.type'='integer','projection.enabled'='true','projection.updatedatyear.range'='2018,2022',
        'projection.updatedatmonth.type'='integer',
        'projection.updatedatmonth.range'='1,12',
        'projection.updatedatday.type'='integer',
        'projection.updatedatday.range'='1,31',
        'storage.location.template' = 's3://aktana-bdp-pfizerus/prod/data/archive_rds/facility/updatedatyear=${updatedatyear}/updatedatmonth=${updatedatmonth}/updatedatday=${updatedatday}/'    
    );

