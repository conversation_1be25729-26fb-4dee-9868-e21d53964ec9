name: 'pfi_salesdata_transform'
version: '0.1.0'
config-version: 2
require-dbt-version: [">=1.0.0", "<2.0.0"]

vars:
   # customer
   customer: 'pfizerus'

   # source environment
   src_env: 'prod'

# This setting configures which "profile" dbt uses for this project.
profile: 'kpi_reporting'

# FIXME: query-comment must be disabled for Athena to work because /* block comments are unsupported in Athena DML
# Removing this line will result in a Runtime Error during the integration test
#   `2 of 5 (2) create external table dbt.people_csv_partitioned ...`. The error is
#   "FAILED: ParseException line 1:0 cannot recognize input near '/' '*' '{".
# Is there a better way around this?
query-comment:

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]
