{{ config(materialized='table') }}

{%- set dpids = ["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24"]  -%}
{%- set cte_names = [] %}

  with union_cte as 
  (
  select DATE_TRUNC('month', DATE_PARSE(a.filedate, '%Y%m%d')) as filemonthdate, a.presc_num as presc_num, a.brandcode as brandcode
  from  pfizerus_prod_current.ext_sales_pfi_akt_trx_m_data a where a.filedate >= '********'
  union
  select DATE_TRUNC('month', DATE_PARSE(b.filedate, '%Y%m%d')) as filemonthdate, b.presc_num as presc_num, b.brandcode as brandcode
  from  pfizerus_prod_current.ext_sales_pfi_akt_nbrx_w_data b where b.filedate >= '********'
  ),
  combined_sales as
  ( 
  select u.filemonthdate, u.presc_num, u.brandcode, trxmonthback1, trxmonthback2, trxmonthback3, trxmonthback4, trxmonthback5, trxmonthback6, trxmonthback7, trxmonthback8,
  trxmonthback9, trxmonthback10, trxmonthback11, trxmonthback12, trxmonthback13, trxmonthback14, trxmonthback15, trxmonthback16,
  trxmonthback17, trxmonthback18, trxmonthback19, trxmonthback20, trxmonthback21, trxmonthback22, trxmonthback23, trxmonthback24,
  trxunitmonthback1, trxunitmonthback2, trxunitmonthback3, trxunitmonthback4, trxunitmonthback5, trxunitmonthback6, trxunitmonthback7, trxunitmonthback8,
  trxunitmonthback9, trxunitmonthback10, trxunitmonthback11, trxunitmonthback12, trxunitmonthback13, trxunitmonthback14, trxunitmonthback15, trxunitmonthback16,
  trxunitmonthback17, trxunitmonthback18, trxunitmonthback19, trxunitmonthback20, trxunitmonthback21, trxunitmonthback22, trxunitmonthback23, trxunitmonthback24,
  nrxmonthback1, nrxmonthback2, nrxmonthback3, nrxmonthback4, nrxmonthback5, nrxmonthback6, nrxmonthback7, nrxmonthback8,
  nrxmonthback9, nrxmonthback10, nrxmonthback11, nrxmonthback12, nrxmonthback13, nrxmonthback14, nrxmonthback15, nrxmonthback16,
  nrxmonthback17, nrxmonthback18, nrxmonthback19, nrxmonthback20, nrxmonthback21, nrxmonthback22, nrxmonthback23, nrxmonthback24
  from union_cte u
  left join pfizerus_prod_current.ext_sales_pfi_akt_trx_m_data a on a.filedate >= '********' and 
            u.filemonthdate = DATE_TRUNC('month', DATE_PARSE(a.filedate, '%Y%m%d')) and u.presc_num = a.presc_num and u.brandcode = a.brandcode
  left join pfizerus_prod_current.ext_sales_pfi_akt_nrx_m_data b on b.filedate >= '********' and
            u.filemonthdate = DATE_TRUNC('month', DATE_PARSE(b.filedate, '%Y%m%d')) and u.presc_num = b.presc_num and u.brandcode = b.brandcode 
  ),
{% for dpid in dpids %}
{%- set cte_name = 't_' ~ dpid %}
{% do cte_names.append(cte_name) %}
    {{ cte_name }} AS (
        SELECT date_format(filemonthdate, '%Y%m%d') as filedate, 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, presc_num as account_ref_id, brandcode as product_ref_id, date_trunc('month', date_add('month', -{{ dpid }}, filemonthdate))  as sale_date, 'monthly' as frequency, b.trxmonthback{{ dpid }} as sales_value_trx, b.trxunitmonthback{{ dpid }} as sales_value_unit, b.nrxmonthback{{ dpid }} as sales_value_nrx
            FROM combined_sales b
            where b.trxmonthback{{ dpid }} > 0.0 OR b.trxunitmonthback{{ dpid }} > 0.0 or b.nrxmonthback{{ dpid }} > 0.0
    ){% if not loop.last %},{% endif %}
{% endfor %}

{% for ct in cte_names %}
  select * from {{ ct }}
{% if not loop.last -%} union all {%- endif %}
{% endfor %}
