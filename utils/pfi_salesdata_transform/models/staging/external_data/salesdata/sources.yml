version: 2

sources:
  - name: pfirawsales  
    description: “Archived data from Loaders”
    database: awsdatacatalog
    schema: "{{ var('customer', 'default') }}_{{ var('src_env', 'prod') }}_current"
    tables:
        - name: ext_sales_pfi_akt_trx_m_data
          description: "raw monthly trx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1269_md_prod_trx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: trxmonthback1 
                 data_type: double
               - name: trxmonthback2
                 data_type: double
               - name: trxmonthback3
                 data_type: double
               - name: trxmonthback4
                 data_type: double
               - name: trxmonthback5
                 data_type: double
               - name: trxmonthback6
                 data_type: double
               - name: trxmonthback7
                 data_type: double
               - name: trxmonthback8
                 data_type: double
               - name: trxmonthback9
                 data_type: double
               - name: trxmonthback10
                 data_type: double
               - name: trxmonthback11
                 data_type: double
               - name: trxmonthback12
                 data_type: double
               - name: trxmonthback13
                 data_type: double
               - name: trxmonthback14
                 data_type: double
               - name: trxmonthback15
                 data_type: double
               - name: trxmonthback16
                 data_type: double
               - name: trxmonthback17
                 data_type: double
               - name: trxmonthback18
                 data_type: double
               - name: trxmonthback19
                 data_type: double
               - name: trxmonthback20
                 data_type: double
               - name: trxmonthback21
                 data_type: double
               - name: trxmonthback22
                 data_type: double
               - name: trxmonthback23
                 data_type: double
               - name: trxmonthback24
                 data_type: double
               - name: trxunitmonthback1 
                 data_type: double
               - name: trxunitmonthback2
                 data_type: double
               - name: trxunitmonthback3
                 data_type: double
               - name: trxunitmonthback4
                 data_type: double
               - name: trxunitmonthback5
                 data_type: double
               - name: trxunitmonthback6
                 data_type: double
               - name: trxunitmonthback7
                 data_type: double
               - name: trxunitmonthback8
                 data_type: double
               - name: trxunitmonthback9
                 data_type: double
               - name: trxunitmonthback10
                 data_type: double
               - name: trxunitmonthback11
                 data_type: double
               - name: trxunitmonthback12
                 data_type: double
               - name: trxunitmonthback13
                 data_type: double
               - name: trxunitmonthback14
                 data_type: double
               - name: trxunitmonthback15
                 data_type: double
               - name: trxunitmonthback16
                 data_type: double
               - name: trxunitmonthback17
                 data_type: double
               - name: trxunitmonthback18
                 data_type: double
               - name: trxunitmonthback19
                 data_type: double
               - name: trxunitmonthback20
                 data_type: double
               - name: trxunitmonthback21
                 data_type: double
               - name: trxunitmonthback22
                 data_type: double
               - name: trxunitmonthback23
                 data_type: double
               - name: trxunitmonthback24
                 data_type: double

        - name: ext_sales_pfi_akt_nrx_m_data
          description: "raw monthly nrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1269_md_prod_nrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: nrxmonthback1 
                 data_type: double
               - name: nrxmonthback2
                 data_type: double
               - name: nrxmonthback3
                 data_type: double
               - name: nrxmonthback4
                 data_type: double
               - name: nrxmonthback5
                 data_type: double
               - name: nrxmonthback6
                 data_type: double
               - name: nrxmonthback7
                 data_type: double
               - name: nrxmonthback8
                 data_type: double
               - name: nrxmonthback9
                 data_type: double
               - name: nrxmonthback10
                 data_type: double
               - name: nrxmonthback11
                 data_type: double
               - name: nrxmonthback12
                 data_type: double
               - name: nrxmonthback13
                 data_type: double
               - name: nrxmonthback14
                 data_type: double
               - name: nrxmonthback15
                 data_type: double
               - name: nrxmonthback16
                 data_type: double
               - name: nrxmonthback17
                 data_type: double
               - name: nrxmonthback18
                 data_type: double
               - name: nrxmonthback19
                 data_type: double
               - name: nrxmonthback20
                 data_type: double
               - name: nrxmonthback21
                 data_type: double
               - name: nrxmonthback22
                 data_type: double
               - name: nrxmonthback23
                 data_type: double
               - name: nrxmonthback24
                 data_type: double
               - name: nrxunitmonthback1 
                 data_type: double
               - name: nrxunitmonthback2
                 data_type: double
               - name: nrxunitmonthback3
                 data_type: double
               - name: nrxunitmonthback4
                 data_type: double
               - name: nrxunitmonthback5
                 data_type: double
               - name: nrxunitmonthback6
                 data_type: double
               - name: nrxunitmonthback7
                 data_type: double
               - name: nrxunitmonthback8
                 data_type: double
               - name: nrxunitmonthback9
                 data_type: double
               - name: nrxunitmonthback10
                 data_type: double
               - name: nrxunitmonthback11
                 data_type: double
               - name: nrxunitmonthback12
                 data_type: double
               - name: nrxunitmonthback13
                 data_type: double
               - name: nrxunitmonthback14
                 data_type: double
               - name: nrxunitmonthback15
                 data_type: double
               - name: nrxunitmonthback16
                 data_type: double
               - name: nrxunitmonthback17
                 data_type: double
               - name: nrxunitmonthback18
                 data_type: double
               - name: nrxunitmonthback19
                 data_type: double
               - name: nrxunitmonthback20
                 data_type: double
               - name: nrxunitmonthback21
                 data_type: double
               - name: nrxunitmonthback22
                 data_type: double
               - name: nrxunitmonthback23
                 data_type: double
               - name: nrxunitmonthback24
                 data_type: double

        - name: ext_sales_pfi_akt_trx_w_data
          description: "raw weekly trx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_trx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: trxweekback1 
                 data_type: double
               - name: trxweekback2
                 data_type: double
               - name: trxweekback3
                 data_type: double
               - name: trxweekback4
                 data_type: double
               - name: trxweekback5
                 data_type: double
               - name: trxweekback6
                 data_type: double
               - name: trxweekback7
                 data_type: double
               - name: trxweekback8
                 data_type: double
               - name: trxweekback9
                 data_type: double
               - name: trxweekback10
                 data_type: double
               - name: trxweekback11
                 data_type: double
               - name: trxweekback12
                 data_type: double
               - name: trxweekback13
                 data_type: double
               - name: trxweekback14
                 data_type: double
               - name: trxweekback15
                 data_type: double
               - name: trxweekback16
                 data_type: double
               - name: trxweekback17
                 data_type: double
               - name: trxweekback18
                 data_type: double
               - name: trxweekback19
                 data_type: double
               - name: trxweekback20
                 data_type: double
               - name: trxweekback21
                 data_type: double
               - name: trxweekback22
                 data_type: double
               - name: trxweekback23
                 data_type: double
               - name: trxweekback24               
                 data_type: double
               - name: trxweekback25               
                 data_type: double
               - name: trxweekback26               
                 data_type: double
               - name: trxunitweekback1 
                 data_type: double
               - name: trxunitweekback2
                 data_type: double
               - name: trxunitweekback3
                 data_type: double
               - name: trxunitweekback4
                 data_type: double
               - name: trxunitweekback5
                 data_type: double
               - name: trxunitweekback6
                 data_type: double
               - name: trxunitweekback7
                 data_type: double
               - name: trxunitweekback8
                 data_type: double
               - name: trxunitweekback9
                 data_type: double
               - name: trxunitweekback10
                 data_type: double
               - name: trxunitweekback11
                 data_type: double
               - name: trxunitweekback12
                 data_type: double
               - name: trxunitweekback13
                 data_type: double
               - name: trxunitweekback14
                 data_type: double
               - name: trxunitweekback15
                 data_type: double
               - name: trxunitweekback16
                 data_type: double
               - name: trxunitweekback17
                 data_type: double
               - name: trxunitweekback18
                 data_type: double
               - name: trxunitweekback19
                 data_type: double
               - name: trxunitweekback20
                 data_type: double
               - name: trxunitweekback21
                 data_type: double
               - name: trxunitweekback22
                 data_type: double
               - name: trxunitweekback23
                 data_type: double
               - name: trxunitweekback24
                 data_type: double
               - name: trxunitweekback25
                 data_type: double
               - name: trxunitweekback26
                 data_type: double

        - name: ext_sales_pfi_akt_nrx_w_data
          description: "raw weekly nrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_nrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: brandname 
                 data_type: varchar(50)
               - name: nrxweekback1 
                 data_type: double
               - name: nrxweekback2
                 data_type: double
               - name: nrxweekback3
                 data_type: double
               - name: nrxweekback4
                 data_type: double
               - name: nrxweekback5
                 data_type: double
               - name: nrxweekback6
                 data_type: double
               - name: nrxweekback7
                 data_type: double
               - name: nrxweekback8
                 data_type: double
               - name: nrxweekback9
                 data_type: double
               - name: nrxweekback10
                 data_type: double
               - name: nrxweekback11
                 data_type: double
               - name: nrxweekback12
                 data_type: double
               - name: nrxweekback13
                 data_type: double
               - name: nrxweekback14
                 data_type: double
               - name: nrxweekback15
                 data_type: double
               - name: nrxweekback16
                 data_type: double
               - name: nrxweekback17
                 data_type: double
               - name: nrxweekback18
                 data_type: double
               - name: nrxweekback19
                 data_type: double
               - name: nrxweekback20
                 data_type: double
               - name: nrxweekback21
                 data_type: double
               - name: nrxweekback22
                 data_type: double
               - name: nrxweekback23
                 data_type: double
               - name: nrxweekback24               
                 data_type: double
               - name: nrxweekback25               
                 data_type: double
               - name: nrxweekback26               
                 data_type: double
               - name: nrxunitweekback1 
                 data_type: double
               - name: nrxunitweekback2
                 data_type: double
               - name: nrxunitweekback3
                 data_type: double
               - name: nrxunitweekback4
                 data_type: double
               - name: nrxunitweekback5
                 data_type: double
               - name: nrxunitweekback6
                 data_type: double
               - name: nrxunitweekback7
                 data_type: double
               - name: nrxunitweekback8
                 data_type: double
               - name: nrxunitweekback9
                 data_type: double
               - name: nrxunitweekback10
                 data_type: double
               - name: nrxunitweekback11
                 data_type: double
               - name: nrxunitweekback12
                 data_type: double
               - name: nrxunitweekback13
                 data_type: double
               - name: nrxunitweekback14
                 data_type: double
               - name: nrxunitweekback15
                 data_type: double
               - name: nrxunitweekback16
                 data_type: double
               - name: nrxunitweekback17
                 data_type: double
               - name: nrxunitweekback18
                 data_type: double
               - name: nrxunitweekback19
                 data_type: double
               - name: nrxunitweekback20
                 data_type: double
               - name: nrxunitweekback21
                 data_type: double
               - name: nrxunitweekback22
                 data_type: double
               - name: nrxunitweekback23
                 data_type: double
               - name: nrxunitweekback24
                 data_type: double
               - name: nrxunitweekback25
                 data_type: double
               - name: nrxunitweekback26
                 data_type: double

        - name: ext_sales_pfi_akt_nbrx_w_data
          description: "raw weekly nbrx sales data for pfizerus"
          external:
            location: "s3://aktana-externalfiles-{{var('customer', 'default')}}/{{ var('src_env', 'prod') }}/repository/sales/rx1270_md_prod_nbrx/"
            row_format: "SERDE 'org.apache.hadoop.hive.serde2.OpenCSVSerde'" 
            table_properties: ('skip.header.line.count'='0', 'projection.filedate.type'='date','projection.enabled'='true','projection.filedate.range'='20220101,NOW','projection.filedate.format'='yyyyMMdd','projection.filedate.interval'='1','projection.filedate.interval.unit'='DAYS')
            hive_compatible_partitions: true
            projection_partitions: true
            partitions:
               - name: filedate
                 data_type: string
          columns:
               - name: presc_num 
                 data_type: varchar(20)
               - name: brandcode 
                 data_type: varchar(20)
               - name: nbrxweekback1 
                 data_type: double
               - name: nbrxweekback2
                 data_type: double
               - name: nbrxweekback3
                 data_type: double
               - name: nbrxweekback4
                 data_type: double
               - name: nbrxweekback5
                 data_type: double
               - name: nbrxweekback6
                 data_type: double
               - name: nbrxweekback7
                 data_type: double
               - name: nbrxweekback8
                 data_type: double
               - name: nbrxweekback9
                 data_type: double
               - name: nbrxweekback10
                 data_type: double
               - name: nbrxweekback11
                 data_type: double
               - name: nbrxweekback12
                 data_type: double
               - name: nbrxweekback13
                 data_type: double
               - name: nbrxweekback14
                 data_type: double
               - name: nbrxweekback15
                 data_type: double
               - name: nbrxweekback16
                 data_type: double
               - name: nbrxweekback17
                 data_type: double
               - name: nbrxweekback18
                 data_type: double
               - name: nbrxweekback19
                 data_type: double
               - name: nbrxweekback20
                 data_type: double
               - name: nbrxweekback21
                 data_type: double
               - name: nbrxweekback22
                 data_type: double
               - name: nbrxweekback23
                 data_type: double
               - name: nbrxweekback24               
                 data_type: double
               - name: nbrxweekback25               
                 data_type: double
               - name: nbrxweekback26               
                 data_type: double
