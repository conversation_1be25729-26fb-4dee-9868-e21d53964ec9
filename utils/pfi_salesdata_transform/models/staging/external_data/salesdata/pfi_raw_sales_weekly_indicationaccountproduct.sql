{{ config(materialized='table') }}

{%- set dpids = ["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26"]  -%}
{%- set cte_names = [] %}

  with union_cte as 
  (
  select DATE_TRUNC('week', DATE_PARSE(a.filedate, '%Y%m%d')) as fileweekdate, a.presc_num as presc_num, a.brandcode as brandcode
  from  pfizerus_prod_current.ext_sales_pfi_akt_trx_w_data a where a.filedate >= '********'
  union
  select DATE_TRUNC('week', DATE_PARSE(b.filedate, '%Y%m%d')) as fileweekdate, b.presc_num as presc_num, b.brandcode as brandcode
  from  pfizerus_prod_current.ext_sales_pfi_akt_nbrx_w_data b where b.filedate >= '********'
  ),
  combined_sales as
  ( 
  select u.fileweekdate, u.presc_num, u.brandcode, trxweekback1, trxweekback2, trxweekback3, trxweekback4, trxweekback5, trxweekback6, trxweekback7, trxweekback8,
  trxweekback9, trxweekback10, trxweekback11, trxweekback12, trxweekback13, trxweekback14, trxweekback15, trxweekback16,
  trxweekback17, trxweekback18, trxweekback19, trxweekback20, trxweekback21, trxweekback22, trxweekback23, trxweekback24,
  trxweekback25, trxweekback26,
  trxunitweekback1, trxunitweekback2, trxunitweekback3, trxunitweekback4, trxunitweekback5, trxunitweekback6, trxunitweekback7, trxunitweekback8,
  trxunitweekback9, trxunitweekback10, trxunitweekback11, trxunitweekback12, trxunitweekback13, trxunitweekback14, trxunitweekback15, trxunitweekback16,
  trxunitweekback17, trxunitweekback18, trxunitweekback19, trxunitweekback20, trxunitweekback21, trxunitweekback22, trxunitweekback23, trxunitweekback24,
  trxunitweekback25, trxunitweekback26,
  nbrxweekback1, nbrxweekback2, nbrxweekback3, nbrxweekback4, nbrxweekback5, nbrxweekback6, nbrxweekback7, nbrxweekback8,
  nbrxweekback9, nbrxweekback10, nbrxweekback11, nbrxweekback12, nbrxweekback13, nbrxweekback14, nbrxweekback15, nbrxweekback16,
  nbrxweekback17, nbrxweekback18, nbrxweekback19, nbrxweekback20, nbrxweekback21, nbrxweekback22, nbrxweekback23, nbrxweekback24,
  nbrxweekback25, nbrxweekback26
  from union_cte u
  left join pfizerus_prod_current.ext_sales_pfi_akt_trx_w_data a on a.filedate >= '********' and 
            u.fileweekdate = DATE_TRUNC('week', DATE_PARSE(a.filedate, '%Y%m%d')) and u.presc_num = a.presc_num and u.brandcode = a.brandcode
  left join pfizerus_prod_current.ext_sales_pfi_akt_nbrx_w_data b on b.filedate >= '********' and
            u.fileweekdate = DATE_TRUNC('week', DATE_PARSE(b.filedate, '%Y%m%d')) and u.presc_num = b.presc_num and u.brandcode = b.brandcode 
  ),
{% for dpid in dpids %}
{%- set cte_name = 't_' ~ dpid %}
{% do cte_names.append(cte_name) %}
    {{ cte_name }} AS (
        SELECT date_format(fileweekdate, '%Y%m%d') as filedate, 'Default' as indication, 'Default' as diagnosis_group, 'Default' as product_group, presc_num as account_ref_id, brandcode as product_ref_id, date_trunc('week', date_add('week', -{{ dpid }}, fileweekdate))  as sale_date, 'weekly' as frequency, b.trxweekback{{ dpid }} as sales_value_trx, b.trxunitweekback{{ dpid }} as sales_value_unit, b.nbrxweekback{{ dpid }} as sales_value_nbrx
            FROM combined_sales b
            where b.trxweekback{{ dpid }} > 0.0 OR b.trxunitweekback{{ dpid }} > 0.0 or b.nbrxweekback{{ dpid }} > 0.0
    ){% if not loop.last %},{% endif %}
{% endfor %}

{% for ct in cte_names %}
  select * from {{ ct }}
{% if not loop.last -%} union all {%- endif %}
{% endfor %}
