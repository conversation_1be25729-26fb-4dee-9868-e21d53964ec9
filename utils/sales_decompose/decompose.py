import logging.config
import time
from urllib.parse import quote_plus  # PY2: from urllib import quote_plus
#from pyathena import connect
import pandas as pd
import awswrangler as wr
import getopt, sys, os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from fbprophet import Prophet
from fbprophet.plot import add_changepoints_to_plot

df = wr.s3.read_parquet(path="s3://aktana-bdp-genzymeus/prod/dbt/impact/download/sales_weekly/")

gData = df.groupby(['indication', 'accountuid', 'productuid', 'sale_date'])['sales_value_trx'].sum()
gDim = gData.groupby(['indication', 'accountuid', 'productuid']).count();
gDim = gDim.reset_index()
gDim_filtered = gDim.query('sales_value_trx > 60')
gData = gData.reset_index();

dirname = "charts2/"
samplingfreq = "W"
m = Prophet()
fcst_all = pd.DataFrame()  # store all forecasts here
delta_all = pd.DataFrame()
for index, row in gDim_filtered.head(100).iterrows():
    print(row['indication'], row['accountuid'], row['productuid'])
    temp = gData.query('indication == ' + "'" + row['indication'] + "' and " + 'accountuid == ' + "'" + row['accountuid'] + "' and " + 'productuid == ' + "'" + row['productuid'] + "'")
    temp = temp.drop(columns=[ 'indication', 'accountuid', 'productuid' ])
    temp['sale_date'] = pd.to_datetime(temp['sale_date'])
    temp = temp.set_index('sale_date')
    d_df = temp.resample(samplingfreq).sum()
    d_df = d_df.reset_index().dropna()
    d_df.columns = ['ds', 'y']
    try:
        p = Prophet(seasonality_mode='multiplicative',yearly_seasonality=False,weekly_seasonality=False,interval_width=0.95,changepoint_range=0.95);
        #p.add_seasonality(name='monthly', period=30.5, fourier_order=5)        
        m = p.fit(d_df)
        future = m.make_future_dataframe(periods=12, freq=samplingfreq)
    except ValueError:
        pass
    fcst = m.predict(future)     
    fprefix = dirname + "plot_" + row['indication'] + "_" + row['accountuid'] + "_" + row['productuid']        
    #fig1 = m.plot(fcst)    
    fig2 = m.plot_components(fcst)    
    fig3 = m.plot(fcst)    
    change_points = add_changepoints_to_plot(fig3.gca(), m, fcst)    

    delta_series = pd.Series(m.params['delta'].squeeze())
    chgpoint_series = m.changepoints

    deltadf = pd.DataFrame({'delta':delta_series, 'changepoint':chgpoint_series});
    deltadf['indication'] = row['indication']
    deltadf['accountuid'] = row['accountuid']
    deltadf['productuid'] = row['productuid']

    tempdeltadf = deltadf.query('abs(delta) > 0.001')    
    delta_all = pd.concat([delta_all, tempdeltadf])

    #fig1.savefig(fprefix + "_fcst.png")
    #fig2.savefig(fprefix + "_comp.png")
    if (tempdeltadf.shape[0] > 0):
        fig2.savefig(fprefix + "_comp.png")
        fig3.savefig(fprefix + "_chg.png")
        deltas = m.params['delta'].mean(0)
        fig4 = plt.figure(facecolor='w')
        ax = fig4.add_subplot(111)
        ax.bar(range(len(deltas)), deltas)
        ax.grid(True, which='major', c='gray', ls='-', lw=1, alpha=0.2)
        ax.set_ylabel('Rate change')
        ax.set_xlabel('Potential changepoint')
        fig4.tight_layout()
        fig4.savefig(fprefix + "_rate.png")      

    fcst['indication'] = row['indication']
    fcst['accountuid'] = row['accountuid']
    fcst['productuid'] = row['productuid']
    fcst['Fact'] = d_df['y'].reset_index(drop = True)    
    fcst_all = pd.concat((fcst_all, fcst))

fcst_all.to_excel(dirname + 'ProphetFcst.xlsx', index=False)    
delta_all.to_excel(dirname + 'ProphetDelta.xlsx', index=False)    