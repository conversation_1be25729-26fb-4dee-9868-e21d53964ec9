{{ config(materialized='table') }}

{% set customers = ["pfizerus", "almirallus", "axsomeus", "antaresus",  "biogenna", "emdseronous", "genentechca", "genzymeus", "sanofius", "leous", "novartisbr", "novartisca", "novartisus" ] %}
with
{% for c in customers %}
{{c}}_facility_ranked as (
  SELECT
    facilityId, externalid facilityuid, latitude, longitude, geolocationstring, updatedatyear, updatedatmonth, updatedatday, 
    ROW_NUMBER() OVER(PARTITION BY externalid ORDER BY cast(updatedatyear as int) DESC, cast(updatedatmonth as int) DESC, cast(updatedatday as int) DESC ) AS row_number
  FROM impact_pfizerus_satya.{{c}}_facility
)
{% if not loop.last %},{% endif %}
{% endfor %}
{% for c in customers %}
SELECT '{{c}}' as customer, 
 facilityId, facilityuid, latitude, longitude, geolocationstring, updatedatyear, updatedatmonth, updatedatday
 from {{c}}_facility_ranked where row_number = 1
{% if not loop.last %} UNION {% endif %}
{% endfor %}
