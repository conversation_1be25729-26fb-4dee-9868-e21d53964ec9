{{ config(materialized='table') }}

select customer, ownerid, call_channel_vod__c, call_type_vod__c, date_trunc('year', call_date_vod__c) year,
      ST_AsText(ST_Centroid(ST_MultiPoint(array_agg(ST_GeometryFromText(daily_centroid) order by call_date_vod__c)))) yearly_centroid
from {{ ref('xc_daily_centroid') }} 
      group by customer, ownerid, call_channel_vod__c, call_type_vod__c, date_trunc('year', call_date_vod__c)
