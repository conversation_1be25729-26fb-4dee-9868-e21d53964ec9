{{ config(materialized='table') }}

{% set customers = ["pfizerus", "almirallus", "axsomeus", "antaresus", "biogenna", "emdseronous", "genentechca", "genzymeus", "sanofius", "leous", "novartisbr", "novartisca", "novartisus" ] %}
with
{% for c in customers %}
{{c}}_call2_vod__c_ranked as (
  SELECT
    id, ownerid, account_vod__c, parent_address_vod__c, call_date_vod__c, status_vod__c, call_channel_vod__c, call_type_vod__c, 
call_datetime_vod__c,  sysmodstampyear, sysmodstampmonth, sysmodstampday,
    ROW_NUMBER() OVER(PARTITION BY id ORDER BY cast(sysmodstampyear as int) DESC, cast(sysmodstampmonth as int) DESC, cast(sysmodstampday as int) DESC ) AS row_number
  FROM impact_pfizerus_satya.{{c}}_call2_vod__c where status_vod__c = 'Submitted_vod'
)
{% if not loop.last %},{% endif %}
{% endfor %}
{% for c in customers %}
SELECT '{{c}}' as customer, id, ownerid, account_vod__c, parent_address_vod__c, call_date_vod__c, status_vod__c, call_channel_vod__c, call_type_vod__c, 
call_datetime_vod__c,  sysmodstampyear, sysmodstampmonth, sysmodstampday
from  {{c}}_call2_vod__c_ranked 
where row_number = 1
{% if not loop.last %} UNION {% endif %}
{% endfor %}
