{{ config(materialized='table') }}

select c.customer, c.ownerid, call_channel_vod__c, call_type_vod__c, c.call_date_vod__c, 
ST_AsText(to_geometry(to_spherical_geography(ST_Centroid(ST_MultiPoint(array_agg(to_geometry(to_spherical_geography(ST_Point(f.longitude, f.latitude))) order by c.call_datetime_vod__c)))))) daily_centroid  
from 
xc_call2_vod__c c inner join xc_facility f
on c.customer = f.customer and
      c.parent_address_vod__c = f.facilityuid
      where f.latitude is not null and f.longitude is not null
      group by c.customer, c.ownerid, call_channel_vod__c, call_type_vod__c, c.call_date_vod__c
      order by c.customer, c.ownerid, call_channel_vod__c, call_type_vod__c, c.call_date_vod__c
