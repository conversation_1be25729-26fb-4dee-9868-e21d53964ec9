
#CUSTOMER="argenxus"
#ENVIRONMENT="prod"
BUCKET="aktana-bdp-$CUSTOMER"
PREFIX="$ENVIRONMENT/data/archive_rds"

non1000tables=/tmp/${CUSTOMER}.${ENVIRONMENT}.non1000tables
year1000tables=/tmp/${CUSTOMER}.${ENVIRONMENT}.year1000tables

aws s3api list-objects --bucket $BUCKET --prefix $PREFIX --query 'Contents[].Key' | jq -rc '.[]' | grep "year=1000" | grep ".parquet" | awk -F/ '{print $4}' | uniq | sort > $year1000tables
aws s3api list-objects --bucket $BUCKET --prefix $PREFIX --query 'Contents[].Key' | jq -rc '.[]' | grep "year=20" | grep ".parquet" | awk -F/ '{print $4}' | uniq | sort > $non1000tables
echo "Tables with year=1000 files to cleanup since year=20* exists:"
echo "-------------------------------------------------------------"
comm -12 $year1000tables $non1000tables |
while read common_file ; do
    echo "aws s3 rm s3://$BUCKET/$PREFIX/$common_file/sysmodstampyear=1000 --recursive"
    echo "aws s3 rm s3://$BUCKET/$PREFIX/$common_file/updatedatyear=1000 --recursive"
    echo "aws s3 rm s3://$BUCKET/$PREFIX/$common_file/lastmodifieddateyear=1000 --recursive"
    # or combined
    # echo "aws s3 rm s3://$BUCKET/$PREFIX/$common_file/ --recursive --exclude '*' --include '*year=1000*'"
done

echo "Tables with year=1000 files to drop from athena:"
echo "------------------------------------------------"
comm -12 $year1000tables $non1000tables |
while read common_file ; do
    echo "drop table $common_file"
done

echo "Tables with year 1000 files only:"
echo "---------------------------------"
comm -23 $year1000tables $non1000tables

echo "Tables with year=20* files only:"
echo "---------------------------------"
comm -13 $year1000tables $non1000tables

rm $year1000tables $non1000tables
