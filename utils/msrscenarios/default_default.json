{"uid": "default", "name": "<PERSON><PERSON><PERSON>", "type": "pilot", "description": "Description of default scenario", "period_definitions": [{"uid": "Analysis Period", "name": "Analysis Period", "rolling_duration_in_months": 24, "rolling_offset_in_months": 0}, {"uid": "Conversion Period", "name": "Conversion Period", "rolling_duration_in_months": 24, "rolling_offset_in_months": 3}], "repaccountassignment_begin_duration_in_months": 2, "repaccountassignment_asof_offset_in_months": -1, "repaccountassignment_begindate": null, "repaccountassignment_asofdate": null, "analyze_driven_events": true, "analyze_aligned_events": true, "factorusecasename_map_override": [], "remove_same_state_transitions": true, "pilot_rep_predicate": "usr.is_aktana_user", "control_rep_predicate": " NOT usr.is_aktana_user", "excluded_rep_predicate": "usr.is_aktana_user and (coalesce(usr.suggestion_avg_monthly_count, 0) < 5 OR ((coalesce(usr.pilot_call_activity_avg_monthly_count, 0) + coalesce(usr.pilot_email_activity_avg_monthly_count,0)) < 5) ) ", "accountproduct_segment_rpt": "hcpsegment_std_akt", "accountproduct_tier_rpt": "hcptier_std_akt", "account_dim1_rpt": "specialties_std_akt", "account_dim1_rpt_label": "Specialty", "account_dim2_rpt": "department_std_akt", "account_dim2_rpt_label": "Department", "accountproduct_dim1_rpt": "decile_std_akt", "accountproduct_dim1_rpt_label": "Decile", "accountproduct_dim2_rpt": "hcppriority_std_akt", "accountproduct_dim2_rpt_label": "HCP Priority", "repaccount_istarget_rpt": " isMyTarget_std_akt ", "sales_filedate": "********", "sales_interaction_offset_months": 0, "sales_filedate_offset_weeks": 0, "golive_date": "2021-01-01", "call_type": "call_type_vod__c", "call_channel_raw": "call_channel_vod__c", "call_channel_category": "case when call_channel_vod__c in ('Email_vod','Phone_vod','Video_vod','Message_vod') then 'Remote' when call_channel_vod__c in ('Face_to_face_vod') then 'Visit' else 'Other' end", "call_channel_dse": "case when call_channel_vod__c in ('Email_vod','Phone_vod','Video_vod','Message_vod') then 'WEB_INTERACTIVE_CHANNEL' when call_channel_vod__c in ('Face_to_face_vod') then 'VISIT_CHANNEL' else 'VISIT_CHANNEL' end"}